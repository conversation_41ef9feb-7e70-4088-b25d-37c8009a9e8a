import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/',
  }),
}));

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';

// Global test utilities
global.console = {
  ...console,
  // Suppress console.log in tests unless explicitly needed
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock fetch for API tests
global.fetch = jest.fn();

// Mock Date.now for consistent testing
const mockDate = new Date('2023-01-01T00:00:00.000Z');
jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
Date.now = jest.fn(() => mockDate.getTime());

// Setup for React Testing Library
import { configure } from '@testing-library/react';

configure({
  testIdAttribute: 'data-testid',
});

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock window.scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
  localStorageMock.clear();
  sessionStorageMock.clear();
});

// Global test helpers
export const createMockAsset = (overrides = {}) => ({
  id: 'test-asset-id',
  name: 'Test Asset',
  category: 'Equipment',
  location: 'Office',
  department: 'IT',
  purchaseDate: new Date('2020-01-01'),
  purchasePrice: 10000,
  status: 'active',
  serialNumber: 'TEST001',
  assetImages: [],
  assetTypeId: 'test-asset-type-id',
  createdAt: new Date('2020-01-01'),
  updatedAt: new Date('2020-01-01'),
  ...overrides,
});

export const createMockAssetType = (overrides = {}) => ({
  id: 'test-asset-type-id',
  name: 'Computer',
  code: 'COMP',
  description: 'Computer equipment',
  categoryId: 'test-category-id',
  subcategory: 'Desktop',
  icon: 'computer',
  color: '#000000',
  isActive: true,
  tags: ['technology'],
  createdAt: new Date('2020-01-01'),
  updatedAt: new Date('2020-01-01'),
  createdBy: 'test-user-id',
  version: 1,
  ...overrides,
});

export const createMockDepreciationSettings = (overrides = {}) => ({
  id: 'test-settings-id',
  assetTypeId: 'test-asset-type-id',
  method: 'straight_line' as const,
  usefulLife: 5,
  usefulLifeUnit: 'years' as const,
  salvageValue: 1000,
  salvageValueType: 'fixed' as const,
  startDate: new Date('2020-01-01'),
  customRates: null,
  acceleratedDepreciation: null,
  impairmentSettings: null,
  isActive: true,
  createdAt: new Date('2020-01-01'),
  updatedAt: new Date('2020-01-01'),
  ...overrides,
});

export const createMockDepreciationSchedule = (overrides = {}) => ({
  id: 'test-schedule-id',
  assetId: 'test-asset-id',
  year: 2020,
  month: 1,
  depreciationAmount: 150,
  accumulatedDepreciation: 150,
  bookValue: 9850,
  method: 'straight_line',
  calculatedAt: new Date('2020-01-01'),
  isActual: false,
  notes: null,
  ...overrides,
});

export const createMockRequest = (url: string, options: RequestInit = {}) => {
  return new Request(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    ...options,
  });
};

export const createMockNextRequest = (url: string, options: RequestInit = {}) => {
  return new Request(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    ...options,
  }) as any; // Cast to satisfy NextRequest type
};

// Test database helpers
export const resetTestDatabase = async () => {
  // This would reset your test database
  // Implementation depends on your test database setup
  console.log('Resetting test database...');
};

export const seedTestData = async () => {
  // This would seed your test database with initial data
  // Implementation depends on your test database setup
  console.log('Seeding test data...');
};
