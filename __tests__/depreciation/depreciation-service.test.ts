import { DepreciationService } from '@/lib/services/depreciation-service';
import { DepreciationEngine } from '@/lib/engines/depreciation-engine';
import prisma from '@/lib/prisma';
import {
  AssetNotFoundError,
  DepreciationSettingsNotFoundError,
  ValidationError,
} from '@/lib/utils/depreciation-error-handler';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  asset: {
    count: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
  },
  depreciationSchedule: {
    findMany: jest.fn(),
    findFirst: jest.fn(),
  },
}));

jest.mock('@/lib/engines/depreciation-engine');

describe('DepreciationService', () => {
  const mockAssetId = 'test-asset-id';
  const mockAssetTypeId = 'test-asset-type-id';

  const mockAsset = {
    id: mockAssetId,
    name: 'Test Asset',
    category: 'Equipment',
    location: 'Office',
    department: 'IT',
    purchaseDate: new Date('2020-01-01'),
    purchasePrice: 10000,
    status: 'active',
    assetTypeId: mockAssetTypeId,
    assetType: {
      id: mockAssetTypeId,
      name: 'Computer',
      depreciationSettings: {
        id: 'settings-id',
        assetTypeId: mockAssetTypeId,
        method: 'straight_line',
        usefulLife: 5,
        usefulLifeUnit: 'years',
        salvageValue: 1000,
        salvageValueType: 'fixed',
        startDate: new Date('2020-01-01'),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    },
    depreciationSchedule: [],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getAssetsWithDepreciation', () => {
    it('should return assets with depreciation data', async () => {
      const mockAssets = [mockAsset];
      
      (prisma.asset.count as jest.Mock).mockResolvedValue(1);
      (prisma.asset.findMany as jest.Mock).mockResolvedValue(mockAssets);

      const result = await DepreciationService.getAssetsWithDepreciation(
        {},
        { page: 1, limit: 10 }
      );

      expect(result.assets).toHaveLength(1);
      expect(result.total).toBe(1);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
    });

    it('should apply filters correctly', async () => {
      const filter = {
        categories: ['Equipment'],
        includeDisposed: false,
        includeFullyDepreciated: true,
      };

      (prisma.asset.count as jest.Mock).mockResolvedValue(0);
      (prisma.asset.findMany as jest.Mock).mockResolvedValue([]);

      await DepreciationService.getAssetsWithDepreciation(filter, { page: 1, limit: 10 });

      expect(prisma.asset.count).toHaveBeenCalledWith({
        where: expect.objectContaining({
          category: { in: ['Equipment'] },
          status: { not: 'disposed' },
        }),
      });
    });
  });

  describe('getAssetDepreciationSummary', () => {
    it('should return depreciation summary for an asset', async () => {
      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(mockAsset);

      const summary = await DepreciationService.getAssetDepreciationSummary(mockAssetId);

      expect(summary).toBeDefined();
      expect(summary?.assetId).toBe(mockAssetId);
      expect(summary?.assetName).toBe('Test Asset');
      expect(summary?.purchasePrice).toBe(10000);
      expect(summary?.depreciationMethod).toBe('straight_line');
    });

    it('should return null for asset without depreciation settings', async () => {
      const assetWithoutSettings = {
        ...mockAsset,
        assetType: {
          ...mockAsset.assetType,
          depreciationSettings: null,
        },
      };

      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(assetWithoutSettings);

      const summary = await DepreciationService.getAssetDepreciationSummary(mockAssetId);

      expect(summary).toBeNull();
    });

    it('should return null for non-existent asset', async () => {
      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(null);

      const summary = await DepreciationService.getAssetDepreciationSummary(mockAssetId);

      expect(summary).toBeNull();
    });
  });

  describe('calculateAssetDepreciation', () => {
    it('should calculate depreciation for an asset', async () => {
      const mockCalculationResult = {
        totalDepreciation: 3600,
        remainingValue: 6400,
        schedule: [
          {
            year: 2020,
            month: 1,
            depreciationAmount: 150,
            accumulatedDepreciation: 150,
            bookValue: 9850,
            method: 'straight_line',
            isActual: false,
          },
        ],
        metadata: {
          method: 'straight_line' as const,
          usefulLife: 5,
          salvageValue: 1000,
          calculatedAt: new Date(),
        },
      };

      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(mockAsset);
      (DepreciationEngine.calculateDepreciationFromPrisma as jest.Mock).mockResolvedValue(mockCalculationResult);
      (DepreciationEngine.saveDepreciationSchedule as jest.Mock).mockResolvedValue(undefined);
      (prisma.depreciationSchedule.findFirst as jest.Mock).mockResolvedValue(null);

      const result = await DepreciationService.calculateAssetDepreciation(mockAssetId, false);

      expect(result).toEqual(mockCalculationResult);
      expect(DepreciationEngine.calculateDepreciationFromPrisma).toHaveBeenCalledWith({
        assetId: mockAssetId,
        purchasePrice: 10000,
        purchaseDate: mockAsset.purchaseDate,
        settings: mockAsset.assetType.depreciationSettings,
      });
    });

    it('should throw AssetNotFoundError for non-existent asset', async () => {
      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(null);

      await expect(
        DepreciationService.calculateAssetDepreciation(mockAssetId)
      ).rejects.toThrow(AssetNotFoundError);
    });

    it('should throw DepreciationSettingsNotFoundError for asset without settings', async () => {
      const assetWithoutSettings = {
        ...mockAsset,
        assetType: {
          ...mockAsset.assetType,
          depreciationSettings: null,
        },
      };

      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(assetWithoutSettings);

      await expect(
        DepreciationService.calculateAssetDepreciation(mockAssetId)
      ).rejects.toThrow(DepreciationSettingsNotFoundError);
    });

    it('should throw ValidationError for invalid asset ID', async () => {
      await expect(
        DepreciationService.calculateAssetDepreciation('')
      ).rejects.toThrow(ValidationError);
    });

    it('should recalculate when requested', async () => {
      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(mockAsset);
      (DepreciationEngine.calculateDepreciationFromPrisma as jest.Mock).mockResolvedValue({
        totalDepreciation: 3600,
        remainingValue: 6400,
        schedule: [],
        metadata: {
          method: 'straight_line' as const,
          usefulLife: 5,
          salvageValue: 1000,
          calculatedAt: new Date(),
        },
      });
      (DepreciationEngine.saveDepreciationSchedule as jest.Mock).mockResolvedValue(undefined);

      await DepreciationService.calculateAssetDepreciation(mockAssetId, true);

      expect(DepreciationEngine.saveDepreciationSchedule).toHaveBeenCalled();
    });
  });

  describe('bulkCalculateDepreciation', () => {
    it('should calculate depreciation for multiple assets', async () => {
      const assetIds = [mockAssetId, 'asset-2'];
      const mockResult = {
        totalDepreciation: 3600,
        remainingValue: 6400,
        schedule: [],
        metadata: {
          method: 'straight_line' as const,
          usefulLife: 5,
          salvageValue: 1000,
          calculatedAt: new Date(),
        },
      };

      // Mock successful calculation for first asset
      (prisma.asset.findUnique as jest.Mock)
        .mockResolvedValueOnce(mockAsset)
        .mockResolvedValueOnce(null); // Second asset not found

      (DepreciationEngine.calculateDepreciationFromPrisma as jest.Mock).mockResolvedValue(mockResult);
      (DepreciationEngine.saveDepreciationSchedule as jest.Mock).mockResolvedValue(undefined);
      (prisma.depreciationSchedule.findFirst as jest.Mock).mockResolvedValue(null);

      const result = await DepreciationService.bulkCalculateDepreciation({
        assetIds,
        recalculate: false,
      });

      expect(result.summary.total).toBe(2);
      expect(result.summary.successful).toBe(1);
      expect(result.summary.failed).toBe(1);
      expect(result.results).toHaveLength(1);
      expect(result.errors).toHaveLength(1);
    });
  });

  describe('getCurrentBookValue', () => {
    it('should return current book value', async () => {
      (DepreciationEngine.getCurrentBookValue as jest.Mock).mockResolvedValue(8200);

      const bookValue = await DepreciationService.getCurrentBookValue(mockAssetId);

      expect(bookValue).toBe(8200);
      expect(DepreciationEngine.getCurrentBookValue).toHaveBeenCalledWith(mockAssetId);
    });
  });

  describe('getDepreciationSchedule', () => {
    it('should return depreciation schedule for an asset', async () => {
      const mockSchedule = [
        {
          id: '1',
          assetId: mockAssetId,
          year: 2020,
          month: 1,
          depreciationAmount: 150,
          accumulatedDepreciation: 150,
          bookValue: 9850,
          method: 'straight_line',
          calculatedAt: new Date(),
          isActual: false,
          notes: null,
        },
      ];

      (prisma.depreciationSchedule.findMany as jest.Mock).mockResolvedValue(mockSchedule);

      const schedule = await DepreciationService.getDepreciationSchedule(mockAssetId);

      expect(schedule).toEqual(mockSchedule);
      expect(prisma.depreciationSchedule.findMany).toHaveBeenCalledWith({
        where: { assetId: mockAssetId },
        orderBy: [{ year: 'asc' }, { month: 'asc' }],
      });
    });
  });

  describe('getDepreciationStatistics', () => {
    it('should return depreciation statistics', async () => {
      const mockAssets = [mockAsset];
      
      (prisma.asset.findMany as jest.Mock).mockResolvedValue(mockAssets);

      const statistics = await DepreciationService.getDepreciationStatistics({});

      expect(statistics).toBeDefined();
      expect(statistics.totalAssets).toBe(1);
      expect(statistics.assetsWithDepreciation).toBe(1);
      expect(statistics.totalAcquisitionCost).toBe(10000);
    });

    it('should handle empty asset list', async () => {
      (prisma.asset.findMany as jest.Mock).mockResolvedValue([]);

      const statistics = await DepreciationService.getDepreciationStatistics({});

      expect(statistics.totalAssets).toBe(0);
      expect(statistics.assetsWithDepreciation).toBe(0);
      expect(statistics.totalAcquisitionCost).toBe(0);
      expect(statistics.totalAccumulatedDepreciation).toBe(0);
      expect(statistics.totalNetBookValue).toBe(0);
    });
  });
});
