import { DepreciationEngine } from '@/lib/engines/depreciation-engine';
import { EnhancedDepreciationSettings } from '@/lib/schemas/depreciation';
import prisma from '@/lib/prisma';

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  depreciationSchedule: {
    deleteMany: jest.fn(),
    createMany: jest.fn(),
    findFirst: jest.fn(),
    findMany: jest.fn(),
  },
  asset: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
  },
}));

describe('DepreciationEngine', () => {
  const mockAssetId = 'test-asset-id';
  const mockPurchasePrice = 10000;
  const mockPurchaseDate = new Date('2020-01-01');
  const mockCurrentDate = new Date('2023-01-01');

  const mockStraightLineSettings: EnhancedDepreciationSettings = {
    id: 'test-settings-id',
    assetTypeId: 'test-asset-type-id',
    method: 'straight_line',
    usefulLife: 5,
    usefulLifeUnit: 'years',
    salvageValue: 1000,
    salvageValueType: 'fixed',
    startDate: mockPurchaseDate,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('calculateDepreciation', () => {
    it('should calculate straight-line depreciation correctly', async () => {
      const result = await DepreciationEngine.calculateDepreciation({
        assetId: mockAssetId,
        purchasePrice: mockPurchasePrice,
        purchaseDate: mockPurchaseDate,
        settings: mockStraightLineSettings,
        currentDate: mockCurrentDate,
      });

      expect(result).toBeDefined();
      expect(result.metadata.method).toBe('straight_line');
      expect(result.metadata.usefulLife).toBe(5);
      expect(result.metadata.salvageValue).toBe(1000);
      
      // Check that depreciation schedule is generated
      expect(result.schedule).toBeDefined();
      expect(result.schedule.length).toBeGreaterThan(0);
      
      // Verify straight-line calculation: (10000 - 1000) / 5 = 1800 per year
      const annualDepreciation = (mockPurchasePrice - mockStraightLineSettings.salvageValue) / mockStraightLineSettings.usefulLife;
      expect(annualDepreciation).toBe(1800);
      
      // Check first year entries
      const firstYearEntries = result.schedule.filter(entry => entry.year === 2020);
      const firstYearTotal = firstYearEntries.reduce((sum, entry) => sum + entry.depreciationAmount, 0);
      expect(firstYearTotal).toBeCloseTo(1800, 2);
    });

    it('should calculate declining balance depreciation correctly', async () => {
      const decliningBalanceSettings: EnhancedDepreciationSettings = {
        ...mockStraightLineSettings,
        method: 'declining_balance',
      };

      const result = await DepreciationEngine.calculateDepreciation({
        assetId: mockAssetId,
        purchasePrice: mockPurchasePrice,
        purchaseDate: mockPurchaseDate,
        settings: decliningBalanceSettings,
        currentDate: mockCurrentDate,
      });

      expect(result.metadata.method).toBe('declining_balance');
      expect(result.schedule).toBeDefined();
      expect(result.schedule.length).toBeGreaterThan(0);
      
      // Declining balance should have higher depreciation in early years
      const firstYearEntries = result.schedule.filter(entry => entry.year === 2020);
      const secondYearEntries = result.schedule.filter(entry => entry.year === 2021);
      
      const firstYearTotal = firstYearEntries.reduce((sum, entry) => sum + entry.depreciationAmount, 0);
      const secondYearTotal = secondYearEntries.reduce((sum, entry) => sum + entry.depreciationAmount, 0);
      
      expect(firstYearTotal).toBeGreaterThan(secondYearTotal);
    });

    it('should calculate double declining balance depreciation correctly', async () => {
      const doubleDecliningSettings: EnhancedDepreciationSettings = {
        ...mockStraightLineSettings,
        method: 'double_declining_balance',
      };

      const result = await DepreciationEngine.calculateDepreciation({
        assetId: mockAssetId,
        purchasePrice: mockPurchasePrice,
        purchaseDate: mockPurchaseDate,
        settings: doubleDecliningSettings,
        currentDate: mockCurrentDate,
      });

      expect(result.metadata.method).toBe('double_declining_balance');
      
      // Double declining should depreciate faster than regular declining balance
      const firstYearEntries = result.schedule.filter(entry => entry.year === 2020);
      const firstYearTotal = firstYearEntries.reduce((sum, entry) => sum + entry.depreciationAmount, 0);
      
      // Double declining rate = 2 / useful life = 2 / 5 = 40%
      // First year depreciation should be approximately 40% of purchase price
      const expectedFirstYear = mockPurchasePrice * 0.4;
      expect(firstYearTotal).toBeCloseTo(expectedFirstYear, -2); // Within 100s
    });

    it('should calculate sum of years digits depreciation correctly', async () => {
      const sumOfYearsSettings: EnhancedDepreciationSettings = {
        ...mockStraightLineSettings,
        method: 'sum_of_years_digits',
      };

      const result = await DepreciationEngine.calculateDepreciation({
        assetId: mockAssetId,
        purchasePrice: mockPurchasePrice,
        purchaseDate: mockPurchaseDate,
        settings: sumOfYearsSettings,
        currentDate: mockCurrentDate,
      });

      expect(result.metadata.method).toBe('sum_of_years_digits');
      
      // Sum of years digits should have decreasing depreciation each year
      const yearlyTotals = [];
      for (let year = 2020; year <= 2024; year++) {
        const yearEntries = result.schedule.filter(entry => entry.year === year);
        const yearTotal = yearEntries.reduce((sum, entry) => sum + entry.depreciationAmount, 0);
        yearlyTotals.push(yearTotal);
      }
      
      // Each year should have less depreciation than the previous
      for (let i = 1; i < yearlyTotals.length; i++) {
        expect(yearlyTotals[i]).toBeLessThan(yearlyTotals[i - 1]);
      }
    });

    it('should handle percentage-based salvage value correctly', async () => {
      const percentageSalvageSettings: EnhancedDepreciationSettings = {
        ...mockStraightLineSettings,
        salvageValue: 10, // 10%
        salvageValueType: 'percentage',
      };

      const result = await DepreciationEngine.calculateDepreciation({
        assetId: mockAssetId,
        purchasePrice: mockPurchasePrice,
        purchaseDate: mockPurchaseDate,
        settings: percentageSalvageSettings,
        currentDate: mockCurrentDate,
      });

      // 10% of 10000 = 1000
      expect(result.metadata.salvageValue).toBe(1000);
      
      // Depreciable amount should be 9000
      const depreciableAmount = mockPurchasePrice - 1000;
      const annualDepreciation = depreciableAmount / mockStraightLineSettings.usefulLife;
      expect(annualDepreciation).toBe(1800);
    });

    it('should throw error for unsupported depreciation method', async () => {
      const unsupportedSettings: EnhancedDepreciationSettings = {
        ...mockStraightLineSettings,
        method: 'units_of_production',
      };

      await expect(
        DepreciationEngine.calculateDepreciation({
          assetId: mockAssetId,
          purchasePrice: mockPurchasePrice,
          purchaseDate: mockPurchaseDate,
          settings: unsupportedSettings,
          currentDate: mockCurrentDate,
        })
      ).rejects.toThrow('Units of production method requires usage data');
    });

    it('should calculate custom rates depreciation correctly', async () => {
      const customRatesSettings: EnhancedDepreciationSettings = {
        ...mockStraightLineSettings,
        method: 'custom',
        customRates: [
          { year: 1, rate: 0.4, description: 'Year 1' },
          { year: 2, rate: 0.3, description: 'Year 2' },
          { year: 3, rate: 0.2, description: 'Year 3' },
          { year: 4, rate: 0.1, description: 'Year 4' },
        ],
      };

      const result = await DepreciationEngine.calculateDepreciation({
        assetId: mockAssetId,
        purchasePrice: mockPurchasePrice,
        purchaseDate: mockPurchaseDate,
        settings: customRatesSettings,
        currentDate: mockCurrentDate,
      });

      expect(result.metadata.method).toBe('custom');
      
      // Check that custom rates are applied correctly
      const firstYearEntries = result.schedule.filter(entry => entry.year === 2020);
      const firstYearTotal = firstYearEntries.reduce((sum, entry) => sum + entry.depreciationAmount, 0);
      
      // First year should be 40% of purchase price
      const expectedFirstYear = mockPurchasePrice * 0.4;
      expect(firstYearTotal).toBeCloseTo(expectedFirstYear, 2);
    });
  });

  describe('getCurrentBookValue', () => {
    it('should return current book value for an asset', async () => {
      const mockSchedule = [
        {
          id: '1',
          assetId: mockAssetId,
          year: 2020,
          month: 12,
          depreciationAmount: 1800,
          accumulatedDepreciation: 1800,
          bookValue: 8200,
          method: 'straight_line',
          calculatedAt: new Date(),
          isActual: true,
          notes: null,
        },
      ];

      (prisma.depreciationSchedule.findMany as jest.Mock).mockResolvedValue(mockSchedule);

      const bookValue = await DepreciationEngine.getCurrentBookValue(mockAssetId);
      expect(bookValue).toBe(8200);
    });

    it('should return purchase price if no depreciation schedule exists', async () => {
      (prisma.depreciationSchedule.findMany as jest.Mock).mockResolvedValue([]);
      (prisma.asset.findUnique as jest.Mock).mockResolvedValue({
        id: mockAssetId,
        purchasePrice: mockPurchasePrice,
      });

      const bookValue = await DepreciationEngine.getCurrentBookValue(mockAssetId);
      expect(bookValue).toBe(mockPurchasePrice);
    });
  });

  describe('saveDepreciationSchedule', () => {
    it('should save depreciation schedule to database', async () => {
      const mockSchedule = [
        {
          year: 2020,
          month: 1,
          depreciationAmount: 150,
          accumulatedDepreciation: 150,
          bookValue: 9850,
          method: 'straight_line',
          isActual: false,
        },
      ];

      (prisma.depreciationSchedule.deleteMany as jest.Mock).mockResolvedValue({ count: 0 });
      (prisma.depreciationSchedule.createMany as jest.Mock).mockResolvedValue({ count: 1 });

      await DepreciationEngine.saveDepreciationSchedule(mockAssetId, mockSchedule);

      expect(prisma.depreciationSchedule.deleteMany).toHaveBeenCalledWith({
        where: { assetId: mockAssetId },
      });

      expect(prisma.depreciationSchedule.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            assetId: mockAssetId,
            year: 2020,
            month: 1,
            depreciationAmount: 150,
            accumulatedDepreciation: 150,
            bookValue: 9850,
            method: 'straight_line',
            isActual: false,
          }),
        ]),
      });
    });
  });
});
