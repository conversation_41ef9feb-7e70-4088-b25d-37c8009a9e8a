import { DepreciationEngine } from '@/lib/engines/depreciation-engine';
import { DepreciationService } from '@/lib/services/depreciation-service';
import prisma from '@/lib/prisma';
import {
  createMockAsset,
  createMockAssetType,
  createMockDepreciationSettings,
} from '../setup';

// This is an integration test that tests the full depreciation flow
describe('Depreciation Integration Flow', () => {
  let testAsset: any;
  let testAssetType: any;
  let testDepreciationSettings: any;

  beforeAll(async () => {
    // Clean up any existing test data
    await prisma.depreciationSchedule.deleteMany({
      where: { assetId: { startsWith: 'test-' } },
    });
    await prisma.asset.deleteMany({
      where: { id: { startsWith: 'test-' } },
    });
    await prisma.depreciationSettings.deleteMany({
      where: { assetTypeId: { startsWith: 'test-' } },
    });
    await prisma.assetType.deleteMany({
      where: { id: { startsWith: 'test-' } },
    });
  });

  beforeEach(async () => {
    // Create test data
    testAssetType = await prisma.assetType.create({
      data: createMockAssetType(),
    });

    testDepreciationSettings = await prisma.depreciationSettings.create({
      data: createMockDepreciationSettings({
        assetTypeId: testAssetType.id,
      }),
    });

    testAsset = await prisma.asset.create({
      data: createMockAsset({
        assetTypeId: testAssetType.id,
      }),
    });
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.depreciationSchedule.deleteMany({
      where: { assetId: testAsset.id },
    });
    await prisma.asset.delete({
      where: { id: testAsset.id },
    });
    await prisma.depreciationSettings.delete({
      where: { id: testDepreciationSettings.id },
    });
    await prisma.assetType.delete({
      where: { id: testAssetType.id },
    });
  });

  describe('Complete Depreciation Calculation Flow', () => {
    it('should calculate, save, and retrieve depreciation schedule', async () => {
      // Step 1: Calculate depreciation using the service
      const calculationResult = await DepreciationService.calculateAssetDepreciation(
        testAsset.id,
        true // Force recalculation
      );

      // Verify calculation result
      expect(calculationResult).toBeDefined();
      expect(calculationResult.totalDepreciation).toBeGreaterThan(0);
      expect(calculationResult.remainingValue).toBeLessThan(testAsset.purchasePrice);
      expect(calculationResult.schedule).toHaveLength(60); // 5 years * 12 months
      expect(calculationResult.metadata.method).toBe('straight_line');

      // Step 2: Verify schedule was saved to database
      const savedSchedule = await prisma.depreciationSchedule.findMany({
        where: { assetId: testAsset.id },
        orderBy: [{ year: 'asc' }, { month: 'asc' }],
      });

      expect(savedSchedule).toHaveLength(60);
      expect(savedSchedule[0].year).toBe(2020);
      expect(savedSchedule[0].month).toBe(1);
      expect(savedSchedule[0].depreciationAmount).toBeCloseTo(150, 2); // (10000 - 1000) / 60 months

      // Step 3: Get current book value
      const currentBookValue = await DepreciationService.getCurrentBookValue(testAsset.id);
      expect(currentBookValue).toBe(savedSchedule[savedSchedule.length - 1].bookValue);

      // Step 4: Get depreciation summary
      const summary = await DepreciationService.getAssetDepreciationSummary(testAsset.id);
      expect(summary).toBeDefined();
      expect(summary!.assetId).toBe(testAsset.id);
      expect(summary!.purchasePrice).toBe(testAsset.purchasePrice);
      expect(summary!.depreciationMethod).toBe('straight_line');

      // Step 5: Verify schedule retrieval
      const retrievedSchedule = await DepreciationService.getDepreciationSchedule(testAsset.id);
      expect(retrievedSchedule).toHaveLength(60);
      expect(retrievedSchedule[0].assetId).toBe(testAsset.id);
    });

    it('should handle recalculation correctly', async () => {
      // First calculation
      await DepreciationService.calculateAssetDepreciation(testAsset.id, true);
      
      const firstSchedule = await prisma.depreciationSchedule.findMany({
        where: { assetId: testAsset.id },
      });
      expect(firstSchedule).toHaveLength(60);

      // Update depreciation settings
      await prisma.depreciationSettings.update({
        where: { id: testDepreciationSettings.id },
        data: { usefulLife: 3 }, // Change from 5 to 3 years
      });

      // Recalculate
      await DepreciationService.calculateAssetDepreciation(testAsset.id, true);
      
      const secondSchedule = await prisma.depreciationSchedule.findMany({
        where: { assetId: testAsset.id },
      });
      expect(secondSchedule).toHaveLength(36); // 3 years * 12 months

      // Verify higher monthly depreciation due to shorter useful life
      expect(secondSchedule[0].depreciationAmount).toBeGreaterThan(firstSchedule[0].depreciationAmount);
    });

    it('should calculate different depreciation methods correctly', async () => {
      const methods = ['straight_line', 'declining_balance', 'double_declining_balance', 'sum_of_years_digits'];
      const results: any[] = [];

      for (const method of methods) {
        // Update depreciation method
        await prisma.depreciationSettings.update({
          where: { id: testDepreciationSettings.id },
          data: { method },
        });

        // Calculate depreciation
        const result = await DepreciationService.calculateAssetDepreciation(testAsset.id, true);
        results.push({ method, result });

        // Clean up schedule for next iteration
        await prisma.depreciationSchedule.deleteMany({
          where: { assetId: testAsset.id },
        });
      }

      // Verify different methods produce different results
      const straightLine = results.find(r => r.method === 'straight_line');
      const decliningBalance = results.find(r => r.method === 'declining_balance');
      const doubleDeclining = results.find(r => r.method === 'double_declining_balance');
      const sumOfYears = results.find(r => r.method === 'sum_of_years_digits');

      // All should have the same total depreciable amount but different timing
      const depreciableAmount = testAsset.purchasePrice - testDepreciationSettings.salvageValue;
      
      expect(straightLine.result.totalDepreciation).toBeCloseTo(depreciableAmount, 0);
      expect(decliningBalance.result.totalDepreciation).toBeLessThanOrEqual(depreciableAmount);
      expect(doubleDeclining.result.totalDepreciation).toBeLessThanOrEqual(depreciableAmount);
      expect(sumOfYears.result.totalDepreciation).toBeCloseTo(depreciableAmount, 0);

      // Accelerated methods should have higher first-year depreciation
      const straightLineFirstYear = straightLine.result.schedule
        .filter((s: any) => s.year === 2020)
        .reduce((sum: number, s: any) => sum + s.depreciationAmount, 0);
      
      const decliningFirstYear = decliningBalance.result.schedule
        .filter((s: any) => s.year === 2020)
        .reduce((sum: number, s: any) => sum + s.depreciationAmount, 0);

      expect(decliningFirstYear).toBeGreaterThan(straightLineFirstYear);
    });
  });

  describe('Bulk Operations', () => {
    let additionalAssets: any[] = [];

    beforeEach(async () => {
      // Create additional test assets
      for (let i = 1; i <= 3; i++) {
        const asset = await prisma.asset.create({
          data: createMockAsset({
            id: `test-asset-${i}`,
            name: `Test Asset ${i}`,
            assetTypeId: testAssetType.id,
          }),
        });
        additionalAssets.push(asset);
      }
    });

    afterEach(async () => {
      // Clean up additional assets
      for (const asset of additionalAssets) {
        await prisma.depreciationSchedule.deleteMany({
          where: { assetId: asset.id },
        });
        await prisma.asset.delete({
          where: { id: asset.id },
        });
      }
      additionalAssets = [];
    });

    it('should handle bulk depreciation calculation', async () => {
      const assetIds = [testAsset.id, ...additionalAssets.map(a => a.id)];
      
      const result = await DepreciationService.bulkCalculateDepreciation({
        assetIds,
        recalculate: true,
      });

      expect(result.summary.total).toBe(4);
      expect(result.summary.successful).toBe(4);
      expect(result.summary.failed).toBe(0);
      expect(result.results).toHaveLength(4);
      expect(result.errors).toHaveLength(0);

      // Verify all schedules were created
      for (const assetId of assetIds) {
        const schedule = await prisma.depreciationSchedule.findMany({
          where: { assetId },
        });
        expect(schedule).toHaveLength(60); // 5 years * 12 months
      }
    });

    it('should handle mixed success/failure in bulk operations', async () => {
      const assetIds = [testAsset.id, 'non-existent-asset', ...additionalAssets.map(a => a.id)];
      
      const result = await DepreciationService.bulkCalculateDepreciation({
        assetIds,
        recalculate: true,
      });

      expect(result.summary.total).toBe(5);
      expect(result.summary.successful).toBe(4);
      expect(result.summary.failed).toBe(1);
      expect(result.results).toHaveLength(4);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].assetId).toBe('non-existent-asset');
    });
  });

  describe('Statistics and Reporting', () => {
    beforeEach(async () => {
      // Calculate depreciation for the test asset
      await DepreciationService.calculateAssetDepreciation(testAsset.id, true);
    });

    it('should generate accurate depreciation statistics', async () => {
      const statistics = await DepreciationService.getDepreciationStatistics({});

      expect(statistics.totalAssets).toBeGreaterThanOrEqual(1);
      expect(statistics.assetsWithDepreciation).toBeGreaterThanOrEqual(1);
      expect(statistics.totalAcquisitionCost).toBeGreaterThanOrEqual(testAsset.purchasePrice);
      expect(statistics.totalAccumulatedDepreciation).toBeGreaterThan(0);
      expect(statistics.totalNetBookValue).toBeLessThan(statistics.totalAcquisitionCost);
      expect(statistics.averageDepreciationRate).toBeGreaterThan(0);
      expect(statistics.averageDepreciationRate).toBeLessThan(100);
    });

    it('should filter statistics correctly', async () => {
      const filteredStats = await DepreciationService.getDepreciationStatistics({
        categories: ['Equipment'],
        includeDisposed: false,
        includeFullyDepreciated: true,
      });

      expect(filteredStats.totalAssets).toBeGreaterThanOrEqual(1);
      expect(filteredStats.assetsWithDepreciation).toBeGreaterThanOrEqual(1);
    });

    it('should retrieve assets with depreciation data', async () => {
      const result = await DepreciationService.getAssetsWithDepreciation(
        {},
        { page: 1, limit: 10 }
      );

      expect(result.assets.length).toBeGreaterThanOrEqual(1);
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);

      const assetWithDepreciation = result.assets.find(a => a.id === testAsset.id);
      expect(assetWithDepreciation).toBeDefined();
    });
  });
});
