# Asset Depreciation Tool Tests

This directory contains comprehensive tests for the Asset Depreciation Tool functionality.

## Test Structure

```
__tests__/
├── depreciation/                 # Unit tests for depreciation functionality
│   ├── depreciation-engine.test.ts     # Tests for calculation engine
│   └── depreciation-service.test.ts    # Tests for service layer
├── api/                         # API endpoint tests
│   └── depreciation/
│       └── calculate.test.ts    # Tests for calculation API
├── utils/                       # Utility tests
│   └── depreciation-error-handler.test.ts  # Error handling tests
├── integration/                 # Integration tests
│   └── depreciation-flow.test.ts       # End-to-end flow tests
├── setup.ts                     # Test setup and utilities
├── global-setup.js             # Global test environment setup
├── global-teardown.js          # Global test environment cleanup
└── README.md                   # This file
```

## Running Tests

### All Tests
```bash
npm test
```

### Watch Mode (for development)
```bash
npm run test:watch
```

### Coverage Report
```bash
npm run test:coverage
```

### Depreciation-specific Tests
```bash
npm run test:depreciation
```

### Integration Tests
```bash
npm run test:integration
```

### CI/CD Tests
```bash
npm run test:ci
```

## Test Categories

### Unit Tests

#### Depreciation Engine Tests (`depreciation-engine.test.ts`)
- Tests all depreciation calculation methods:
  - Straight-line depreciation
  - Declining balance depreciation
  - Double declining balance depreciation
  - Sum of years digits depreciation
  - Custom rates depreciation
- Tests salvage value calculations (fixed and percentage)
- Tests error handling for invalid methods
- Tests schedule generation and saving

#### Depreciation Service Tests (`depreciation-service.test.ts`)
- Tests service layer functionality:
  - Asset depreciation calculation
  - Bulk depreciation operations
  - Depreciation statistics generation
  - Asset filtering and pagination
- Tests error handling and validation
- Tests database integration

#### Error Handler Tests (`depreciation-error-handler.test.ts`)
- Tests custom error classes
- Tests error response formatting
- Tests validation functions
- Tests Prisma error handling
- Tests response helpers

### API Tests

#### Calculate API Tests (`calculate.test.ts`)
- Tests POST /api/depreciation/calculate endpoint
- Tests GET /api/depreciation/calculate endpoint
- Tests single asset calculation
- Tests bulk asset calculation
- Tests error responses and validation

### Integration Tests

#### Depreciation Flow Tests (`depreciation-flow.test.ts`)
- Tests complete end-to-end depreciation workflows
- Tests database operations with real Prisma client
- Tests different depreciation methods with actual data
- Tests bulk operations and statistics generation
- Tests data consistency across operations

## Test Data

The tests use mock data factories defined in `setup.ts`:

- `createMockAsset()` - Creates test asset data
- `createMockAssetType()` - Creates test asset type data
- `createMockDepreciationSettings()` - Creates test depreciation settings
- `createMockDepreciationSchedule()` - Creates test schedule entries

## Test Database

Integration tests use a separate test database to avoid affecting development data:

- Database URL: `TEST_DATABASE_URL` environment variable
- Schema is reset before each test run
- Test data is cleaned up after each test

## Coverage Targets

The test suite aims for high coverage on critical depreciation functionality:

- **Depreciation Engine**: 80% coverage (branches, functions, lines, statements)
- **Depreciation Service**: 80% coverage (branches, functions, lines, statements)
- **Error Handler**: 85% coverage (branches, functions, lines, statements)
- **Overall**: 70% coverage minimum

## Test Environment Setup

### Prerequisites

1. Node.js and npm installed
2. PostgreSQL database for testing
3. Environment variables configured:
   ```bash
   TEST_DATABASE_URL=postgresql://test:test@localhost:5432/test_db
   NODE_ENV=test
   ```

### Database Setup

1. Create a test database:
   ```sql
   CREATE DATABASE test_db;
   CREATE USER test WITH PASSWORD 'test';
   GRANT ALL PRIVILEGES ON DATABASE test_db TO test;
   ```

2. The test setup will automatically:
   - Reset the database schema
   - Generate Prisma client
   - Seed initial data if needed

### Running Tests Locally

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up test database:
   ```bash
   npm run db:push
   ```

3. Run tests:
   ```bash
   npm test
   ```

## Writing New Tests

### Unit Tests

When adding new depreciation functionality:

1. Create unit tests in the appropriate `__tests__/depreciation/` file
2. Mock external dependencies (database, APIs)
3. Test both success and error cases
4. Aim for high coverage of new code

### Integration Tests

For complex workflows:

1. Add tests to `__tests__/integration/depreciation-flow.test.ts`
2. Use real database operations
3. Test complete user scenarios
4. Clean up test data properly

### API Tests

For new API endpoints:

1. Create tests in `__tests__/api/depreciation/`
2. Test all HTTP methods and status codes
3. Test request validation and error responses
4. Mock external services but use real request/response objects

## Debugging Tests

### Common Issues

1. **Database connection errors**: Check `TEST_DATABASE_URL` environment variable
2. **Prisma client errors**: Run `npm run db:generate` to regenerate client
3. **Mock conflicts**: Clear mocks between tests using `jest.clearAllMocks()`
4. **Async test issues**: Ensure all async operations are properly awaited

### Debug Mode

Run tests with debug output:
```bash
DEBUG=* npm test
```

Or run specific tests:
```bash
npm test -- --testNamePattern="should calculate straight-line depreciation"
```

## Continuous Integration

The test suite is designed to run in CI/CD environments:

- Uses `jest --ci` flag for CI-optimized runs
- Generates JUnit XML reports for CI systems
- Includes coverage reporting
- Fails on coverage thresholds not met

## Contributing

When contributing to the depreciation functionality:

1. Write tests for all new features
2. Maintain or improve coverage percentages
3. Follow existing test patterns and naming conventions
4. Update this README if adding new test categories
5. Ensure all tests pass before submitting PRs

## Test Performance

- Unit tests should run in < 5 seconds
- Integration tests may take 10-30 seconds
- Full test suite should complete in < 2 minutes
- Use `--maxWorkers=1` for debugging race conditions
