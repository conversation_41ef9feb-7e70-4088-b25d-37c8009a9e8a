import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { Prisma } from '@prisma/client';
import {
  DepreciationError,
  AssetNotFoundError,
  AssetTypeNotFoundError,
  DepreciationSettingsNotFoundError,
  InvalidDepreciationMethodError,
  CalculationError,
  ValidationError,
  DatabaseError,
  handleDepreciationError,
  createSuccessResponse,
  createPaginatedResponse,
  validateAssetId,
  validateAssetTypeId,
  validateDepreciationSettings,
  validatePurchaseData,
} from '@/lib/utils/depreciation-error-handler';

describe('DepreciationError Classes', () => {
  describe('DepreciationError', () => {
    it('should create a depreciation error with correct properties', () => {
      const error = new DepreciationError('Test error', 'TEST_ERROR', 400, { detail: 'test' });
      
      expect(error.message).toBe('Test error');
      expect(error.code).toBe('TEST_ERROR');
      expect(error.statusCode).toBe(400);
      expect(error.details).toEqual({ detail: 'test' });
      expect(error.name).toBe('DepreciationError');
    });
  });

  describe('AssetNotFoundError', () => {
    it('should create an asset not found error', () => {
      const assetId = 'test-asset-id';
      const error = new AssetNotFoundError(assetId);
      
      expect(error.message).toBe(`Asset with ID ${assetId} not found`);
      expect(error.code).toBe('ASSET_NOT_FOUND');
      expect(error.statusCode).toBe(404);
    });
  });

  describe('AssetTypeNotFoundError', () => {
    it('should create an asset type not found error', () => {
      const assetTypeId = 'test-asset-type-id';
      const error = new AssetTypeNotFoundError(assetTypeId);
      
      expect(error.message).toBe(`Asset type with ID ${assetTypeId} not found`);
      expect(error.code).toBe('ASSET_TYPE_NOT_FOUND');
      expect(error.statusCode).toBe(404);
    });
  });

  describe('DepreciationSettingsNotFoundError', () => {
    it('should create a depreciation settings not found error', () => {
      const assetTypeId = 'test-asset-type-id';
      const error = new DepreciationSettingsNotFoundError(assetTypeId);
      
      expect(error.message).toBe(`No depreciation settings found for asset type ${assetTypeId}`);
      expect(error.code).toBe('DEPRECIATION_SETTINGS_NOT_FOUND');
      expect(error.statusCode).toBe(400);
    });
  });

  describe('InvalidDepreciationMethodError', () => {
    it('should create an invalid depreciation method error', () => {
      const method = 'invalid_method';
      const error = new InvalidDepreciationMethodError(method);
      
      expect(error.message).toBe(`Invalid depreciation method: ${method}`);
      expect(error.code).toBe('INVALID_DEPRECIATION_METHOD');
      expect(error.statusCode).toBe(400);
    });
  });

  describe('CalculationError', () => {
    it('should create a calculation error', () => {
      const message = 'Division by zero';
      const details = { operation: 'divide' };
      const error = new CalculationError(message, details);
      
      expect(error.message).toBe(`Depreciation calculation failed: ${message}`);
      expect(error.code).toBe('CALCULATION_ERROR');
      expect(error.statusCode).toBe(500);
      expect(error.details).toEqual(details);
    });
  });

  describe('ValidationError', () => {
    it('should create a validation error', () => {
      const message = 'Invalid input';
      const details = { field: 'assetId' };
      const error = new ValidationError(message, details);
      
      expect(error.message).toBe(`Validation failed: ${message}`);
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.statusCode).toBe(400);
      expect(error.details).toEqual(details);
    });
  });

  describe('DatabaseError', () => {
    it('should create a database error', () => {
      const message = 'Connection failed';
      const details = { host: 'localhost' };
      const error = new DatabaseError(message, details);
      
      expect(error.message).toBe(`Database operation failed: ${message}`);
      expect(error.code).toBe('DATABASE_ERROR');
      expect(error.statusCode).toBe(500);
      expect(error.details).toEqual(details);
    });
  });
});

describe('handleDepreciationError', () => {
  it('should handle DepreciationError correctly', () => {
    const error = new AssetNotFoundError('test-id');
    const response = handleDepreciationError(error);
    
    expect(response).toBeInstanceOf(NextResponse);
    // Note: In a real test environment, you'd need to extract the response body
    // This is a simplified test showing the structure
  });

  it('should handle ZodError correctly', () => {
    const zodError = new ZodError([
      {
        code: 'invalid_type',
        expected: 'string',
        received: 'number',
        path: ['assetId'],
        message: 'Expected string, received number',
      },
    ]);
    
    const response = handleDepreciationError(zodError);
    expect(response).toBeInstanceOf(NextResponse);
  });

  it('should handle Prisma errors correctly', () => {
    const prismaError = new Prisma.PrismaClientKnownRequestError(
      'Unique constraint failed',
      {
        code: 'P2002',
        clientVersion: '4.0.0',
        meta: { target: ['email'] },
      }
    );
    
    const response = handleDepreciationError(prismaError);
    expect(response).toBeInstanceOf(NextResponse);
  });

  it('should handle generic Error correctly', () => {
    const error = new Error('Generic error message');
    const response = handleDepreciationError(error);
    
    expect(response).toBeInstanceOf(NextResponse);
  });

  it('should handle unknown errors correctly', () => {
    const error = 'String error';
    const response = handleDepreciationError(error);
    
    expect(response).toBeInstanceOf(NextResponse);
  });
});

describe('Validation Functions', () => {
  describe('validateAssetId', () => {
    it('should pass for valid asset ID', () => {
      expect(() => validateAssetId('validAssetId123')).not.toThrow();
    });

    it('should throw ValidationError for empty asset ID', () => {
      expect(() => validateAssetId('')).toThrow(ValidationError);
      expect(() => validateAssetId('')).toThrow('Asset ID is required');
    });

    it('should throw ValidationError for invalid asset ID format', () => {
      expect(() => validateAssetId('invalid-id-with-special-chars!@#')).toThrow(ValidationError);
      expect(() => validateAssetId('invalid-id-with-special-chars!@#')).toThrow('Invalid asset ID format');
    });
  });

  describe('validateAssetTypeId', () => {
    it('should pass for valid asset type ID', () => {
      expect(() => validateAssetTypeId('validAssetTypeId123')).not.toThrow();
    });

    it('should throw ValidationError for empty asset type ID', () => {
      expect(() => validateAssetTypeId('')).toThrow(ValidationError);
      expect(() => validateAssetTypeId('')).toThrow('Asset type ID is required');
    });

    it('should throw ValidationError for invalid asset type ID format', () => {
      expect(() => validateAssetTypeId('invalid-id!')).toThrow(ValidationError);
      expect(() => validateAssetTypeId('invalid-id!')).toThrow('Invalid asset type ID format');
    });
  });

  describe('validateDepreciationSettings', () => {
    const validSettings = {
      method: 'straight_line',
      usefulLife: 5,
      salvageValue: 1000,
      salvageValueType: 'fixed',
    };

    it('should pass for valid depreciation settings', () => {
      expect(() => validateDepreciationSettings(validSettings)).not.toThrow();
    });

    it('should throw ValidationError for missing settings', () => {
      expect(() => validateDepreciationSettings(null)).toThrow(ValidationError);
      expect(() => validateDepreciationSettings(null)).toThrow('Depreciation settings are required');
    });

    it('should throw ValidationError for missing method', () => {
      const settings = { ...validSettings, method: undefined };
      expect(() => validateDepreciationSettings(settings)).toThrow(ValidationError);
      expect(() => validateDepreciationSettings(settings)).toThrow('Depreciation method is required');
    });

    it('should throw ValidationError for invalid useful life', () => {
      const settings = { ...validSettings, usefulLife: 0 };
      expect(() => validateDepreciationSettings(settings)).toThrow(ValidationError);
      expect(() => validateDepreciationSettings(settings)).toThrow('Useful life must be greater than 0');
    });

    it('should throw ValidationError for negative salvage value', () => {
      const settings = { ...validSettings, salvageValue: -100 };
      expect(() => validateDepreciationSettings(settings)).toThrow(ValidationError);
      expect(() => validateDepreciationSettings(settings)).toThrow('Salvage value cannot be negative');
    });

    it('should throw ValidationError for salvage value percentage > 100%', () => {
      const settings = { ...validSettings, salvageValue: 150, salvageValueType: 'percentage' };
      expect(() => validateDepreciationSettings(settings)).toThrow(ValidationError);
      expect(() => validateDepreciationSettings(settings)).toThrow('Salvage value percentage cannot exceed 100%');
    });
  });

  describe('validatePurchaseData', () => {
    const validDate = new Date('2020-01-01');

    it('should pass for valid purchase data', () => {
      expect(() => validatePurchaseData(10000, validDate)).not.toThrow();
    });

    it('should throw ValidationError for invalid purchase price', () => {
      expect(() => validatePurchaseData(0, validDate)).toThrow(ValidationError);
      expect(() => validatePurchaseData(0, validDate)).toThrow('Purchase price must be greater than 0');
    });

    it('should throw ValidationError for missing purchase date', () => {
      expect(() => validatePurchaseData(10000, null as any)).toThrow(ValidationError);
      expect(() => validatePurchaseData(10000, null as any)).toThrow('Purchase date is required');
    });

    it('should throw ValidationError for future purchase date', () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() + 1);
      
      expect(() => validatePurchaseData(10000, futureDate)).toThrow(ValidationError);
      expect(() => validatePurchaseData(10000, futureDate)).toThrow('Purchase date cannot be in the future');
    });

    it('should throw ValidationError for purchase date too far in the past', () => {
      const ancientDate = new Date('1900-01-01');
      
      expect(() => validatePurchaseData(10000, ancientDate)).toThrow(ValidationError);
      expect(() => validatePurchaseData(10000, ancientDate)).toThrow('Purchase date is too far in the past');
    });
  });
});

describe('Response Helpers', () => {
  describe('createSuccessResponse', () => {
    it('should create a success response', () => {
      const data = { test: 'data' };
      const message = 'Operation successful';
      
      const response = createSuccessResponse(data, message);
      
      expect(response).toBeInstanceOf(NextResponse);
    });
  });

  describe('createPaginatedResponse', () => {
    it('should create a paginated response', () => {
      const data = [{ id: 1 }, { id: 2 }];
      const pagination = { page: 1, limit: 10 };
      const total = 25;
      const message = 'Data retrieved';
      
      const response = createPaginatedResponse(data, pagination, total, message);
      
      expect(response).toBeInstanceOf(NextResponse);
    });
  });
});
