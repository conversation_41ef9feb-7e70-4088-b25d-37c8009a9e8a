const { execSync } = require('child_process');

module.exports = async () => {
  console.log('Setting up test environment...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/test_db';
  
  try {
    // Reset test database schema
    console.log('Resetting test database...');
    execSync('npx prisma db push --force-reset', { 
      stdio: 'inherit',
      env: { ...process.env, DATABASE_URL: process.env.DATABASE_URL }
    });
    
    // Generate Prisma client for test environment
    console.log('Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    
    console.log('Test environment setup complete.');
  } catch (error) {
    console.error('Failed to setup test environment:', error);
    process.exit(1);
  }
};
