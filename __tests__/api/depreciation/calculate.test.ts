import { NextRequest } from 'next/server';
import { POST, GET } from '@/app/api/depreciation/calculate/route';
import { DepreciationEngine } from '@/lib/engines/depreciation-engine';
import prisma from '@/lib/prisma';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  asset: {
    findUnique: jest.fn(),
  },
  depreciationSchedule: {
    findFirst: jest.fn(),
  },
}));

jest.mock('@/lib/engines/depreciation-engine');

describe('/api/depreciation/calculate', () => {
  const mockAssetId = 'test-asset-id';
  const mockAsset = {
    id: mockAssetId,
    name: 'Test Asset',
    purchasePrice: 10000,
    purchaseDate: new Date('2020-01-01'),
    assetType: {
      depreciationSettings: {
        id: 'settings-id',
        assetTypeId: 'asset-type-id',
        method: 'straight_line',
        usefulLife: 5,
        usefulLifeUnit: 'years',
        salvageValue: 1000,
        salvageValueType: 'fixed',
        startDate: new Date('2020-01-01'),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    },
  };

  const mockCalculationResult = {
    totalDepreciation: 3600,
    remainingValue: 6400,
    schedule: [
      {
        year: 2020,
        month: 1,
        depreciationAmount: 150,
        accumulatedDepreciation: 150,
        bookValue: 9850,
        method: 'straight_line',
        isActual: false,
      },
    ],
    metadata: {
      method: 'straight_line' as const,
      usefulLife: 5,
      salvageValue: 1000,
      calculatedAt: new Date(),
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/depreciation/calculate', () => {
    it('should calculate depreciation for a single asset', async () => {
      const request = new NextRequest('http://localhost:3000/api/depreciation/calculate', {
        method: 'POST',
        body: JSON.stringify({
          assetId: mockAssetId,
          recalculate: false,
        }),
      });

      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(mockAsset);
      (DepreciationEngine.calculateDepreciationFromPrisma as jest.Mock).mockResolvedValue(mockCalculationResult);
      (DepreciationEngine.saveDepreciationSchedule as jest.Mock).mockResolvedValue(undefined);
      (prisma.depreciationSchedule.findFirst as jest.Mock).mockResolvedValue(null);

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockCalculationResult);
    });

    it('should handle bulk calculation', async () => {
      const request = new NextRequest('http://localhost:3000/api/depreciation/calculate', {
        method: 'POST',
        body: JSON.stringify({
          assetIds: [mockAssetId],
          recalculate: false,
        }),
      });

      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(mockAsset);
      (DepreciationEngine.calculateDepreciationFromPrisma as jest.Mock).mockResolvedValue(mockCalculationResult);
      (DepreciationEngine.saveDepreciationSchedule as jest.Mock).mockResolvedValue(undefined);
      (prisma.depreciationSchedule.findFirst as jest.Mock).mockResolvedValue(null);

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.summary.total).toBe(1);
      expect(data.data.summary.successful).toBe(1);
    });

    it('should return 404 for non-existent asset', async () => {
      const request = new NextRequest('http://localhost:3000/api/depreciation/calculate', {
        method: 'POST',
        body: JSON.stringify({
          assetId: 'non-existent-id',
          recalculate: false,
        }),
      });

      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(null);

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Asset not found');
    });

    it('should return 400 for asset without depreciation settings', async () => {
      const assetWithoutSettings = {
        ...mockAsset,
        assetType: {
          depreciationSettings: null,
        },
      };

      const request = new NextRequest('http://localhost:3000/api/depreciation/calculate', {
        method: 'POST',
        body: JSON.stringify({
          assetId: mockAssetId,
          recalculate: false,
        }),
      });

      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(assetWithoutSettings);

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('No depreciation settings found');
    });

    it('should return 400 for invalid request body', async () => {
      const request = new NextRequest('http://localhost:3000/api/depreciation/calculate', {
        method: 'POST',
        body: JSON.stringify({
          // Missing required assetId
          recalculate: false,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid request parameters');
    });

    it('should save schedule when recalculate is true', async () => {
      const request = new NextRequest('http://localhost:3000/api/depreciation/calculate', {
        method: 'POST',
        body: JSON.stringify({
          assetId: mockAssetId,
          recalculate: true,
        }),
      });

      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(mockAsset);
      (DepreciationEngine.calculateDepreciationFromPrisma as jest.Mock).mockResolvedValue(mockCalculationResult);
      (DepreciationEngine.saveDepreciationSchedule as jest.Mock).mockResolvedValue(undefined);

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(DepreciationEngine.saveDepreciationSchedule).toHaveBeenCalledWith(
        mockAssetId,
        mockCalculationResult.schedule
      );
    });
  });

  describe('GET /api/depreciation/calculate', () => {
    it('should return current book value and depreciation info', async () => {
      const url = new URL('http://localhost:3000/api/depreciation/calculate');
      url.searchParams.set('assetId', mockAssetId);
      
      const request = new NextRequest(url);

      (DepreciationEngine.getCurrentBookValue as jest.Mock).mockResolvedValue(8200);
      (prisma.asset.findUnique as jest.Mock).mockResolvedValue({
        ...mockAsset,
        depreciationSchedule: [
          {
            id: '1',
            assetId: mockAssetId,
            year: 2023,
            month: 1,
            calculatedAt: new Date(),
          },
        ],
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.assetId).toBe(mockAssetId);
      expect(data.data.bookValue).toBe(8200);
      expect(data.data.accumulatedDepreciation).toBe(1800); // 10000 - 8200
      expect(data.data.purchasePrice).toBe(10000);
    });

    it('should return 400 for missing asset ID', async () => {
      const request = new NextRequest('http://localhost:3000/api/depreciation/calculate');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Asset ID is required');
    });

    it('should return 400 for invalid asset ID format', async () => {
      const url = new URL('http://localhost:3000/api/depreciation/calculate');
      url.searchParams.set('assetId', 'invalid-id-with-special-chars!@#');
      
      const request = new NextRequest(url);

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid asset ID format');
    });

    it('should return 404 for non-existent asset', async () => {
      const url = new URL('http://localhost:3000/api/depreciation/calculate');
      url.searchParams.set('assetId', 'non-existent-id');
      
      const request = new NextRequest(url);

      (DepreciationEngine.getCurrentBookValue as jest.Mock).mockResolvedValue(0);
      (prisma.asset.findUnique as jest.Mock).mockResolvedValue(null);

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Asset not found');
    });
  });
});
