#!/usr/bin/env node

/**
 * Migration script for the remaining 2 admin pages that still use static tabs
 */

const fs = require('fs');

// Configuration for remaining pages
const REMAINING_PAGES = {
  'app/admin/ecommerce/page.tsx': {
    tabsConfig: 'getEcommerceHeaderTabs',
    headerConfig: 'getEcommerceHeaderConfig',
    tabs: ['overview', 'products', 'orders', 'customers', 'analytics']
  },
  'app/admin/customer-dashboard/page.tsx': {
    tabsConfig: 'getCustomerDashboardHeaderTabs', 
    headerConfig: 'getCustomerDashboardHeaderConfig',
    tabs: ['overview', 'profile', 'assets', 'support', 'billing']
  }
};

function migrateRemainingPage(filePath, config) {
  console.log(`\n🔄 Migrating ${filePath}...`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ File not found: ${filePath}`);
    return false;
  }

  // Create backup
  const backupPath = `${filePath}.backup.${Date.now()}`;
  fs.copyFileSync(filePath, backupPath);
  console.log(`📋 Created backup: ${backupPath}`);

  let content = fs.readFileSync(filePath, 'utf8');

  // Step 1: Update imports
  console.log('  📦 Updating imports...');
  
  // Remove static tabs import
  content = content.replace(
    /import { Tabs, TabsContent, TabsList, TabsTrigger } from "@\/components\/ui\/tabs"\n?/g,
    ''
  );
  
  // Add useMemo if not present
  if (content.includes('import { useState }') && !content.includes('useMemo')) {
    content = content.replace(
      'import { useState }',
      'import { useState, useMemo }'
    );
  }
  
  if (content.includes('import { useState, useEffect }') && !content.includes('useMemo')) {
    content = content.replace(
      'import { useState, useEffect }',
      'import { useState, useEffect, useMemo }'
    );
  }
  
  // Add new imports after useAdminHeader import
  const headerImportMatch = content.match(/import { useAdminHeader } from "@\/hooks\/use-admin-header"/);
  if (headerImportMatch) {
    content = content.replace(
      headerImportMatch[0],
      `${headerImportMatch[0]}
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { HeaderTabContent } from "@/components/ui/header-tab-content"`
    );
  }

  // Add tabs config import
  const tabsConfigImport = `import { ${config.tabsConfig} } from "@/lib/utils/admin-tabs-configs"`;
  if (!content.includes(tabsConfigImport)) {
    const headerConfigImportMatch = content.match(/import.*admin-header-configs.*/);
    if (headerConfigImportMatch) {
      content = content.replace(
        headerConfigImportMatch[0],
        `${headerConfigImportMatch[0]}
${tabsConfigImport}`
      );
    }
  }

  // Step 2: Update header setup
  console.log('  🎯 Updating header setup...');
  
  // Find and replace the header setup
  const headerSetupPattern = /useAdminHeader\(([^)]+)\)/;
  const headerSetupMatch = content.match(headerSetupPattern);
  
  if (headerSetupMatch) {
    const headerConfigCall = headerSetupMatch[1];
    const newHeaderSetup = `
  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => ${headerConfigCall}(), []);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => ${config.tabsConfig}(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "${config.tabs[0]}");`;

    // Replace the existing header setup
    content = content.replace(
      /\/\/ Set up the header for this page[\s\S]*?useAdminHeader\([^)]+\)/,
      newHeaderSetup
    );
  }

  // Step 3: Add getTabContent function
  console.log('  🔧 Adding getTabContent function...');
  
  const getTabContentFunction = `
  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
${config.tabs.map(tab => `      case "${tab}":
        return (
          <div className="space-y-4">
            <div>Content for ${tab} tab - TODO: Move existing content here</div>
          </div>
        );`).join('\n')}
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };`;

  // Insert before the return statement
  const returnPattern = /return \(/;
  const returnMatch = content.match(returnPattern);
  
  if (returnMatch) {
    const insertIndex = content.indexOf(returnMatch[0]);
    content = content.slice(0, insertIndex) + getTabContentFunction + '\n\n  ' + content.slice(insertIndex);
  }

  // Step 4: Replace static tabs with HeaderTabContent
  console.log('  🔄 Replacing static tabs structure...');
  
  // Find and replace the entire Tabs structure
  const tabsPattern = /<Tabs[^>]*>[\s\S]*?<\/Tabs>/;
  const tabsMatch = content.match(tabsPattern);
  
  if (tabsMatch) {
    const headerTabContent = `      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
${config.tabs.map(tab => `          { id: "${tab}", content: getTabContent("${tab}") },`).join('\n')}
        ]}
      />`;

    content = content.replace(tabsMatch[0], headerTabContent);
  }

  // Step 5: Write the migrated file
  console.log('  💾 Writing migrated file...');
  fs.writeFileSync(filePath, content);
  
  console.log(`✅ Successfully migrated ${filePath}`);
  return true;
}

function main() {
  console.log('🚀 Migrating remaining admin pages with static tabs...\n');
  
  const results = {
    success: [],
    failed: []
  };

  Object.entries(REMAINING_PAGES).forEach(([filePath, config]) => {
    try {
      const success = migrateRemainingPage(filePath, config);
      
      if (success) {
        results.success.push(filePath);
      } else {
        results.failed.push(filePath);
      }
    } catch (error) {
      console.error(`❌ Error migrating ${filePath}:`, error.message);
      results.failed.push(filePath);
    }
  });

  // Print summary
  console.log('\n📊 Migration Summary:');
  console.log(`✅ Successfully migrated: ${results.success.length} files`);
  console.log(`❌ Failed migrations: ${results.failed.length} files`);

  if (results.success.length > 0) {
    console.log('\n✅ Successfully migrated:');
    results.success.forEach(file => console.log(`  - ${file}`));
  }

  if (results.failed.length > 0) {
    console.log('\n❌ Failed migrations:');
    results.failed.forEach(file => console.log(`  - ${file}`));
  }

  console.log('\n🎉 Migration complete!');
  console.log('\n📝 Next steps:');
  console.log('1. Add the missing tab configs to lib/utils/admin-tabs-configs.tsx');
  console.log('2. Review migrated files and move existing tab content to getTabContent function');
  console.log('3. Test each page to ensure tabs work correctly');
  console.log('4. Remove backup files once satisfied with migration');
}

if (require.main === module) {
  main();
}

module.exports = { migrateRemainingPage, REMAINING_PAGES };
