#!/usr/bin/env node

/**
 * Advanced migration script that extracts and migrates existing tab content
 * This script handles the complex task of moving content from static tabs to the new structure
 */

const fs = require('fs');
const path = require('path');

class TabContentExtractor {
  constructor() {
    this.extractedContent = new Map();
  }

  /**
   * Extract content from TabsContent components
   */
  extractTabContent(content, tabId) {
    // Pattern to match TabsContent with specific value
    const tabContentPattern = new RegExp(
      `<TabsContent\\s+value="${tabId}"[^>]*>([\\s\\S]*?)<\\/TabsContent>`,
      'g'
    );
    
    const matches = [...content.matchAll(tabContentPattern)];
    
    if (matches.length > 0) {
      // Get the content inside TabsContent, removing the outer wrapper
      let tabContent = matches[0][1].trim();
      
      // Clean up the content - remove extra indentation
      const lines = tabContent.split('\n');
      const minIndent = Math.min(
        ...lines
          .filter(line => line.trim().length > 0)
          .map(line => line.match(/^\\s*/)[0].length)
      );
      
      tabContent = lines
        .map(line => line.slice(minIndent))
        .join('\n')
        .trim();
      
      return tabContent;
    }
    
    return null;
  }

  /**
   * Generate getTabContent function with extracted content
   */
  generateGetTabContentFunction(tabContents, defaultTab = 'overview') {
    const cases = Object.entries(tabContents)
      .map(([tabId, content]) => {
        if (content) {
          return `      case "${tabId}":
        return (
          <div className="space-y-4">
${content.split('\n').map(line => `            ${line}`).join('\n')}
          </div>
        );`;
        } else {
          return `      case "${tabId}":
        return (
          <div className="space-y-4">
            <div>Content for ${tabId} tab</div>
          </div>
        );`;
        }
      })
      .join('\n');

    return `
  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
${cases}
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };`;
  }

  /**
   * Process a single file for content extraction and migration
   */
  processFile(filePath, config) {
    console.log(`\\n🔍 Processing ${filePath}...`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`❌ File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Extract content for each tab
    const tabContents = {};
    config.tabs.forEach(tabId => {
      console.log(`  📄 Extracting content for tab: ${tabId}`);
      const extractedContent = this.extractTabContent(content, tabId);
      tabContents[tabId] = extractedContent;
      
      if (extractedContent) {
        console.log(`    ✅ Found content (${extractedContent.length} chars)`);
      } else {
        console.log(`    ⚠️  No content found`);
      }
    });

    // Store extracted content for analysis
    this.extractedContent.set(filePath, tabContents);

    return tabContents;
  }

  /**
   * Generate migration report
   */
  generateReport() {
    console.log('\\n📊 Content Extraction Report:');
    
    this.extractedContent.forEach((tabContents, filePath) => {
      console.log(`\\n📁 ${filePath}:`);
      
      Object.entries(tabContents).forEach(([tabId, content]) => {
        if (content) {
          const lines = content.split('\\n').length;
          const chars = content.length;
          console.log(`  ✅ ${tabId}: ${lines} lines, ${chars} characters`);
        } else {
          console.log(`  ❌ ${tabId}: No content found`);
        }
      });
    });
  }

  /**
   * Save extracted content to JSON for manual review
   */
  saveExtractedContent(outputPath = 'extracted-tab-content.json') {
    const data = {};
    
    this.extractedContent.forEach((tabContents, filePath) => {
      data[filePath] = tabContents;
    });

    fs.writeFileSync(outputPath, JSON.stringify(data, null, 2));
    console.log(`\\n💾 Extracted content saved to: ${outputPath}`);
  }
}

/**
 * Complete migration function that handles both structure and content
 */
function completeMigration(filePath, config) {
  console.log(`\\n🚀 Complete migration for ${filePath}...`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ File not found: ${filePath}`);
    return false;
  }

  // Create backup
  const backupPath = `${filePath}.backup.${Date.now()}`;
  fs.copyFileSync(filePath, backupPath);
  console.log(`📋 Created backup: ${backupPath}`);

  let content = fs.readFileSync(filePath, 'utf8');
  const extractor = new TabContentExtractor();
  
  // Extract existing tab content
  const tabContents = extractor.processFile(filePath, config);
  
  // Step 1: Update imports
  console.log('  📦 Updating imports...');
  
  // Remove static tabs import
  content = content.replace(
    /import { Tabs, TabsContent, TabsList, TabsTrigger } from "@\/components\/ui\/tabs"/g,
    ''
  );
  
  // Add useMemo if not present
  if (!content.includes('useMemo')) {
    content = content.replace(
      /import { useState/,
      'import { useState, useMemo'
    );
    content = content.replace(
      /import { useState, useEffect/,
      'import { useState, useEffect, useMemo'
    );
  }
  
  // Add new imports
  const headerImportMatch = content.match(/import { useAdminHeader } from "@\/hooks\/use-admin-header"/);
  if (headerImportMatch) {
    content = content.replace(
      headerImportMatch[0],
      `${headerImportMatch[0]}
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { HeaderTabContent } from "@/components/ui/header-tab-content"`
    );
  }

  // Add tabs config import
  const tabsConfigImport = `import { ${config.tabsConfig} } from "@/lib/utils/admin-tabs-configs"`;
  if (!content.includes(tabsConfigImport)) {
    const headerConfigImportMatch = content.match(/import.*admin-header-configs.*/);
    if (headerConfigImportMatch) {
      content = content.replace(
        headerConfigImportMatch[0],
        `${headerConfigImportMatch[0]}
${tabsConfigImport}`
      );
    }
  }

  // Step 2: Update header setup
  console.log('  🎯 Updating header setup...');
  const headerSetupPattern = /useAdminHeader\(([^)]+)\)/;
  const headerSetupMatch = content.match(headerSetupPattern);
  
  if (headerSetupMatch) {
    const headerConfigCall = headerSetupMatch[1];
    const newHeaderSetup = `
  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => ${headerConfigCall}(), []);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => ${config.tabsConfig}(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "${config.tabs[0]}");`;

    content = content.replace(
      /\/\/ Set up the header for this page[\s\S]*?useAdminHeader\([^)]+\)/,
      newHeaderSetup
    );
  }

  // Step 3: Add getTabContent function with extracted content
  console.log('  🔧 Adding getTabContent function with extracted content...');
  const getTabContentFunction = extractor.generateGetTabContentFunction(tabContents, config.tabs[0]);

  // Insert before the first function or return statement
  const insertPattern = /(const \w+Badge|const \w+Status|return \()/;
  const insertMatch = content.match(insertPattern);
  
  if (insertMatch) {
    const insertIndex = content.indexOf(insertMatch[0]);
    content = content.slice(0, insertIndex) + getTabContentFunction + '\n\n  ' + content.slice(insertIndex);
  }

  // Step 4: Replace static tabs with HeaderTabContent
  console.log('  🔄 Replacing static tabs structure...');
  const tabsPattern = /<Tabs[^>]*>[\s\S]*?<\/Tabs>/;
  const tabsMatch = content.match(tabsPattern);
  
  if (tabsMatch) {
    const headerTabContent = `      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
${config.tabs.map(tab => `          { id: "${tab}", content: getTabContent("${tab}") },`).join('\n')}
        ]}
      />`;

    content = content.replace(tabsMatch[0], headerTabContent);
  }

  // Step 5: Clean up any remaining TabsContent references
  content = content.replace(/<TabsContent[\s\S]*?<\/TabsContent>/g, '');

  // Step 6: Write the migrated file
  console.log('  💾 Writing migrated file...');
  fs.writeFileSync(filePath, content);
  
  console.log(`✅ Successfully migrated ${filePath}`);
  return true;
}

module.exports = { TabContentExtractor, completeMigration };
