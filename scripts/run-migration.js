#!/usr/bin/env node

/**
 * Main migration runner script
 * Orchestrates the complete migration of admin pages from static tabs to header-integrated tabs
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { MIGRATION_CONFIG } = require('./migrate-admin-tabs');
const { TabContentExtractor, completeMigration } = require('./migrate-tab-content');

class MigrationRunner {
  constructor() {
    this.results = {
      success: [],
      failed: [],
      backups: [],
      skipped: []
    };
    this.startTime = Date.now();
  }

  /**
   * Check if file needs migration (has static tabs)
   */
  needsMigration(filePath) {
    if (!fs.existsSync(filePath)) {
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if file has static tabs imports
    const hasStaticTabs = content.includes('import { Tabs, TabsContent, TabsList, TabsTrigger }');
    
    // Check if file already has header-integrated tabs
    const hasHeaderTabs = content.includes('HeaderTabContent') || content.includes('useHeaderTabs');
    
    return hasStaticTabs && !hasHeaderTabs;
  }

  /**
   * Validate migration configuration
   */
  validateConfig() {
    console.log('🔍 Validating migration configuration...');
    
    const issues = [];
    
    Object.entries(MIGRATION_CONFIG).forEach(([filePath, config]) => {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        issues.push(`File not found: ${filePath}`);
      }
      
      // Check if required config properties exist
      if (!config.tabsConfig || !config.headerConfig || !config.tabs) {
        issues.push(`Invalid config for ${filePath}: missing required properties`);
      }
      
      // Check if tabs array is not empty
      if (!Array.isArray(config.tabs) || config.tabs.length === 0) {
        issues.push(`Invalid config for ${filePath}: tabs array is empty`);
      }
    });

    if (issues.length > 0) {
      console.log('❌ Configuration validation failed:');
      issues.forEach(issue => console.log(`  - ${issue}`));
      return false;
    }

    console.log('✅ Configuration validation passed');
    return true;
  }

  /**
   * Run pre-migration checks
   */
  runPreChecks() {
    console.log('\n🔍 Running pre-migration checks...');
    
    // Check if required dependencies exist
    const requiredFiles = [
      'hooks/use-admin-header.ts',
      'hooks/use-admin-tabs.ts',
      'components/ui/header-tab-content.tsx',
      'lib/utils/admin-header-configs.tsx',
      'lib/utils/admin-tabs-configs.tsx'
    ];

    const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
    
    if (missingFiles.length > 0) {
      console.log('❌ Missing required files:');
      missingFiles.forEach(file => console.log(`  - ${file}`));
      return false;
    }

    console.log('✅ All required dependencies found');
    return true;
  }

  /**
   * Create comprehensive backup
   */
  createBackups() {
    console.log('\n📋 Creating backups...');
    
    const backupDir = `backups/admin-tabs-migration-${Date.now()}`;
    
    if (!fs.existsSync('backups')) {
      fs.mkdirSync('backups');
    }
    
    fs.mkdirSync(backupDir);

    Object.keys(MIGRATION_CONFIG).forEach(filePath => {
      if (fs.existsSync(filePath)) {
        const fileName = path.basename(filePath);
        const backupPath = path.join(backupDir, fileName);
        fs.copyFileSync(filePath, backupPath);
        this.results.backups.push(backupPath);
        console.log(`  📄 Backed up: ${filePath} -> ${backupPath}`);
      }
    });

    console.log(`✅ Created ${this.results.backups.length} backup files in ${backupDir}`);
    return backupDir;
  }

  /**
   * Run the migration for all configured files
   */
  runMigration() {
    console.log('\n🚀 Starting migration process...');

    Object.entries(MIGRATION_CONFIG).forEach(([filePath, config]) => {
      try {
        console.log(`\n${'='.repeat(60)}`);
        console.log(`📁 Processing: ${filePath}`);
        console.log(`${'='.repeat(60)}`);

        // Check if migration is needed
        if (!this.needsMigration(filePath)) {
          console.log(`⏭️  Skipping ${filePath} - already migrated or no static tabs found`);
          this.results.skipped.push(filePath);
          return;
        }

        // Run complete migration
        const success = completeMigration(filePath, config);
        
        if (success) {
          this.results.success.push(filePath);
          console.log(`✅ Successfully migrated: ${filePath}`);
        } else {
          this.results.failed.push(filePath);
          console.log(`❌ Failed to migrate: ${filePath}`);
        }

      } catch (error) {
        console.error(`❌ Error migrating ${filePath}:`, error.message);
        this.results.failed.push(filePath);
      }
    });
  }

  /**
   * Run post-migration validation
   */
  runPostValidation() {
    console.log('\n🔍 Running post-migration validation...');

    this.results.success.forEach(filePath => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check if migration was successful
        const hasHeaderTabContent = content.includes('HeaderTabContent');
        const hasUseHeaderTabs = content.includes('useHeaderTabs');
        const hasGetTabContent = content.includes('getTabContent');
        const hasStaticTabs = content.includes('<Tabs ') || content.includes('<TabsList');

        if (!hasHeaderTabContent || !hasUseHeaderTabs || !hasGetTabContent) {
          console.log(`⚠️  ${filePath}: Migration may be incomplete`);
          console.log(`    HeaderTabContent: ${hasHeaderTabContent ? '✅' : '❌'}`);
          console.log(`    useHeaderTabs: ${hasUseHeaderTabs ? '✅' : '❌'}`);
          console.log(`    getTabContent: ${hasGetTabContent ? '✅' : '❌'}`);
        }

        if (hasStaticTabs) {
          console.log(`⚠️  ${filePath}: Still contains static tabs - manual cleanup needed`);
        }

      } catch (error) {
        console.error(`❌ Error validating ${filePath}:`, error.message);
      }
    });
  }

  /**
   * Generate migration report
   */
  generateReport() {
    const duration = ((Date.now() - this.startTime) / 1000).toFixed(2);
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 MIGRATION SUMMARY REPORT');
    console.log('='.repeat(80));
    
    console.log(`⏱️  Duration: ${duration} seconds`);
    console.log(`📁 Total files processed: ${Object.keys(MIGRATION_CONFIG).length}`);
    console.log(`✅ Successfully migrated: ${this.results.success.length}`);
    console.log(`❌ Failed migrations: ${this.results.failed.length}`);
    console.log(`⏭️  Skipped (already migrated): ${this.results.skipped.length}`);
    console.log(`📋 Backup files created: ${this.results.backups.length}`);

    if (this.results.success.length > 0) {
      console.log('\n✅ Successfully migrated files:');
      this.results.success.forEach(file => console.log(`  - ${file}`));
    }

    if (this.results.failed.length > 0) {
      console.log('\n❌ Failed migrations:');
      this.results.failed.forEach(file => console.log(`  - ${file}`));
    }

    if (this.results.skipped.length > 0) {
      console.log('\n⏭️  Skipped files:');
      this.results.skipped.forEach(file => console.log(`  - ${file}`));
    }

    console.log('\n📝 Next Steps:');
    console.log('1. Review migrated files for any manual adjustments needed');
    console.log('2. Test each migrated page to ensure tabs work correctly');
    console.log('3. Run TypeScript diagnostics to fix any type errors');
    console.log('4. Remove backup files once satisfied with migration');
    console.log('5. Update any custom tab content in getTabContent functions');

    // Save detailed report to file
    const reportData = {
      timestamp: new Date().toISOString(),
      duration: `${duration}s`,
      results: this.results,
      config: MIGRATION_CONFIG
    };

    fs.writeFileSync('migration-report.json', JSON.stringify(reportData, null, 2));
    console.log('\n💾 Detailed report saved to: migration-report.json');
  }

  /**
   * Main execution method
   */
  async run() {
    console.log('🎯 Admin Tabs Migration Tool');
    console.log('=' .repeat(50));

    // Validation phase
    if (!this.validateConfig()) {
      process.exit(1);
    }

    if (!this.runPreChecks()) {
      process.exit(1);
    }

    // Backup phase
    const backupDir = this.createBackups();

    // Migration phase
    this.runMigration();

    // Validation phase
    this.runPostValidation();

    // Report phase
    this.generateReport();

    console.log('\n🎉 Migration process completed!');
    
    if (this.results.failed.length === 0) {
      console.log('🎊 All migrations successful!');
    } else {
      console.log(`⚠️  ${this.results.failed.length} migrations failed - check logs above`);
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  const runner = new MigrationRunner();
  runner.run().catch(error => {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  });
}

module.exports = { MigrationRunner };
