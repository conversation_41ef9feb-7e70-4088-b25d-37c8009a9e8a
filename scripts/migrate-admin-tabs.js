#!/usr/bin/env node

/**
 * Bulk migration script for converting admin pages from static tabs to header-integrated tabs
 * This script automates the migration process for all admin pages
 */

const fs = require('fs');
const path = require('path');

// Configuration for each admin page migration
const MIGRATION_CONFIG = {
  'app/admin/financial/page.tsx': {
    tabsConfig: 'getFinancialHeaderTabs',
    headerConfig: 'getFinancialHeaderConfig',
    tabs: ['overview', 'tco', 'roi', 'lease-buy', 'tax', 'budget']
  },
  'app/admin/maintenance/page.tsx': {
    tabsConfig: 'getMaintenanceHeaderTabs',
    headerConfig: 'getMaintenanceHeaderConfig',
    tabs: ['overview', 'scheduled', 'calendar', 'technicians', 'analytics']
  },
  'app/admin/asset-types/page.tsx': {
    tabsConfig: 'getAssetTypesHeaderTabs',
    headerConfig: 'getAssetTypesHeaderConfig',
    tabs: ['overview', 'types', 'categories', 'templates', 'analytics']
  },
  'app/admin/settings/page.tsx': {
    tabsConfig: 'getSettingsHeaderTabs',
    headerConfig: 'getSettingsHeaderConfig',
    tabs: ['general', 'users', 'customFields', 'formBuilder', 'notifications', 'integrations', 'security']
  },
  'app/admin/modules/inventory/page.tsx': {
    tabsConfig: 'getInventoryHeaderTabs',
    headerConfig: 'getInventoryHeaderConfig',
    tabs: ['overview', 'stock', 'movements', 'suppliers', 'analytics']
  },
  'app/admin/modules/asset-leasing/page.tsx': {
    tabsConfig: 'getAssetLeasingHeaderTabs',
    headerConfig: 'getAssetLeasingHeaderConfig',
    tabs: ['overview', 'contracts', 'payments', 'analytics']
  },
  'app/admin/reports/page.tsx': {
    tabsConfig: 'getReportsHeaderTabs',
    headerConfig: 'getReportsHeaderConfig',
    tabs: ['overview', 'financial', 'operational', 'compliance', 'custom']
  }
};

// Import replacements
const IMPORT_REPLACEMENTS = {
  // Remove static tabs import
  'import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"': '',
  
  // Add new imports
  'import { useState } from "react"': 'import { useState, useMemo } from "react"',
  'import { useState, useEffect } from "react"': 'import { useState, useEffect, useMemo } from "react"',
  'import { useAdminHeader } from "@/hooks/use-admin-header"': `import { useAdminHeader } from "@/hooks/use-admin-header"
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { HeaderTabContent } from "@/components/ui/header-tab-content"`,
};

function migrateFile(filePath, config) {
  console.log(`\n🔄 Migrating ${filePath}...`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ File not found: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Step 1: Update imports
  console.log('  📦 Updating imports...');
  Object.entries(IMPORT_REPLACEMENTS).forEach(([oldImport, newImport]) => {
    if (content.includes(oldImport)) {
      content = content.replace(oldImport, newImport);
    }
  });

  // Add tabs config import if not present
  const tabsConfigImport = `import { ${config.tabsConfig} } from "@/lib/utils/admin-tabs-configs"`;
  if (!content.includes(tabsConfigImport)) {
    const headerConfigImportMatch = content.match(/import.*admin-header-configs.*/);
    if (headerConfigImportMatch) {
      content = content.replace(
        headerConfigImportMatch[0],
        `${headerConfigImportMatch[0]}\n${tabsConfigImport}`
      );
    }
  }

  // Step 2: Update header setup
  console.log('  🎯 Updating header setup...');
  const headerSetupPattern = /useAdminHeader\(([^)]+)\)/;
  const headerSetupMatch = content.match(headerSetupPattern);
  
  if (headerSetupMatch) {
    const headerConfigCall = headerSetupMatch[1];
    const newHeaderSetup = `
  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => ${headerConfigCall}(), []);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => ${config.tabsConfig}(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "${config.tabs[0]}");`;

    content = content.replace(
      /\/\/ Set up the header for this page\s*useAdminHeader\([^)]+\)/,
      newHeaderSetup
    );
  }

  // Step 3: Add getTabContent function
  console.log('  🔧 Adding getTabContent function...');
  const getTabContentFunction = `
  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
${config.tabs.map(tab => `      case "${tab}":
        return (
          <div className="space-y-4">
            <div>Content for ${tab} tab - TODO: Move existing content here</div>
          </div>
        );`).join('\n')}
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };`;

  // Insert before the first function or component
  const functionPattern = /(const \w+Badge|const \w+Status|export default function)/;
  const functionMatch = content.match(functionPattern);
  
  if (functionMatch) {
    const insertIndex = content.indexOf(functionMatch[0]);
    content = content.slice(0, insertIndex) + getTabContentFunction + '\n\n  ' + content.slice(insertIndex);
  }

  // Step 4: Replace static tabs with HeaderTabContent
  console.log('  🔄 Replacing static tabs structure...');
  const tabsPattern = /<Tabs[^>]*>[\s\S]*?<\/Tabs>/;
  const tabsMatch = content.match(tabsPattern);
  
  if (tabsMatch) {
    const headerTabContent = `      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
${config.tabs.map(tab => `          { id: "${tab}", content: getTabContent("${tab}") },`).join('\n')}
        ]}
      />`;

    content = content.replace(tabsMatch[0], headerTabContent);
  }

  // Step 5: Write the migrated file
  console.log('  💾 Writing migrated file...');
  fs.writeFileSync(filePath, content);
  
  console.log(`✅ Successfully migrated ${filePath}`);
  return true;
}

function createBackup(filePath) {
  const backupPath = `${filePath}.backup.${Date.now()}`;
  fs.copyFileSync(filePath, backupPath);
  console.log(`📋 Created backup: ${backupPath}`);
  return backupPath;
}

function main() {
  console.log('🚀 Starting bulk admin tabs migration...\n');
  
  const results = {
    success: [],
    failed: [],
    backups: []
  };

  Object.entries(MIGRATION_CONFIG).forEach(([filePath, config]) => {
    try {
      // Create backup
      if (fs.existsSync(filePath)) {
        const backupPath = createBackup(filePath);
        results.backups.push(backupPath);
      }
      
      // Migrate file
      const success = migrateFile(filePath, config);
      
      if (success) {
        results.success.push(filePath);
      } else {
        results.failed.push(filePath);
      }
    } catch (error) {
      console.error(`❌ Error migrating ${filePath}:`, error.message);
      results.failed.push(filePath);
    }
  });

  // Print summary
  console.log('\n📊 Migration Summary:');
  console.log(`✅ Successfully migrated: ${results.success.length} files`);
  console.log(`❌ Failed migrations: ${results.failed.length} files`);
  console.log(`📋 Backups created: ${results.backups.length} files`);

  if (results.success.length > 0) {
    console.log('\n✅ Successfully migrated:');
    results.success.forEach(file => console.log(`  - ${file}`));
  }

  if (results.failed.length > 0) {
    console.log('\n❌ Failed migrations:');
    results.failed.forEach(file => console.log(`  - ${file}`));
  }

  console.log('\n🎉 Migration complete!');
  console.log('\n📝 Next steps:');
  console.log('1. Review migrated files and move existing tab content to getTabContent function');
  console.log('2. Test each page to ensure tabs work correctly');
  console.log('3. Remove backup files once satisfied with migration');
  console.log('4. Run diagnostics to fix any TypeScript errors');
}

if (require.main === module) {
  main();
}

module.exports = { migrateFile, MIGRATION_CONFIG };
