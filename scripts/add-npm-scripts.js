#!/usr/bin/env node

/**
 * Script to add migration scripts to package.json
 */

const fs = require('fs');
const path = require('path');

function addNpmScripts() {
  const packageJsonPath = 'package.json';
  
  if (!fs.existsSync(packageJsonPath)) {
    console.log('❌ package.json not found');
    return false;
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Add migration scripts
  if (!packageJson.scripts) {
    packageJson.scripts = {};
  }

  const migrationScripts = {
    'migrate:admin-tabs': 'node scripts/simple-migration.js',
    'migrate:admin-tabs-full': 'node scripts/run-migration.js',
    'migrate:extract-content': 'node scripts/migrate-tab-content.js'
  };

  Object.entries(migrationScripts).forEach(([scriptName, command]) => {
    if (!packageJson.scripts[scriptName]) {
      packageJson.scripts[scriptName] = command;
      console.log(`✅ Added script: ${scriptName}`);
    } else {
      console.log(`⏭️  Script already exists: ${scriptName}`);
    }
  });

  // Write back to package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
  console.log('💾 Updated package.json');

  return true;
}

function main() {
  console.log('📦 Adding migration scripts to package.json...\n');
  
  const success = addNpmScripts();
  
  if (success) {
    console.log('\n🎉 Migration scripts added successfully!');
    console.log('\nYou can now run:');
    console.log('  npm run migrate:admin-tabs        - Simple structure migration');
    console.log('  npm run migrate:admin-tabs-full   - Full migration with content extraction');
    console.log('  npm run migrate:extract-content   - Extract content only');
  }
}

if (require.main === module) {
  main();
}

module.exports = { addNpmScripts };
