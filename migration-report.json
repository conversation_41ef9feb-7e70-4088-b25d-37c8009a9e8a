{"timestamp": "2025-07-04T16:21:01.195Z", "duration": "0.06s", "results": {"success": [], "failed": ["app/admin/settings/page.tsx", "app/admin/modules/inventory/page.tsx", "app/admin/modules/asset-leasing/page.tsx", "app/admin/reports/page.tsx"], "backups": ["backups/admin-tabs-migration-1751646061143/page.tsx", "backups/admin-tabs-migration-1751646061143/page.tsx", "backups/admin-tabs-migration-1751646061143/page.tsx", "backups/admin-tabs-migration-1751646061143/page.tsx", "backups/admin-tabs-migration-1751646061143/page.tsx", "backups/admin-tabs-migration-1751646061143/page.tsx", "backups/admin-tabs-migration-1751646061143/page.tsx"], "skipped": ["app/admin/financial/page.tsx", "app/admin/maintenance/page.tsx", "app/admin/asset-types/page.tsx"]}, "config": {"app/admin/financial/page.tsx": {"tabsConfig": "getFinancialHeaderTabs", "headerConfig": "getFinancialHeaderConfig", "tabs": ["overview", "tco", "roi", "lease-buy", "tax", "budget"]}, "app/admin/maintenance/page.tsx": {"tabsConfig": "getMaintenanceHeaderTabs", "headerConfig": "getMaintenanceHeaderConfig", "tabs": ["overview", "scheduled", "calendar", "technicians", "analytics"]}, "app/admin/asset-types/page.tsx": {"tabsConfig": "getAssetTypesHeaderTabs", "headerConfig": "getAssetTypesHeaderConfig", "tabs": ["overview", "types", "categories", "templates", "analytics"]}, "app/admin/settings/page.tsx": {"tabsConfig": "getSettingsHeaderTabs", "headerConfig": "getSettingsHeaderConfig", "tabs": ["general", "users", "customFields", "formBuilder", "notifications", "integrations", "security"]}, "app/admin/modules/inventory/page.tsx": {"tabsConfig": "getInventoryHeaderTabs", "headerConfig": "getInventoryHeaderConfig", "tabs": ["overview", "stock", "movements", "suppliers", "analytics"]}, "app/admin/modules/asset-leasing/page.tsx": {"tabsConfig": "getAssetLeasingHeaderTabs", "headerConfig": "getAssetLeasingHeaderConfig", "tabs": ["overview", "contracts", "payments", "analytics"]}, "app/admin/reports/page.tsx": {"tabsConfig": "getReportsHeaderTabs", "headerConfig": "getReportsHeaderConfig", "tabs": ["overview", "financial", "operational", "compliance", "custom"]}}}