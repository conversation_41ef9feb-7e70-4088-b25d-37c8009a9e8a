"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useParams, useRouter } from "next/navigation";
import { AssetModuleEditor } from "@/components/asset-modules/asset-module-editor";
import { AssetModule } from "@/lib/types/asset-modules";
import { toast } from "@/components/ui/use-toast";

interface ModuleEditorPageProps {
  params: {
    moduleId: string;
  };
}

export default function ModuleEditorPage() {
  const params = useParams();
  const router = useRouter();
  const moduleId = params.moduleId as string;
  
  const [module, setModule] = useState<AssetModule | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Load module data
  useEffect(() => {
    if (moduleId === 'new') {
      // Create new module
      setModule(createDefaultModule());
      setIsLoading(false);
    } else {
      loadModule(moduleId);
    }
  }, [moduleId]);

  // Handle beforeunload to warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  const loadModule = async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-modules/${id}`);
      if (!response.ok) {
        throw new Error("Failed to load module");
      }

      const moduleData = await response.json();
      setModule(moduleData);
    } catch (error) {
      console.error("Error loading module:", error);
      setError("Failed to load module. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleModuleUpdate = useCallback((updates: Partial<AssetModule>) => {
    if (!module) return;

    setModule(prev => prev ? { ...prev, ...updates } : null);
    setHasUnsavedChanges(true);
  }, [module]);

  const handleSave = useCallback(async (moduleToSave: AssetModule) => {
    try {
      const method = moduleId === 'new' ? 'POST' : 'PUT';
      const url = moduleId === 'new' ? '/api/asset-modules' : `/api/asset-modules/${moduleId}`;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...moduleToSave,
          updatedAt: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save module');
      }

      const savedModule = await response.json();
      setModule(savedModule);
      setHasUnsavedChanges(false);

      toast({
        title: "Success",
        description: "Module saved successfully.",
      });

      // If this was a new module, redirect to the saved module's URL
      if (moduleId === 'new') {
        router.replace(`/module-editor/${savedModule.id}`);
      }

      return savedModule;
    } catch (error) {
      console.error("Error saving module:", error);
      toast({
        title: "Error",
        description: "Failed to save module.",
        variant: "destructive",
      });
      throw error;
    }
  }, [moduleId, router]);

  const handleClose = useCallback(() => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        'You have unsaved changes. Are you sure you want to leave?'
      );
      if (!confirmed) return;
    }

    // Navigate back to module management
    router.push('/admin/asset-modules');
  }, [hasUnsavedChanges, router]);

  const handleExport = useCallback((moduleToExport: AssetModule) => {
    const dataStr = JSON.stringify(moduleToExport, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${moduleToExport.name.replace(/\s+/g, '_')}_module.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  }, []);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading module editor...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-destructive mb-2">Error</h1>
          <p className="text-muted-foreground mb-4">{error}</p>
          <button
            onClick={() => router.push('/admin/asset-modules')}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Back to Modules
          </button>
        </div>
      </div>
    );
  }

  if (!module) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-2">Module Not Found</h1>
          <p className="text-muted-foreground mb-4">The requested module could not be found.</p>
          <button
            onClick={() => router.push('/admin/asset-modules')}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Back to Modules
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-background">
      <AssetModuleEditor
        module={module}
        onUpdate={handleModuleUpdate}
        onSave={handleSave}
        onClose={handleClose}
        onExport={handleExport}
        hasUnsavedChanges={hasUnsavedChanges}
        isNewModule={moduleId === 'new'}
      />
    </div>
  );
}

// Helper function to create a default module
function createDefaultModule(): AssetModule {
  return {
    id: `module-${Date.now()}`,
    name: "New Asset Module",
    version: "1.0.0",
    description: "",
    category: "custom",
    author: "current-user", // TODO: Get from auth context
    tags: [],
    fields: [],
    logic: {
      nodes: [],
      edges: [],
      variables: [],
      functions: [],
    },
    rendering: {
      formLayout: {
        sections: [],
        columns: 1,
        spacing: "normal",
        grouping: [],
      },
      displayLayout: {
        views: [],
        defaultView: "card",
      },
      components: [],
    },
    validation: {
      rules: [],
      crossFieldValidation: [],
    },
    isActive: false,
    isBuiltIn: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    usageCount: 0,
    dependencies: [],
    compatibleAssetTypes: [],
    requiredPermissions: [],
  };
}
