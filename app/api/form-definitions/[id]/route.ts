import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/form-definitions/[id] - Get specific form definition
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const formId = params.id;

    if (!formId) {
      return NextResponse.json(
        { error: "Form ID is required" },
        { status: 400 }
      );
    }

    const form = await prisma.formDefinition.findUnique({
      where: { id: formId },
      include: {
        assetTypeForms: {
          include: {
            assetType: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
          },
        },
      },
    });

    if (!form) {
      return NextResponse.json(
        { error: "Form definition not found" },
        { status: 404 }
      );
    }

    // Parse JSON fields
    const parsedForm = {
      ...form,
      sections: JSON.parse(form.sections as string),
      settings: JSON.parse(form.settings as string),
    };

    return NextResponse.json(parsedForm);

  } catch (error) {
    console.error("Error fetching form definition:", error);
    return NextResponse.json(
      { error: "Failed to fetch form definition" },
      { status: 500 }
    );
  }
}

// PUT /api/form-definitions/[id] - Update specific form definition
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const formId = params.id;
    const body = await request.json();

    if (!formId) {
      return NextResponse.json(
        { error: "Form ID is required" },
        { status: 400 }
      );
    }

    const { name, description, sections, settings, updatedBy } = body;

    // Build update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (sections !== undefined) updateData.sections = JSON.stringify(sections);
    if (settings !== undefined) updateData.settings = JSON.stringify(settings);
    if (updatedBy !== undefined) updateData.updatedBy = updatedBy;

    // Increment version number
    const currentForm = await prisma.formDefinition.findUnique({
      where: { id: formId },
      select: { version: true },
    });

    if (!currentForm) {
      return NextResponse.json(
        { error: "Form definition not found" },
        { status: 404 }
      );
    }

    updateData.version = currentForm.version + 1;

    const updatedForm = await prisma.formDefinition.update({
      where: { id: formId },
      data: updateData,
    });

    // Parse JSON fields for response
    const parsedForm = {
      ...updatedForm,
      sections: JSON.parse(updatedForm.sections as string),
      settings: JSON.parse(updatedForm.settings as string),
    };

    return NextResponse.json(parsedForm);

  } catch (error) {
    console.error("Error updating form definition:", error);
    return NextResponse.json(
      { error: "Failed to update form definition" },
      { status: 500 }
    );
  }
}

// DELETE /api/form-definitions/[id] - Delete specific form definition
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const formId = params.id;

    if (!formId) {
      return NextResponse.json(
        { error: "Form ID is required" },
        { status: 400 }
      );
    }

    // Check if form is being used
    const usage = await prisma.assetTypeForm.findFirst({
      where: { formId },
    });

    if (usage) {
      return NextResponse.json(
        { error: "Cannot delete form that is currently in use" },
        { status: 400 }
      );
    }

    await prisma.formDefinition.delete({
      where: { id: formId },
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error("Error deleting form definition:", error);
    return NextResponse.json(
      { error: "Failed to delete form definition" },
      { status: 500 }
    );
  }
}
