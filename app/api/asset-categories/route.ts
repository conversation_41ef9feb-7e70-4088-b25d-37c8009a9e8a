import { NextRequest, NextResponse } from "next/server";
import { AssetTypeDbService } from "@/lib/modules/asset-types/db-service";

const assetTypeService = AssetTypeDbService.getInstance();

// GET /api/asset-categories - Get all asset categories
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const parentId = searchParams.get("parentId") || undefined;

    let categories;
    if (parentId) {
      categories = await assetTypeService.getCategories(parentId);
    } else {
      categories = await assetTypeService.getAllCategories();
    }
    
    return NextResponse.json(categories);
  } catch (error) {
    console.error("Failed to fetch asset categories:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

// POST /api/asset-categories - Create new asset category
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: "Category name is required" },
        { status: 400 }
      );
    }

    const categoryData = {
      name: body.name,
      description: body.description || "",
      parentId: body.parentId || null,
      level: body.level || 0,
      path: body.path || body.name.toLowerCase().replace(/\s+/g, "_"),
      isActive: body.isActive !== undefined ? body.isActive : true,
    };

    const category = await assetTypeService.createCategory(categoryData);
    
    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    console.error("Failed to create asset category:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal Server Error" },
      { status: 400 }
    );
  }
}
