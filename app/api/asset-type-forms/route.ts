import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { FormRuntimeEngine } from "@/lib/engines/form-runtime-engine";
import { AssetOperationType } from "@/lib/types/asset-type-forms";

// GET /api/asset-type-forms - Get form for specific asset type and operation, or all forms for asset type
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetTypeId = searchParams.get("assetTypeId");
    const operationType = searchParams.get("operationType") as AssetOperationType;
    const assetId = searchParams.get("assetId");
    const userId = searchParams.get("userId");
    const userRole = searchParams.get("userRole");
    const getAllForms = searchParams.get("getAllForms") === "true";

    if (!assetTypeId) {
      return NextResponse.json(
        { error: "Asset type ID is required" },
        { status: 400 }
      );
    }

    // If getAllForms is true, return all forms for the asset type
    if (getAllForms) {
      const forms = await prisma.assetTypeForm.findMany({
        where: { assetTypeId },
        include: {
          form: {
            select: {
              id: true,
              name: true,
              description: true,
              sections: true,
              settings: true,
              version: true,
              isActive: true,
              createdAt: true,
              updatedAt: true,
            },
          },
        },
        orderBy: [
          { operationType: "asc" },
          { isDefault: "desc" },
          { createdAt: "desc" },
        ],
      });

      // Parse JSON fields and format response
      const formattedForms = forms.map((form) => ({
        ...form,
        form: {
          ...form.form,
          sections: JSON.parse(form.form.sections as string),
          settings: JSON.parse(form.form.settings as string),
        },
      }));

      return NextResponse.json(formattedForms);
    }

    if (!operationType || !userId || !userRole) {
      return NextResponse.json(
        { error: "Missing required parameters for form operation" },
        { status: 400 }
      );
    }
    
    const context = {
      assetId: assetId || undefined,
      assetTypeId,
      operationType,
      userId,
      userRole,
    };
    
    // Get form definition
    const form = await FormRuntimeEngine.getFormForOperation(
      assetTypeId,
      operationType,
      context
    );
    
    if (!form) {
      return NextResponse.json(
        { error: "No form found for this asset type and operation" },
        { status: 404 }
      );
    }
    
    // Get pre-population data
    const prePopulationData = await FormRuntimeEngine.getPrePopulationData(
      assetId || undefined,
      assetTypeId,
      operationType,
      context
    );
    
    return NextResponse.json({
      form,
      prePopulationData,
    });
    
  } catch (error) {
    console.error("Error getting asset type form:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/asset-type-forms - Submit form data
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { formId, assetTypeId, operationType, data, context } = body;
    
    if (!formId || !assetTypeId || !operationType || !data || !context) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // Process form submission
    const result = await FormRuntimeEngine.processFormSubmission({
      formId,
      assetTypeId,
      operationType,
      data,
      context,
    });
    
    if (!result.success) {
      return NextResponse.json(
        { 
          error: "Form submission failed",
          errors: result.errors,
          validationResults: result.validationResults,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: result.data,
      warnings: result.warnings,
    });
    
  } catch (error) {
    console.error("Error processing form submission:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/asset-type-forms - Associate form with asset type and operation
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { assetTypeId, formId, operationType, isDefault, createdBy } = body;
    
    if (!assetTypeId || !formId || !operationType || !createdBy) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // If setting as default, unset other defaults for this asset type and operation
    if (isDefault) {
      await prisma.assetTypeForm.updateMany({
        where: {
          assetTypeId,
          operationType,
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });
    }
    
    // Check if there's already an association for this asset type and operation
    const existingAssociation = await prisma.assetTypeForm.findFirst({
      where: {
        assetTypeId,
        operationType,
      },
    });

    let assetTypeForm;

    if (existingAssociation) {
      // Update existing association
      assetTypeForm = await prisma.assetTypeForm.update({
        where: {
          id: existingAssociation.id,
        },
        data: {
          formId,
          isDefault: isDefault || false,
          isActive: true,
          updatedAt: new Date(),
        },
        include: {
          form: true,
          assetType: true,
        },
      });
    } else {
      // Create new association
      assetTypeForm = await prisma.assetTypeForm.create({
        data: {
          assetTypeId,
          formId,
          operationType,
          isDefault: isDefault || false,
          isActive: true,
          createdBy,
        },
        include: {
          form: true,
          assetType: true,
        },
      });
    }
    
    return NextResponse.json(assetTypeForm);

  } catch (error) {
    console.error("Error associating form with asset type:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/asset-type-forms - Remove form association
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetTypeId = searchParams.get("assetTypeId");
    const operationType = searchParams.get("operationType");
    const associationId = searchParams.get("id");

    if (!assetTypeId && !associationId) {
      return NextResponse.json(
        { error: "Either assetTypeId with operationType or association ID is required" },
        { status: 400 }
      );
    }

    let whereClause: any;

    if (associationId) {
      whereClause = { id: associationId };
    } else {
      whereClause = {
        assetTypeId: assetTypeId!,
        operationType: operationType!,
      };
    }

    const deletedAssociation = await prisma.assetTypeForm.delete({
      where: whereClause,
      include: {
        form: true,
        assetType: true,
      },
    });

    return NextResponse.json({
      success: true,
      deletedAssociation,
    });

  } catch (error) {
    console.error("Error removing form association:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}