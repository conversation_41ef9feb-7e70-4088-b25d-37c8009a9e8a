import { NextRequest, NextResponse } from "next/server";
import { AssetTypeFormService } from "@/lib/services/asset-type-form-service";

// GET /api/asset-type-forms/[id] - Get specific asset type form by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const formId = params.id;

    if (!formId) {
      return NextResponse.json(
        { error: "Form ID is required" },
        { status: 400 }
      );
    }

    // Get the asset type form with its associated form definition
    const assetTypeForm = await AssetTypeFormService.getAssetTypeFormById(formId);

    if (!assetTypeForm) {
      return NextResponse.json(
        { error: "Asset type form not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(assetTypeForm);

  } catch (error) {
    console.error("Error fetching asset type form:", error);
    return NextResponse.json(
      { error: "Failed to fetch asset type form" },
      { status: 500 }
    );
  }
}

// PUT /api/asset-type-forms/[id] - Update specific asset type form
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const formId = params.id;
    const body = await request.json();

    if (!formId) {
      return NextResponse.json(
        { error: "Form ID is required" },
        { status: 400 }
      );
    }

    // Update the asset type form
    const updatedForm = await AssetTypeFormService.updateAssetTypeForm(formId, body);

    if (!updatedForm) {
      return NextResponse.json(
        { error: "Asset type form not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedForm);

  } catch (error) {
    console.error("Error updating asset type form:", error);
    return NextResponse.json(
      { error: "Failed to update asset type form" },
      { status: 500 }
    );
  }
}

// DELETE /api/asset-type-forms/[id] - Delete specific asset type form
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const formId = params.id;

    if (!formId) {
      return NextResponse.json(
        { error: "Form ID is required" },
        { status: 400 }
      );
    }

    // Delete the asset type form
    const deleted = await AssetTypeFormService.deleteAssetTypeForm(formId);

    if (!deleted) {
      return NextResponse.json(
        { error: "Asset type form not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error("Error deleting asset type form:", error);
    return NextResponse.json(
      { error: "Failed to delete asset type form" },
      { status: 500 }
    );
  }
}
