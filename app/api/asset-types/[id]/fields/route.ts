import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// GET /api/asset-types/[id]/fields - Get custom fields for asset type
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const assetTypeId = params.id;

    // Get asset type with custom fields
    const assetType = await prisma.assetType.findUnique({
      where: { id: assetTypeId },
      include: {
        customFields: {
          orderBy: { order: 'asc' },
        },
      },
    });

    if (!assetType) {
      return NextResponse.json(
        { error: "Asset type not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(assetType.customFields);
  } catch (error) {
    console.error("Error fetching asset type fields:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}