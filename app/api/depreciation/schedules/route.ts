import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { PaginationSchema } from "@/lib/schemas/api";
import { z } from "zod";

// GET /api/depreciation/schedules - Get depreciation schedules
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const params = Object.fromEntries(searchParams.entries());

    // Validate pagination parameters
    const pagination = PaginationSchema.parse(params);
    
    // Build where clause
    const where: any = {};

    // Filter by asset ID
    if (params.assetId) {
      where.assetId = params.assetId;
    }

    // Filter by asset IDs (comma-separated)
    if (params.assetIds) {
      where.assetId = { in: params.assetIds.split(',') };
    }

    // Filter by year
    if (params.year) {
      where.year = parseInt(params.year);
    }

    // Filter by year range
    if (params.yearFrom || params.yearTo) {
      where.year = {};
      if (params.yearFrom) {
        where.year.gte = parseInt(params.yearFrom);
      }
      if (params.yearTo) {
        where.year.lte = parseInt(params.yearTo);
      }
    }

    // Filter by depreciation method
    if (params.method) {
      where.method = params.method;
    }

    // Filter by actual vs projected
    if (params.isActual !== undefined) {
      where.isActual = params.isActual === 'true';
    }

    // Get total count
    const total = await prisma.depreciationSchedule.count({ where });

    // Calculate pagination
    const totalPages = Math.ceil(total / pagination.limit);
    const skip = (pagination.page - 1) * pagination.limit;

    // Fetch schedules with asset information
    const schedules = await prisma.depreciationSchedule.findMany({
      where,
      include: {
        asset: {
          select: {
            id: true,
            name: true,
            category: true,
            purchasePrice: true,
            purchaseDate: true,
            assetType: {
              select: {
                id: true,
                name: true,
                depreciationSettings: true,
              },
            },
          },
        },
      },
      orderBy: [
        { year: pagination.sortOrder === 'desc' ? 'desc' : 'asc' },
        { month: pagination.sortOrder === 'desc' ? 'desc' : 'asc' },
      ],
      skip,
      take: pagination.limit,
    });

    return NextResponse.json({
      success: true,
      data: schedules,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
        hasNext: pagination.page < totalPages,
        hasPrev: pagination.page > 1,
      },
    });

  } catch (error) {
    console.error("Error fetching depreciation schedules:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Failed to fetch schedules",
      },
      { status: 500 }
    );
  }
}

// DELETE /api/depreciation/schedules - Delete depreciation schedules
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetId = searchParams.get("assetId");
    const assetIds = searchParams.get("assetIds");

    if (!assetId && !assetIds) {
      return NextResponse.json(
        {
          success: false,
          error: "Asset ID or asset IDs are required",
        },
        { status: 400 }
      );
    }

    let where: any = {};

    if (assetId) {
      where.assetId = assetId;
    } else if (assetIds) {
      where.assetId = { in: assetIds.split(',') };
    }

    // Delete schedules
    const result = await prisma.depreciationSchedule.deleteMany({ where });

    return NextResponse.json({
      success: true,
      message: `Deleted ${result.count} depreciation schedule entries`,
      deletedCount: result.count,
    });

  } catch (error) {
    console.error("Error deleting depreciation schedules:", error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete schedules",
      },
      { status: 500 }
    );
  }
}
