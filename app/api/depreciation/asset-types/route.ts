import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { 
  AssetTypeSchema,
  DepreciationSettingsSchema,
  DepreciationSettingsCreateSchema,
  DepreciationSettingsUpdateSchema 
} from "@/lib/schemas/validation";
import { PaginationSchema } from "@/lib/schemas/api";
import { z } from "zod";

// GET /api/depreciation/asset-types - Get asset types with depreciation settings
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const params = Object.fromEntries(searchParams.entries());

    // Validate pagination parameters
    const pagination = PaginationSchema.parse(params);
    
    // Build where clause
    const where: any = {
      isActive: true,
    };

    // Filter by category if provided
    if (params.categoryId) {
      where.categoryId = params.categoryId;
    }

    // Filter by whether depreciation settings exist
    if (params.hasDepreciationSettings === 'true') {
      where.depreciationSettings = { isNot: null };
    } else if (params.hasDepreciationSettings === 'false') {
      where.depreciationSettings = null;
    }

    // Search by name or code
    if (params.search) {
      where.OR = [
        { name: { contains: params.search, mode: 'insensitive' } },
        { code: { contains: params.search, mode: 'insensitive' } },
        { description: { contains: params.search, mode: 'insensitive' } },
      ];
    }

    // Get total count
    const total = await prisma.assetType.count({ where });

    // Calculate pagination
    const totalPages = Math.ceil(total / pagination.limit);
    const skip = (pagination.page - 1) * pagination.limit;

    // Fetch asset types with depreciation settings
    const assetTypes = await prisma.assetType.findMany({
      where,
      include: {
        category: true,
        depreciationSettings: true,
        _count: {
          select: {
            assets: true,
          },
        },
      },
      orderBy: {
        [pagination.sortBy || 'name']: pagination.sortOrder,
      },
      skip,
      take: pagination.limit,
    });

    // Transform the data to include parsed JSON fields
    const transformedAssetTypes = assetTypes.map(assetType => ({
      ...assetType,
      depreciationSettings: assetType.depreciationSettings ? {
        ...assetType.depreciationSettings,
        customRates: assetType.depreciationSettings.customRates 
          ? JSON.parse(assetType.depreciationSettings.customRates) 
          : undefined,
        acceleratedDepreciation: assetType.depreciationSettings.acceleratedDepreciation
          ? JSON.parse(assetType.depreciationSettings.acceleratedDepreciation)
          : undefined,
        impairmentSettings: assetType.depreciationSettings.impairmentSettings
          ? JSON.parse(assetType.depreciationSettings.impairmentSettings)
          : undefined,
      } : null,
    }));

    return NextResponse.json({
      success: true,
      data: transformedAssetTypes,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
        hasNext: pagination.page < totalPages,
        hasPrev: pagination.page > 1,
      },
    });

  } catch (error) {
    console.error("Error fetching asset types with depreciation settings:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Failed to fetch asset types",
      },
      { status: 500 }
    );
  }
}

// POST /api/depreciation/asset-types - Create or update depreciation settings for an asset type
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { assetTypeId, ...depreciationData } = body;

    if (!assetTypeId) {
      return NextResponse.json(
        {
          success: false,
          error: "Asset type ID is required",
        },
        { status: 400 }
      );
    }

    // Validate asset type exists
    const assetType = await prisma.assetType.findUnique({
      where: { id: assetTypeId },
    });

    if (!assetType) {
      return NextResponse.json(
        {
          success: false,
          error: "Asset type not found",
        },
        { status: 404 }
      );
    }

    // Validate depreciation settings data
    const validatedData = DepreciationSettingsCreateSchema.parse({
      ...depreciationData,
      assetTypeId,
      // Convert complex objects to JSON strings for storage
      customRates: depreciationData.customRates 
        ? JSON.stringify(depreciationData.customRates) 
        : undefined,
      acceleratedDepreciation: depreciationData.acceleratedDepreciation
        ? JSON.stringify(depreciationData.acceleratedDepreciation)
        : undefined,
      impairmentSettings: depreciationData.impairmentSettings
        ? JSON.stringify(depreciationData.impairmentSettings)
        : undefined,
    });

    // Create or update depreciation settings
    const depreciationSettings = await prisma.depreciationSettings.upsert({
      where: { assetTypeId },
      update: validatedData,
      create: validatedData,
    });

    return NextResponse.json({
      success: true,
      data: depreciationSettings,
      message: "Depreciation settings saved successfully",
    });

  } catch (error) {
    console.error("Error saving depreciation settings:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid depreciation settings data",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Failed to save depreciation settings",
      },
      { status: 500 }
    );
  }
}

// DELETE /api/depreciation/asset-types - Remove depreciation settings for an asset type
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assetTypeId = searchParams.get("assetTypeId");

    if (!assetTypeId) {
      return NextResponse.json(
        {
          success: false,
          error: "Asset type ID is required",
        },
        { status: 400 }
      );
    }

    // Check if depreciation settings exist
    const existingSettings = await prisma.depreciationSettings.findUnique({
      where: { assetTypeId },
    });

    if (!existingSettings) {
      return NextResponse.json(
        {
          success: false,
          error: "Depreciation settings not found",
        },
        { status: 404 }
      );
    }

    // Delete depreciation settings
    await prisma.depreciationSettings.delete({
      where: { assetTypeId },
    });

    return NextResponse.json({
      success: true,
      message: "Depreciation settings removed successfully",
    });

  } catch (error) {
    console.error("Error removing depreciation settings:", error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Failed to remove depreciation settings",
      },
      { status: 500 }
    );
  }
}
