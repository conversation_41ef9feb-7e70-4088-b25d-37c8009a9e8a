import { NextRequest, NextResponse } from "next/server";
import { DepreciationService } from "@/lib/services/depreciation-service";
import { DepreciationReportFilterSchema } from "@/lib/schemas/depreciation";
import { z } from "zod";

// GET /api/depreciation/statistics - Get depreciation statistics
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const params = Object.fromEntries(searchParams.entries());

    // Validate filter parameters
    const filter = DepreciationReportFilterSchema.parse({
      assetIds: params.assetIds ? params.assetIds.split(',') : undefined,
      assetTypeIds: params.assetTypeIds ? params.assetTypeIds.split(',') : undefined,
      categories: params.categories ? params.categories.split(',') : undefined,
      departments: params.departments ? params.departments.split(',') : undefined,
      locations: params.locations ? params.locations.split(',') : undefined,
      methods: params.methods ? params.methods.split(',') : undefined,
      dateFrom: params.dateFrom ? new Date(params.dateFrom) : undefined,
      dateTo: params.dateTo ? new Date(params.dateTo) : undefined,
      includeFullyDepreciated: params.includeFullyDepreciated !== 'false',
      includeDisposed: params.includeDisposed === 'true',
    });

    // Get statistics
    const statistics = await DepreciationService.getDepreciationStatistics(filter);

    // Get additional breakdown data
    const { assets } = await DepreciationService.getAssetsWithDepreciation(filter, {
      page: 1,
      limit: 1000, // Get all for statistics
    });

    // Calculate method breakdown
    const methodBreakdown = assets.reduce((acc, item) => {
      if (item.depreciation) {
        const method = item.depreciation.depreciationMethod;
        if (!acc[method]) {
          acc[method] = {
            count: 0,
            totalValue: 0,
            totalDepreciation: 0,
          };
        }
        acc[method].count++;
        acc[method].totalValue += item.asset.purchasePrice;
        acc[method].totalDepreciation += item.depreciation.accumulatedDepreciation;
      }
      return acc;
    }, {} as Record<string, { count: number; totalValue: number; totalDepreciation: number }>);

    // Calculate category breakdown
    const categoryBreakdown = assets.reduce((acc, item) => {
      const category = item.asset.category;
      if (!acc[category]) {
        acc[category] = {
          count: 0,
          totalValue: 0,
          totalDepreciation: 0,
        };
      }
      acc[category].count++;
      acc[category].totalValue += item.asset.purchasePrice;
      if (item.depreciation) {
        acc[category].totalDepreciation += item.depreciation.accumulatedDepreciation;
      }
      return acc;
    }, {} as Record<string, { count: number; totalValue: number; totalDepreciation: number }>);

    // Calculate age distribution
    const currentYear = new Date().getFullYear();
    const ageDistribution = assets.reduce((acc, item) => {
      const purchaseYear = new Date(item.asset.purchaseDate).getFullYear();
      const age = currentYear - purchaseYear;
      const ageGroup = age < 1 ? '< 1 year' : 
                     age < 3 ? '1-3 years' :
                     age < 5 ? '3-5 years' :
                     age < 10 ? '5-10 years' : '> 10 years';
      
      if (!acc[ageGroup]) {
        acc[ageGroup] = {
          count: 0,
          totalValue: 0,
        };
      }
      acc[ageGroup].count++;
      acc[ageGroup].totalValue += item.asset.purchasePrice;
      return acc;
    }, {} as Record<string, { count: number; totalValue: number }>);

    // Calculate monthly depreciation trend (last 12 months)
    const monthlyTrend = [];
    const currentDate = new Date();
    
    for (let i = 11; i >= 0; i--) {
      const date = new Date(currentDate);
      date.setMonth(date.getMonth() - i);
      
      const monthlyDepreciation = assets.reduce((sum, item) => {
        if (item.schedule) {
          const monthEntry = item.schedule.find(entry => 
            entry.year === date.getFullYear() && 
            entry.month === date.getMonth() + 1
          );
          return sum + (monthEntry?.depreciationAmount || 0);
        }
        return sum;
      }, 0);

      monthlyTrend.push({
        month: date.toISOString().slice(0, 7), // YYYY-MM format
        depreciation: monthlyDepreciation,
      });
    }

    // Assets requiring attention
    const assetsRequiringAttention = assets.filter(item => {
      if (!item.depreciation) return false;
      
      // Fully depreciated assets
      if (item.depreciation.remainingUsefulLife <= 0) return true;
      
      // Assets nearing end of life (< 1 year remaining)
      if (item.depreciation.remainingUsefulLife < 1) return true;
      
      return false;
    });

    const enhancedStatistics = {
      ...statistics,
      methodBreakdown,
      categoryBreakdown,
      ageDistribution,
      monthlyTrend,
      assetsRequiringAttention: {
        count: assetsRequiringAttention.length,
        totalValue: assetsRequiringAttention.reduce((sum, item) => sum + item.asset.purchasePrice, 0),
        assets: assetsRequiringAttention.slice(0, 10).map(item => ({
          id: item.asset.id,
          name: item.asset.name,
          category: item.asset.category,
          remainingLife: item.depreciation?.remainingUsefulLife || 0,
          currentValue: item.depreciation?.currentBookValue || 0,
        })),
      },
    };

    return NextResponse.json({
      success: true,
      data: enhancedStatistics,
    });

  } catch (error) {
    console.error("Error fetching depreciation statistics:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid filter parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Failed to fetch statistics",
      },
      { status: 500 }
    );
  }
}
