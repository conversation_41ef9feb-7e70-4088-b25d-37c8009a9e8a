import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { 
  DepreciationReportFilterSchema,
  AssetDepreciationSummarySchema 
} from "@/lib/schemas/depreciation";
import { PaginationSchema } from "@/lib/schemas/api";
import { z } from "zod";

// GET /api/depreciation/assets - Get assets with depreciation data
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const params = Object.fromEntries(searchParams.entries());

    // Validate pagination parameters
    const pagination = PaginationSchema.parse(params);
    
    // Validate filter parameters
    const filter = DepreciationReportFilterSchema.parse({
      assetIds: params.assetIds ? params.assetIds.split(',') : undefined,
      assetTypeIds: params.assetTypeIds ? params.assetTypeIds.split(',') : undefined,
      categories: params.categories ? params.categories.split(',') : undefined,
      departments: params.departments ? params.departments.split(',') : undefined,
      locations: params.locations ? params.locations.split(',') : undefined,
      methods: params.methods ? params.methods.split(',') : undefined,
      dateFrom: params.dateFrom ? new Date(params.dateFrom) : undefined,
      dateTo: params.dateTo ? new Date(params.dateTo) : undefined,
      includeFullyDepreciated: params.includeFullyDepreciated !== 'false',
      includeDisposed: params.includeDisposed === 'true',
    });

    // Build where clause
    const where: any = {};

    if (filter.assetIds?.length) {
      where.id = { in: filter.assetIds };
    }

    if (filter.assetTypeIds?.length) {
      where.assetTypeId = { in: filter.assetTypeIds };
    }

    if (filter.categories?.length) {
      where.category = { in: filter.categories };
    }

    if (filter.departments?.length) {
      where.department = { in: filter.departments };
    }

    if (filter.locations?.length) {
      where.location = { in: filter.locations };
    }

    if (!filter.includeDisposed) {
      where.status = { not: "disposed" };
    }

    if (filter.dateFrom || filter.dateTo) {
      where.purchaseDate = {};
      if (filter.dateFrom) {
        where.purchaseDate.gte = filter.dateFrom;
      }
      if (filter.dateTo) {
        where.purchaseDate.lte = filter.dateTo;
      }
    }

    // Add depreciation settings filter
    if (filter.methods?.length) {
      where.assetType = {
        depreciationSettings: {
          method: { in: filter.methods }
        }
      };
    }

    // Get total count
    const total = await prisma.asset.count({ where });

    // Calculate pagination
    const totalPages = Math.ceil(total / pagination.limit);
    const skip = (pagination.page - 1) * pagination.limit;

    // Fetch assets with depreciation data
    const assets = await prisma.asset.findMany({
      where,
      include: {
        assetType: {
          include: {
            depreciationSettings: true,
          },
        },
        depreciationSchedule: {
          orderBy: [
            { year: 'asc' },
            { month: 'asc' }
          ],
          take: 12, // Last 12 months
        },
      },
      orderBy: {
        [pagination.sortBy || 'createdAt']: pagination.sortOrder,
      },
      skip,
      take: pagination.limit,
    });

    // Calculate depreciation summaries
    const assetSummaries = await Promise.all(
      assets.map(async (asset) => {
        if (!asset.assetType?.depreciationSettings) {
          return null;
        }

        // Calculate current book value and accumulated depreciation
        const latestSchedule = asset.depreciationSchedule[asset.depreciationSchedule.length - 1];
        const currentBookValue = latestSchedule?.bookValue ?? asset.purchasePrice;
        const accumulatedDepreciation = asset.purchasePrice - currentBookValue;

        // Calculate remaining depreciable amount
        const settings = asset.assetType.depreciationSettings;
        const salvageValue = settings.salvageValueType === 'percentage' 
          ? asset.purchasePrice * (settings.salvageValue / 100)
          : settings.salvageValue;
        
        const remainingDepreciableAmount = Math.max(0, currentBookValue - salvageValue);

        // Calculate remaining useful life
        const purchaseDate = new Date(asset.purchaseDate);
        const currentDate = new Date();
        const elapsedYears = (currentDate.getTime() - purchaseDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000);
        const remainingUsefulLife = Math.max(0, settings.usefulLife - elapsedYears);

        // Calculate next depreciation date
        const nextDepreciationDate = new Date(currentDate);
        nextDepreciationDate.setMonth(nextDepreciationDate.getMonth() + 1, 1);

        const summary: z.infer<typeof AssetDepreciationSummarySchema> = {
          assetId: asset.id,
          assetName: asset.name,
          purchasePrice: asset.purchasePrice,
          currentBookValue,
          accumulatedDepreciation,
          remainingDepreciableAmount,
          depreciationMethod: settings.method as any,
          usefulLife: settings.usefulLife,
          remainingUsefulLife,
          salvageValue,
          lastCalculatedAt: latestSchedule?.calculatedAt ?? asset.createdAt,
          nextDepreciationDate: remainingUsefulLife > 0 ? nextDepreciationDate : undefined,
        };

        return {
          asset: {
            id: asset.id,
            name: asset.name,
            category: asset.category,
            location: asset.location,
            department: asset.department,
            status: asset.status,
            purchaseDate: asset.purchaseDate,
            purchasePrice: asset.purchasePrice,
            serialNumber: asset.serialNumber,
            assetType: asset.assetType,
          },
          depreciation: summary,
          schedule: asset.depreciationSchedule,
        };
      })
    );

    // Filter out assets without depreciation settings
    const validSummaries = assetSummaries.filter(Boolean);

    // Apply fully depreciated filter if needed
    const filteredSummaries = filter.includeFullyDepreciated 
      ? validSummaries
      : validSummaries.filter(summary => summary!.depreciation.remainingUsefulLife > 0);

    return NextResponse.json({
      success: true,
      data: filteredSummaries,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
        hasNext: pagination.page < totalPages,
        hasPrev: pagination.page > 1,
      },
    });

  } catch (error) {
    console.error("Error fetching assets with depreciation data:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Failed to fetch assets",
      },
      { status: 500 }
    );
  }
}
