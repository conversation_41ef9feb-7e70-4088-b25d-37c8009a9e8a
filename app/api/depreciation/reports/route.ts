import { NextRequest, NextResponse } from "next/server";
import { DepreciationService } from "@/lib/services/depreciation-service";
import { DepreciationReportFilterSchema } from "@/lib/schemas/depreciation";
import { PaginationSchema } from "@/lib/schemas/api";
import { z } from "zod";

// GET /api/depreciation/reports - Generate depreciation reports
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const params = Object.fromEntries(searchParams.entries());

    // Validate parameters
    const pagination = PaginationSchema.parse(params);
    const filter = DepreciationReportFilterSchema.parse({
      assetIds: params.assetIds ? params.assetIds.split(',') : undefined,
      assetTypeIds: params.assetTypeIds ? params.assetTypeIds.split(',') : undefined,
      categories: params.categories ? params.categories.split(',') : undefined,
      departments: params.departments ? params.departments.split(',') : undefined,
      locations: params.locations ? params.locations.split(',') : undefined,
      methods: params.methods ? params.methods.split(',') : undefined,
      dateFrom: params.dateFrom ? new Date(params.dateFrom) : undefined,
      dateTo: params.dateTo ? new Date(params.dateTo) : undefined,
      includeFullyDepreciated: params.includeFullyDepreciated !== 'false',
      includeDisposed: params.includeDisposed === 'true',
    });

    const reportType = params.reportType || 'summary';

    switch (reportType) {
      case 'summary':
        return generateSummaryReport(filter, pagination);
      case 'detailed':
        return generateDetailedReport(filter, pagination);
      case 'schedule':
        return generateScheduleReport(filter, pagination);
      case 'comparison':
        return generateComparisonReport(filter, pagination);
      default:
        return NextResponse.json(
          {
            success: false,
            error: "Invalid report type. Supported types: summary, detailed, schedule, comparison",
          },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error("Error generating depreciation report:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Failed to generate report",
      },
      { status: 500 }
    );
  }
}

async function generateSummaryReport(filter: any, pagination: any) {
  const { assets } = await DepreciationService.getAssetsWithDepreciation(filter, pagination);
  const statistics = await DepreciationService.getDepreciationStatistics(filter);

  const summaryData = assets.map(item => ({
    assetId: item.asset.id,
    assetName: item.asset.name,
    category: item.asset.category,
    location: item.asset.location,
    department: item.asset.department,
    purchaseDate: item.asset.purchaseDate,
    purchasePrice: item.asset.purchasePrice,
    currentBookValue: item.depreciation?.currentBookValue || item.asset.purchasePrice,
    accumulatedDepreciation: item.depreciation?.accumulatedDepreciation || 0,
    depreciationMethod: item.depreciation?.depreciationMethod || 'N/A',
    remainingUsefulLife: item.depreciation?.remainingUsefulLife || 0,
    depreciationRate: item.asset.purchasePrice > 0 
      ? ((item.depreciation?.accumulatedDepreciation || 0) / item.asset.purchasePrice * 100).toFixed(2)
      : '0.00',
  }));

  return NextResponse.json({
    success: true,
    data: {
      reportType: 'summary',
      generatedAt: new Date().toISOString(),
      filter,
      statistics,
      assets: summaryData,
    },
  });
}

async function generateDetailedReport(filter: any, pagination: any) {
  const { assets } = await DepreciationService.getAssetsWithDepreciation(filter, pagination);

  const detailedData = await Promise.all(
    assets.map(async (item) => {
      const schedule = await DepreciationService.getDepreciationSchedule(item.asset.id);
      
      return {
        asset: item.asset,
        depreciation: item.depreciation,
        schedule: schedule.map(entry => ({
          year: entry.year,
          month: entry.month,
          depreciationAmount: entry.depreciationAmount,
          accumulatedDepreciation: entry.accumulatedDepreciation,
          bookValue: entry.bookValue,
          isActual: entry.isActual,
        })),
        assetType: item.asset.assetType,
      };
    })
  );

  return NextResponse.json({
    success: true,
    data: {
      reportType: 'detailed',
      generatedAt: new Date().toISOString(),
      filter,
      assets: detailedData,
    },
  });
}

async function generateScheduleReport(filter: any, pagination: any) {
  const { assets } = await DepreciationService.getAssetsWithDepreciation(filter, pagination);

  // Generate consolidated schedule for all assets
  const consolidatedSchedule: Record<string, any> = {};

  for (const item of assets) {
    const schedule = await DepreciationService.getDepreciationSchedule(item.asset.id);
    
    for (const entry of schedule) {
      const key = `${entry.year}-${entry.month.toString().padStart(2, '0')}`;
      
      if (!consolidatedSchedule[key]) {
        consolidatedSchedule[key] = {
          period: key,
          year: entry.year,
          month: entry.month,
          totalDepreciation: 0,
          assetCount: 0,
          assets: [],
        };
      }
      
      consolidatedSchedule[key].totalDepreciation += entry.depreciationAmount;
      consolidatedSchedule[key].assetCount++;
      consolidatedSchedule[key].assets.push({
        assetId: item.asset.id,
        assetName: item.asset.name,
        depreciationAmount: entry.depreciationAmount,
        bookValue: entry.bookValue,
      });
    }
  }

  const scheduleArray = Object.values(consolidatedSchedule).sort((a: any, b: any) => 
    a.period.localeCompare(b.period)
  );

  return NextResponse.json({
    success: true,
    data: {
      reportType: 'schedule',
      generatedAt: new Date().toISOString(),
      filter,
      schedule: scheduleArray,
    },
  });
}

async function generateComparisonReport(filter: any, pagination: any) {
  const { assets } = await DepreciationService.getAssetsWithDepreciation(filter, pagination);

  // Group assets by depreciation method
  const methodComparison = assets.reduce((acc, item) => {
    if (!item.depreciation) return acc;
    
    const method = item.depreciation.depreciationMethod;
    if (!acc[method]) {
      acc[method] = {
        method,
        assetCount: 0,
        totalPurchasePrice: 0,
        totalCurrentValue: 0,
        totalAccumulatedDepreciation: 0,
        averageAge: 0,
        assets: [],
      };
    }
    
    const age = (new Date().getTime() - new Date(item.asset.purchaseDate).getTime()) / (365.25 * 24 * 60 * 60 * 1000);
    
    acc[method].assetCount++;
    acc[method].totalPurchasePrice += item.asset.purchasePrice;
    acc[method].totalCurrentValue += item.depreciation.currentBookValue;
    acc[method].totalAccumulatedDepreciation += item.depreciation.accumulatedDepreciation;
    acc[method].averageAge = (acc[method].averageAge * (acc[method].assetCount - 1) + age) / acc[method].assetCount;
    acc[method].assets.push({
      id: item.asset.id,
      name: item.asset.name,
      category: item.asset.category,
      age: age.toFixed(1),
    });
    
    return acc;
  }, {} as Record<string, any>);

  // Calculate efficiency metrics for each method
  Object.values(methodComparison).forEach((method: any) => {
    method.depreciationRate = method.totalPurchasePrice > 0 
      ? (method.totalAccumulatedDepreciation / method.totalPurchasePrice * 100).toFixed(2)
      : '0.00';
    method.averageAge = method.averageAge.toFixed(1);
  });

  return NextResponse.json({
    success: true,
    data: {
      reportType: 'comparison',
      generatedAt: new Date().toISOString(),
      filter,
      methodComparison: Object.values(methodComparison),
    },
  });
}
