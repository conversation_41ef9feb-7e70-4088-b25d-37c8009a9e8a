import { NextRequest, NextResponse } from "next/server";
import { DepreciationService } from "@/lib/services/depreciation-service";
import { DepreciationEngine } from "@/lib/engines/depreciation-engine";
import { BulkDepreciationCalculationSchema } from "@/lib/schemas/depreciation";
import { z } from "zod";
import prisma from "@/lib/prisma";

// POST /api/depreciation/bulk - Perform bulk depreciation operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const operation = body.operation;

    switch (operation) {
      case 'calculate':
        return handleBulkCalculation(body);
      case 'recalculate_by_type':
        return handleRecalculateByType(body);
      case 'delete_schedules':
        return handleBulkDeleteSchedules(body);
      case 'export':
        return handleBulkExport(body);
      default:
        return NextResponse.json(
          {
            success: false,
            error: "Invalid operation. Supported operations: calculate, recalculate_by_type, delete_schedules, export",
          },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error("Error in bulk depreciation operation:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid request parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Bulk operation failed",
      },
      { status: 500 }
    );
  }
}

async function handleBulkCalculation(body: any) {
  const { assetIds, recalculate } = BulkDepreciationCalculationSchema.parse(body);
  
  const result = await DepreciationService.bulkCalculateDepreciation({
    assetIds,
    recalculate,
  });

  return NextResponse.json({
    success: true,
    data: result,
    message: `Bulk calculation completed. ${result.summary.successful} successful, ${result.summary.failed} failed.`,
  });
}

async function handleRecalculateByType(body: any) {
  const { assetTypeIds } = z.object({
    assetTypeIds: z.array(z.string().cuid()).min(1, "At least one asset type ID required"),
  }).parse(body);

  const results = [];
  const errors = [];

  for (const assetTypeId of assetTypeIds) {
    try {
      await DepreciationEngine.recalculateForAssetType(assetTypeId);
      
      // Count affected assets
      const assetCount = await prisma.asset.count({
        where: { assetTypeId },
      });

      results.push({
        assetTypeId,
        success: true,
        affectedAssets: assetCount,
      });
    } catch (error) {
      errors.push({
        assetTypeId,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }

  return NextResponse.json({
    success: true,
    data: {
      results,
      errors,
      summary: {
        total: assetTypeIds.length,
        successful: results.length,
        failed: errors.length,
        totalAffectedAssets: results.reduce((sum, r) => sum + r.affectedAssets, 0),
      },
    },
    message: `Recalculation by type completed. ${results.length} asset types processed successfully.`,
  });
}

async function handleBulkDeleteSchedules(body: any) {
  const { assetIds } = z.object({
    assetIds: z.array(z.string().cuid()).min(1, "At least one asset ID required"),
  }).parse(body);

  const result = await prisma.depreciationSchedule.deleteMany({
    where: {
      assetId: { in: assetIds },
    },
  });

  return NextResponse.json({
    success: true,
    data: {
      deletedCount: result.count,
      assetIds,
    },
    message: `Deleted ${result.count} depreciation schedule entries for ${assetIds.length} assets.`,
  });
}

async function handleBulkExport(body: any) {
  const { 
    assetIds, 
    format = 'json',
    includeSchedules = true,
    includeStatistics = true 
  } = z.object({
    assetIds: z.array(z.string().cuid()).min(1, "At least one asset ID required"),
    format: z.enum(['json', 'csv']).default('json'),
    includeSchedules: z.boolean().default(true),
    includeStatistics: z.boolean().default(true),
  }).parse(body);

  // Get assets with depreciation data
  const { assets } = await DepreciationService.getAssetsWithDepreciation(
    { assetIds },
    { page: 1, limit: assetIds.length }
  );

  const exportData: any = {
    exportedAt: new Date().toISOString(),
    assetCount: assets.length,
    assets: [],
  };

  // Process each asset
  for (const item of assets) {
    const assetData: any = {
      asset: item.asset,
      depreciation: item.depreciation,
    };

    if (includeSchedules) {
      assetData.schedule = await DepreciationService.getDepreciationSchedule(item.asset.id);
    }

    exportData.assets.push(assetData);
  }

  // Add statistics if requested
  if (includeStatistics) {
    exportData.statistics = await DepreciationService.getDepreciationStatistics({ assetIds });
  }

  if (format === 'csv') {
    // Convert to CSV format
    const csvData = convertToCSV(exportData);
    
    return new NextResponse(csvData, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="depreciation-export-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    });
  }

  return NextResponse.json({
    success: true,
    data: exportData,
    message: `Exported depreciation data for ${assets.length} assets.`,
  });
}

function convertToCSV(data: any): string {
  const headers = [
    'Asset ID',
    'Asset Name',
    'Category',
    'Location',
    'Department',
    'Purchase Date',
    'Purchase Price',
    'Current Book Value',
    'Accumulated Depreciation',
    'Depreciation Method',
    'Remaining Useful Life',
    'Depreciation Rate (%)',
  ];

  const rows = data.assets.map((item: any) => [
    item.asset.id,
    item.asset.name,
    item.asset.category,
    item.asset.location,
    item.asset.department || '',
    new Date(item.asset.purchaseDate).toLocaleDateString(),
    item.asset.purchasePrice,
    item.depreciation?.currentBookValue || item.asset.purchasePrice,
    item.depreciation?.accumulatedDepreciation || 0,
    item.depreciation?.depreciationMethod || 'N/A',
    item.depreciation?.remainingUsefulLife || 0,
    item.asset.purchasePrice > 0 
      ? ((item.depreciation?.accumulatedDepreciation || 0) / item.asset.purchasePrice * 100).toFixed(2)
      : '0.00',
  ]);

  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => 
      typeof cell === 'string' && cell.includes(',') 
        ? `"${cell.replace(/"/g, '""')}"` 
        : cell
    ).join(','))
  ].join('\n');

  return csvContent;
}

// GET /api/depreciation/bulk - Get bulk operation status or history
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const operation = searchParams.get("operation");

    // This could be extended to track bulk operation history
    // For now, return available operations
    const availableOperations = [
      {
        operation: 'calculate',
        description: 'Calculate depreciation for multiple assets',
        parameters: ['assetIds', 'recalculate'],
      },
      {
        operation: 'recalculate_by_type',
        description: 'Recalculate depreciation for all assets of specific types',
        parameters: ['assetTypeIds'],
      },
      {
        operation: 'delete_schedules',
        description: 'Delete depreciation schedules for multiple assets',
        parameters: ['assetIds'],
      },
      {
        operation: 'export',
        description: 'Export depreciation data for multiple assets',
        parameters: ['assetIds', 'format', 'includeSchedules', 'includeStatistics'],
      },
    ];

    return NextResponse.json({
      success: true,
      data: {
        availableOperations,
        supportedFormats: ['json', 'csv'],
      },
    });

  } catch (error) {
    console.error("Error getting bulk operation info:", error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Failed to get operation info",
      },
      { status: 500 }
    );
  }
}
