"use client";

import React, { useState } from "react";
import { AssetOperation<PERSON>orm<PERSON><PERSON>er, AssetOperationFormRuntime, FormDefinition } from "@/components/form-builder";
import { AssetOperationType, FormContext, FormSubmissionData } from "@/lib/types/asset-type-forms";
import { CustomField } from "@/lib/modules/asset-types/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import { Settings, Play, FileText } from "lucide-react";

// Mock data for testing
const mockAssetType = {
  id: "test-asset-type-1",
  name: "Test Equipment",
  customFields: [
    {
      id: "field-1",
      label: "Serial Number",
      type: "text",
      isRequired: true,
      order: 1,
    },
    {
      id: "field-2", 
      label: "Purchase Price",
      type: "number",
      isRequired: true,
      order: 2,
    },
    {
      id: "field-3",
      label: "Location",
      type: "select",
      isRequired: true,
      order: 3,
      options: [
        { value: "warehouse", label: "Warehouse" },
        { value: "office", label: "Office" },
        { value: "factory", label: "Factory" },
      ],
    },
    {
      id: "field-4",
      label: "Description",
      type: "textarea",
      isRequired: false,
      order: 4,
    },
  ] as CustomField[],
};

const mockContext: FormContext = {
  assetTypeId: mockAssetType.id,
  operationType: "asset.create",
  userId: "test-user-1",
  userRole: "admin",
  location: "warehouse",
  department: "IT",
};

export default function TestFormIntegrationPage() {
  const [activeTab, setActiveTab] = useState("builder");
  const [testOperationType, setTestOperationType] = useState<AssetOperationType>("asset.create");
  const [savedForms, setSavedForms] = useState<Partial<Record<AssetOperationType, FormDefinition>>>({});

  const handleFormSave = async (form: FormDefinition, operationType: AssetOperationType) => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSavedForms(prev => ({
        ...prev,
        [operationType]: form,
      }));

      toast({
        title: "Success",
        description: `Form for ${operationType} saved successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save form",
        variant: "destructive",
      });
    }
  };

  const handleFormPreview = (form: FormDefinition, operationType: AssetOperationType) => {
    setTestOperationType(operationType);
    setActiveTab("runtime");
    toast({
      title: "Preview",
      description: `Switching to runtime view for ${operationType}`,
    });
  };

  const handleFormSubmission = async (data: FormSubmissionData) => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      console.log("Form submission data:", data);
      
      toast({
        title: "Form Submitted",
        description: "Form data has been processed successfully",
      });
    } catch (error) {
      throw new Error("Failed to submit form");
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Form Integration Test</h1>
          <p className="text-muted-foreground">
            Test the complete form builder and runtime integration
          </p>
        </div>
        <Badge variant="outline">Test Environment</Badge>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Asset Type</p>
                <p className="text-lg font-bold">{mockAssetType.name}</p>
              </div>
              <Settings className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Custom Fields</p>
                <p className="text-lg font-bold">{mockAssetType.customFields.length}</p>
              </div>
              <FileText className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">Configured Forms</p>
                <p className="text-lg font-bold">{Object.keys(savedForms).length}</p>
              </div>
              <Play className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="builder" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Form Builder
          </TabsTrigger>
          <TabsTrigger value="runtime" className="flex items-center gap-2">
            <Play className="h-4 w-4" />
            Form Runtime
          </TabsTrigger>
        </TabsList>

        {/* Form Builder Tab */}
        <TabsContent value="builder" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Asset Operation Form Builder</CardTitle>
            </CardHeader>
            <CardContent>
              <AssetOperationFormBuilder
                assetTypeId={mockAssetType.id}
                assetTypeName={mockAssetType.name}
                availableFields={mockAssetType.customFields}
                onSave={handleFormSave}
                onPreview={handleFormPreview}
                initialForms={savedForms}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Form Runtime Tab */}
        <TabsContent value="runtime" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Asset Operation Form Runtime</CardTitle>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Testing:</span>
                  <Badge variant="secondary">{testOperationType}</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <AssetOperationFormRuntime
                assetTypeId={mockAssetType.id}
                operationType={testOperationType}
                context={mockContext}
                onSubmit={handleFormSubmission}
                onCancel={() => {
                  toast({
                    title: "Cancelled",
                    description: "Form submission cancelled",
                  });
                }}
                initialValues={{}}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}