"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AssetOperationFormRenderer } from "@/components/form-builder/asset-operation-form-renderer";
import { FormContext } from "@/lib/types/asset-type-forms";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, ClipboardCheck, Package, MapPin, Calendar, BarChart3, Info } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { getStatusBadge } from "@/lib/utils/asset-status";

interface Asset {
  id: string;
  name: string;
  code: string;
  status: string;
  location: string;
  assetType: {
    id: string;
    name: string;
  };
}

interface AuditSummary {
  totalAssets: number;
  auditedAssets: number;
  discrepancies: number;
  lastAuditDate?: string;
}

export default function InventoryAuditPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [assets, setAssets] = useState<Asset[]>([]);
  const [selectedAssetId, setSelectedAssetId] = useState<string>("");
  const [selectedLocation, setSelectedLocation] = useState<string>("");
  const [auditSummary, setAuditSummary] = useState<AuditSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get parameters from URL if provided
  const assetIdFromUrl = searchParams.get("assetId");
  const locationFromUrl = searchParams.get("location");

  useEffect(() => {
    loadAssets();
    loadAuditSummary();
  }, []);

  useEffect(() => {
    if (assetIdFromUrl && assets.length > 0) {
      const asset = assets.find(a => a.id === assetIdFromUrl);
      if (asset) {
        setSelectedAssetId(assetIdFromUrl);
        setSelectedLocation(asset.location);
      }
    } else if (locationFromUrl) {
      setSelectedLocation(locationFromUrl);
    }
  }, [assetIdFromUrl, locationFromUrl, assets]);

  const loadAssets = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/assets");
      if (response.ok) {
        const data = await response.json();
        setAssets(data.assets || []);
      } else {
        throw new Error("Failed to load assets");
      }
    } catch (error) {
      console.error("Error loading assets:", error);
      toast({
        title: "Error",
        description: "Failed to load assets. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadAuditSummary = async () => {
    try {
      const response = await fetch("/api/inventory/audit-summary");
      if (response.ok) {
        const data = await response.json();
        setAuditSummary(data.summary);
      }
    } catch (error) {
      console.error("Error loading audit summary:", error);
    }
  };

  const handleSubmit = async (formData: Record<string, any>) => {
    try {
      setIsSubmitting(true);

      const selectedAsset = assets.find(a => a.id === selectedAssetId);

      const response = await fetch("/api/asset-operations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          operationType: "inventory.audit",
          assetTypeId: selectedAsset?.assetType.id,
          data: {
            ...formData,
            assetId: selectedAssetId,
            assetName: selectedAsset?.name,
            assetCode: selectedAsset?.code,
            location: selectedLocation,
          },
          context: {
            assetId: selectedAssetId,
            assetTypeId: selectedAsset?.assetType.id,
            operationType: "inventory.audit",
            userId: "current-user", // This should come from auth context
            userRole: "admin", // This should come from auth context
            location: selectedLocation,
          },
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Success",
          description: "Inventory audit completed successfully!",
        });
        
        // Refresh audit summary and redirect
        await loadAuditSummary();
        router.push("/admin/assets");
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to complete inventory audit");
      }
    } catch (error) {
      console.error("Error completing audit:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to complete inventory audit. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = async (formData: Record<string, any>) => {
    try {
      // Save as draft logic
      localStorage.setItem(`audit-draft-${selectedAssetId || selectedLocation}`, JSON.stringify(formData));
      toast({
        title: "Draft Saved",
        description: "Your audit progress has been saved as a draft.",
      });
    } catch (error) {
      console.error("Error saving draft:", error);
      toast({
        title: "Error",
        description: "Failed to save draft.",
        variant: "destructive",
      });
    }
  };

  const handleCancel = () => {
    router.back();
  };



  const filteredAssets = selectedLocation 
    ? assets.filter(asset => asset.location === selectedLocation)
    : assets;

  const locations = [...new Set(assets.map(asset => asset.location))].filter(Boolean);

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    );
  }

  const selectedAsset = assets.find(a => a.id === selectedAssetId);

  const formContext: FormContext = {
    assetId: selectedAssetId,
    assetTypeId: selectedAsset?.assetType.id || "",
    operationType: "inventory.audit",
    userId: "current-user", // This should come from auth context
    userRole: "admin", // This should come from auth context
    location: selectedLocation,
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Inventory Audit</h1>
            <p className="text-muted-foreground">
              Conduct physical verification of assets
            </p>
          </div>
        </div>
      </div>

      {/* Audit Summary */}
      {auditSummary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Audit Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Package className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Assets</p>
                  <p className="font-medium">{auditSummary.totalAssets}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <ClipboardCheck className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Audited</p>
                  <p className="font-medium">{auditSummary.auditedAssets}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Package className="h-4 w-4 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Discrepancies</p>
                  <p className="font-medium">{auditSummary.discrepancies}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Calendar className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Last Audit</p>
                  <p className="font-medium">
                    {auditSummary.lastAuditDate 
                      ? new Date(auditSummary.lastAuditDate).toLocaleDateString()
                      : "Never"
                    }
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Asset/Location Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ClipboardCheck className="h-5 w-5" />
            Select Asset or Location
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="location">Location (Optional)</Label>
              <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                <SelectTrigger>
                  <SelectValue placeholder="Select location to audit" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Locations</SelectItem>
                  {locations.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="asset">Specific Asset (Optional)</Label>
              <Select value={selectedAssetId} onValueChange={setSelectedAssetId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select specific asset" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No specific asset</SelectItem>
                  {filteredAssets.map((asset) => (
                    <SelectItem key={asset.id} value={asset.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{asset.name}</span>
                        <span className="text-sm text-muted-foreground">
                          {asset.code} • {asset.location}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {selectedLocation && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Auditing location: <strong>{selectedLocation}</strong>
                {filteredAssets.length > 0 && (
                  <span> ({filteredAssets.length} assets in this location)</span>
                )}
              </AlertDescription>
            </Alert>
          )}

          {selectedAsset && (
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{selectedAsset.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {selectedAsset.code} • {selectedAsset.assetType.name}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(selectedAsset.status)}
                    <Badge variant="outline">{selectedAsset.location}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Audit Form */}
      {(selectedAssetId || selectedLocation) && (
        <AssetOperationFormRenderer
          assetTypeId={selectedAsset?.assetType.id || "general"}
          operationType="inventory.audit"
          context={formContext}
          onSubmit={handleSubmit}
          onSaveDraft={handleSaveDraft}
          onCancel={handleCancel}
        />
      )}

      {!selectedAssetId && !selectedLocation && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <ClipboardCheck className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Select Asset or Location</h3>
              <p className="text-muted-foreground">
                Choose a specific asset or location to begin the audit process.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}