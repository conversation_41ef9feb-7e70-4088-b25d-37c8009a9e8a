"use client"

import React, { useEffect, useState, useMemo } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { HeaderTabContent, TabContent } from "@/components/ui/header-tab-content"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  Search,
  Download,
  Edit,
  Trash2,
  MoreHorizontal,
  Package,
  DollarSign,
  Calendar,
  MapPin,
  QrCode,
  Share,
  History,
  Zap,
  Loader2,
  AlertCircle,
  Filter,
  SortAsc,
  Eye,
  Copy,
} from "lucide-react"
import { useAdminHeader } from "@/hooks/use-admin-header";
import { useHeaderTabs } from "@/hooks/use-admin-tabs";
import { getAssetManagementHeaderConfig } from "@/lib/utils/admin-header-configs";
import { getAssetManagementHeaderTabs } from "@/lib/utils/admin-tabs-configs";
import { getStatusBadge } from "@/lib/utils/asset-status";

import { useAssets } from "@/hooks/use-assets";
import { Asset } from "@prisma/client";
import { toast } from "@/components/ui/use-toast";
import { AIAssistant } from "@/components/ai/ai-assistant";
import { AIInsightsDashboard } from "@/components/ai/ai-insights-dashboard";
import { AssetOperationsSummary } from "@/components/dashboard/asset-operations-summary";
import { AssetBreakdownCharts } from "@/components/dashboard/asset-breakdown-charts";

const categories = ["All", "IT Equipment", "Machinery", "Furniture", "Security", "Vehicles"]
const statuses = ["All", "active", "maintenance", "disposed"]
const conditions = ["All", "Excellent", "Good", "Fair", "Poor"]

export default function AssetsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [selectedCondition, setSelectedCondition] = useState("All")
  const [selectedAssets, setSelectedAssets] = useState<string[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const [viewMode, setViewMode] = useState<"table" | "grid">("table")
  const [sortBy, setSortBy] = useState("name")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")

  const {
    assets,
    isLoading,
    error,
    fetchAssets,
    deleteAsset
  } = useAssets();



  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() =>
    getAssetManagementHeaderConfig(isAddDialogOpen, setIsAddDialogOpen),
    [isAddDialogOpen, setIsAddDialogOpen]
  );

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getAssetManagementHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "list");



  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "list":
        return (
          <div className="space-y-6">
            {/* Enhanced Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Advanced Filters</span>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Filter className="mr-2 h-4 w-4" />
                      More Filters
                    </Button>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" size="sm">
                          <SortAsc className="mr-2 h-4 w-4" />
                          Sort
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80">
                        <div className="grid gap-4">
                          <div className="space-y-2">
                            <h4 className="font-medium leading-none">Sort Options</h4>
                            <p className="text-sm text-muted-foreground">
                              Choose how to sort your assets
                            </p>
                          </div>
                          <div className="grid gap-2">
                            <div className="grid grid-cols-3 items-center gap-4">
                              <Label htmlFor="sortBy">Sort by</Label>
                              <Select value={sortBy} onValueChange={setSortBy}>
                                <SelectTrigger className="col-span-2 h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="name">Name</SelectItem>
                                  <SelectItem value="category">Category</SelectItem>
                                  <SelectItem value="status">Status</SelectItem>
                                  <SelectItem value="value">Value</SelectItem>
                                  <SelectItem value="date">Date Added</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid grid-cols-3 items-center gap-4">
                              <Label htmlFor="sortOrder">Order</Label>
                              <Select value={sortOrder} onValueChange={(value: "asc" | "desc") => setSortOrder(value)}>
                                <SelectTrigger className="col-span-2 h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="asc">Ascending</SelectItem>
                                  <SelectItem value="desc">Descending</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                  <div className="space-y-2">
                    <Label htmlFor="search">Search Assets</Label>
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="search"
                        placeholder="Search by name, ID, or description..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {statuses.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="condition">Condition</Label>
                    <Select value={selectedCondition} onValueChange={setSelectedCondition}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select condition" />
                      </SelectTrigger>
                      <SelectContent>
                        {conditions.map((condition) => (
                          <SelectItem key={condition} value={condition}>
                            {condition}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>View Mode</Label>
                    <div className="flex space-x-2">
                      <Button
                        variant={viewMode === "table" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setViewMode("table")}
                      >
                        Table
                      </Button>
                      <Button
                        variant={viewMode === "grid" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setViewMode("grid")}
                      >
                        Grid
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Assets Table/Grid */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Assets ({filteredAssets.length})</span>
                  <div className="flex items-center space-x-2">
                    {selectedAssets.length > 0 && (
                      <Badge variant="secondary">
                        {selectedAssets.length} selected
                      </Badge>
                    )}
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      Export
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : (
                  <ScrollArea className="h-[600px]">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50px]">
                            <Checkbox
                              checked={selectedAssets.length === filteredAssets.length}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedAssets(filteredAssets.map((asset: Asset) => asset.id))
                                } else {
                                  setSelectedAssets([])
                                }
                              }}
                            />
                          </TableHead>
                          <TableHead>Asset</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Location</TableHead>
                          <TableHead>Value</TableHead>
                          <TableHead>Last Updated</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredAssets.map((asset: Asset) => (
                          <TableRow key={asset.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedAssets.includes(asset.id)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setSelectedAssets([...selectedAssets, asset.id])
                                  } else {
                                    setSelectedAssets(selectedAssets.filter(id => id !== asset.id))
                                  }
                                }}
                              />
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-3">
                                <Avatar className="h-10 w-10">
                                  <AvatarImage src={asset.assetImages?.[0] || "/placeholder.svg"} alt={asset.name} />
                                  <AvatarFallback>
                                    <Package className="h-4 w-4" />
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{asset.name}</div>
                                  <div className="text-sm text-muted-foreground">{asset.serialNumber || asset.id}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">{asset.category}</Badge>
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(asset.status)}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <MapPin className="mr-1 h-3 w-3" />
                                {asset.location}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <DollarSign className="mr-1 h-3 w-3" />
                                {asset.purchasePrice?.toLocaleString()}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Calendar className="mr-1 h-3 w-3" />
                                {new Date(asset.updatedAt).toLocaleDateString()}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <span className="sr-only">Open menu</span>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => navigator.clipboard.writeText(asset.id)}>
                                    <Copy className="mr-2 h-4 w-4" />
                                    Copy Asset ID
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem asChild>
                                    <Link href={`/admin/assets/${asset.id}`}>
                                      <Eye className="mr-2 h-4 w-4" />
                                      View Details
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem asChild>
                                    <Link href={`/admin/assets/${asset.id}/edit`}>
                                      <Edit className="mr-2 h-4 w-4" />
                                      Edit Asset
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <QrCode className="mr-2 h-4 w-4" />
                                    Generate QR Code
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <History className="mr-2 h-4 w-4" />
                                    View History
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Share className="mr-2 h-4 w-4" />
                                    Transfer Asset
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    className="text-red-600"
                                    onClick={() => handleDeleteAsset(asset.id)}
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete Asset
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>
          </div>
        );
      case "overview":
        return (
          <TabContent>
            <AssetOperationsSummary />
            <AssetBreakdownCharts assets={filteredAssets} isLoading={isLoading} />
          </TabContent>
        );
      case "analytics":
        return (
          <TabContent>
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Asset Value Trends</CardTitle>
                  <CardDescription>Depreciation and value changes over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                    <div className="text-center">
                      <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">Interactive Value Chart</p>
                      <p className="text-sm text-muted-foreground">Asset depreciation over time</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Utilization Metrics</CardTitle>
                  <CardDescription>Asset usage and efficiency analysis</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                    <div className="text-center">
                      <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">Utilization Dashboard</p>
                      <p className="text-sm text-muted-foreground">Real-time usage metrics</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabContent>
        );
      case "ai-insights":
        return (
          <TabContent>
            <AIInsightsDashboard />
          </TabContent>
        );
      default:
        return <TabContent>Tab content not found</TabContent>;
    }
  };  useEffect(() => {
    // Apply filters when they change
    const filters: any = {};
    if (selectedCategory !== "All") filters.category = selectedCategory;
    if (selectedStatus !== "All") filters.status = selectedStatus;
    if (searchTerm) filters.search = searchTerm;
    
    fetchAssets(filters);
  }, [fetchAssets, selectedCategory, selectedStatus, searchTerm]);



  const handleDeleteAsset = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this asset?")) {
      const success = await deleteAsset(id);
      if (success) {
        toast({
          title: "Asset Deleted",
          description: "The asset has been successfully deleted.",
        });
        setSelectedAssets((prev) => prev.filter((assetId) => assetId !== id));
      } else {
        toast({
          title: "Error",
          description: "Failed to delete the asset. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  // Ensure filteredAssets is always an array and handle potential data structure issues
  const filteredAssets = React.useMemo(() => {
    if (!assets) return [];
    if (Array.isArray(assets)) return assets;

    // Handle case where assets might be wrapped in a data property
    if (typeof assets === 'object' && 'data' in assets && Array.isArray((assets as any).data)) {
      return (assets as any).data;
    }

    // Fallback to empty array
    console.warn('Assets data is not in expected format:', assets);
    return [];
  }, [assets]);



  const getConditionBadge = (condition: string) => {
    switch (condition) {
      case "Excellent":
        return (
          <Badge variant="default" className="bg-green-500">
            Excellent
          </Badge>
        )
      case "Good":
        return (
          <Badge variant="default" className="bg-blue-500">
            Good
          </Badge>
        )
      case "Fair":
        return (
          <Badge variant="default" className="bg-yellow-500">
            Fair
          </Badge>
        )
      case "Poor":
        return <Badge variant="destructive">Poor</Badge>
      default:
        return <Badge variant="secondary">{condition}</Badge>
    }
  }

  const handleSelectAsset = (assetId: string) => {
    setSelectedAssets((prev) => (prev.includes(assetId) ? prev.filter((id) => id !== assetId) : [...prev, assetId]))
  }

  const handleSelectAll = () => {
    setSelectedAssets(selectedAssets.length === filteredAssets.length ? [] : filteredAssets.map((asset: Asset) => asset.id))
  }

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center text-center p-6">
              <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Error Loading Assets</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={() => fetchAssets()}>
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }



  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* The AppHeader is now managed by Zustand and rendered in app/layout.tsx */}



      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "list", content: getTabContent("list") },
          { id: "overview", content: getTabContent("overview") },
          { id: "analytics", content: getTabContent("analytics") },
          { id: "ai-insights", content: getTabContent("ai-insights") },
          { id: "test1", content: <div className="p-4 bg-green-100">Test Tab 1 Content</div> },
          { id: "test2", content: <div className="p-4 bg-blue-100">Test Tab 2 Content</div> },
        ]}
      />


      {/* AI Assistant - Always available */}
      <AIAssistant />
    </div>
  )
}