"use client"

import type React from "react"

import { useState, use<PERSON>ffe<PERSON>, useMemo } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"

import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/dialog"
import {
  Plus,
  Settings,
  Edit,
  Trash2,
  Copy,
  Eye,
  Layers,
  Workflow,
  BarChart3,
  Search,
  Filter,
  Download,
  Upload,
  Wrench,
  Clock,
  TrendingDown,
  TrendingUp,
  CheckCircle,
  FileText,
  GitBranch,
  ExternalLink,
  Cog,
} from "lucide-react"
import { AssetTypeService } from "@/lib/modules/asset-types/services"
import type { AssetType, AssetTypeMetrics } from "@/lib/modules/asset-types/types"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { HeaderTabContent } from "@/components/ui/header-tab-content"
import { getAssetTypesHeaderConfig } from "@/lib/utils/admin-header-configs"
import { getAssetTypesHeaderTabs } from "@/lib/utils/admin-tabs-configs"
import { AssetTypeTemplateSelector } from "@/components/asset-types/asset-type-template-selector"
import { AssetTypeTemplate } from "@/lib/templates/asset-type-templates"
import { AssetTypeForm as AssetTypeFormComponent } from "@/components/asset-types/asset-type-form"

function getStatusBadge(isActive: boolean) {
  return isActive ? (
    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
      <CheckCircle className="w-3 h-3 mr-1" />
      Active
    </Badge>
  ) : (
    <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
      <Clock className="w-3 h-3 mr-1" />
      Inactive
    </Badge>
  )
}

function TemplateLibrary({ onTemplateSelect }: { onTemplateSelect: (template: AssetTypeTemplate, customizations?: any) => void }) {
  const [templates, setTemplates] = useState<AssetTypeTemplate[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/asset-type-templates")
      if (response.ok) {
        const data = await response.json()
        setTemplates(data.templates)
      }
    } catch (error) {
      console.error("Error loading templates:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const getTemplateIcon = (template: AssetTypeTemplate) => {
    switch (template.icon) {
      case "Laptop":
        return <Layers className="h-6 w-6" />
      case "Table":
        return <Layers className="h-6 w-6" />
      case "Car":
        return <Layers className="h-6 w-6" />
      default:
        return <Layers className="h-6 w-6" />
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading templates...</span>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {templates.map((template) => (
        <Card
          key={template.id}
          className="cursor-pointer transition-all hover:shadow-md hover:border-primary/50"
          onClick={() => onTemplateSelect(template)}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div
                className="p-2 rounded-lg"
                style={{ backgroundColor: `${template.color}20`, color: template.color }}
              >
                {getTemplateIcon(template)}
              </div>
              <div className="flex-1">
                <CardTitle className="text-sm">{template.name}</CardTitle>
                <Badge variant="outline" className="text-xs">
                  {template.category}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <p className="text-sm text-muted-foreground mb-3">
              {template.description}
            </p>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Custom Fields:</span>
                <Badge variant="secondary">{template.customFields.length}</Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Lifecycle Stages:</span>
                <Badge variant="secondary">{template.lifecycleStages.length}</Badge>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Maintenance Schedules:</span>
                <Badge variant="secondary">{template.maintenanceSchedules.length}</Badge>
              </div>
            </div>

            <div className="flex flex-wrap gap-1 mt-3">
              {template.tags.slice(0, 3).map(tag => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{template.tags.length - 3}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export default function AssetTypesPage() {
  const router = useRouter()
  const [assetTypes, setAssetTypes] = useState<AssetType[]>([])
  const [metrics, setMetrics] = useState<AssetTypeMetrics | null>(null)
  const [selectedAssetType, setSelectedAssetType] = useState<AssetType | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")

  const assetTypeService = AssetTypeService.getInstance()

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => getAssetTypesHeaderConfig(setIsTemplateDialogOpen), [setIsTemplateDialogOpen]);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getAssetTypesHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    const types = await assetTypeService.getAssetTypes()
    const metricsData = await assetTypeService.getMetrics()
    setAssetTypes(types)
    setMetrics(metricsData)
  }

  const handleTemplateSelect = async (template: AssetTypeTemplate, customizations?: any) => {
    try {
      const response = await fetch("/api/asset-type-templates", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId: template.id,
          customizations,
          createForms: true,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log("Asset type created from template:", result);
        setIsTemplateDialogOpen(false);
        await loadData(); // Refresh the data
      } else {
        const error = await response.json();
        console.error("Failed to create asset type from template:", error);
      }
    } catch (error) {
      console.error("Error creating asset type from template:", error);
    }
  }

  const filteredAssetTypes = assetTypes.filter((type) => {
    const matchesSearch =
      type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      type.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      type.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "All" || type.category.name === selectedCategory
    return matchesSearch && matchesCategory
  })

  const categories = ["All", ...Array.from(new Set(assetTypes.map((type) => type.category.name)))]

  // Handler functions
  const handleEdit = (type: AssetType) => {
    setSelectedAssetType(type)
    setIsEditDialogOpen(true)
  }

  const handleDelete = async (id: string) => {
    try {
      await assetTypeService.deleteAssetType(id)
      loadData()
    } catch (error) {
      console.error('Error deleting asset type:', error)
    }
  }

  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Asset Types</CardTitle>
                  <Settings className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics?.totalTypes || 0}</div>
                  <p className="text-xs text-muted-foreground">Configured types</p>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500" />
                </CardContent>
              </Card>
              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Types</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics?.activeTypes || 0}</div>
                  <p className="text-xs text-muted-foreground">Currently in use</p>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-blue-500" />
                </CardContent>
              </Card>
              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Configured Types</CardTitle>
                  <Cog className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {assetTypes.filter(type => {
                      const configCount = [
                        type.customFields?.length > 0,
                        type.lifecycleStages?.length > 0,
                        type.maintenanceSchedules?.length > 0,
                        type.depreciationSettings,
                      ].filter(Boolean).length;
                      return configCount >= 2; // Consider configured if at least 2 areas are set up
                    }).length}
                  </div>
                  <p className="text-xs text-muted-foreground">Fully configured</p>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-500 to-orange-500" />
                </CardContent>
              </Card>
              <Card className="relative overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Configurations</CardTitle>
                  <Settings className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {assetTypes.reduce((total, type) => {
                      return total + [
                        type.customFields?.length > 0,
                        type.lifecycleStages?.length > 0,
                        type.maintenanceSchedules?.length > 0,
                        type.depreciationSettings,
                      ].filter(Boolean).length;
                    }, 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">Configuration areas</p>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 to-pink-500" />
                </CardContent>
              </Card>
            </div>

            {/* Configuration Status Overview */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Configuration Status</CardTitle>
                    <CardDescription>Overview of asset type configurations</CardDescription>
                  </div>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {assetTypes.slice(0, 6).map((type) => {
                    const configAreas = [
                      { name: "Custom Fields", configured: type.customFields?.length > 0, icon: FileText, color: "text-blue-500" },
                      { name: "Lifecycle", configured: type.lifecycleStages?.length > 0, icon: GitBranch, color: "text-purple-500" },
                      { name: "Maintenance", configured: type.maintenanceSchedules?.length > 0, icon: Wrench, color: "text-orange-500" },
                      { name: "Depreciation", configured: !!type.depreciationSettings, icon: TrendingDown, color: "text-red-500" },
                    ];
                    const configuredCount = configAreas.filter(area => area.configured).length;
                    const completionPercentage = (configuredCount / configAreas.length) * 100;

                    return (
                      <div key={type.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-medium text-sm">{type.name}</h4>
                            <p className="text-xs text-muted-foreground">{type.category.name}</p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            asChild
                          >
                            <Link href={`/admin/asset-types/${type.id}`}>
                              <ExternalLink className="h-3 w-3" />
                            </Link>
                          </Button>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-xs">
                            <span>Configuration Progress</span>
                            <span className="font-medium">{configuredCount}/4</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${completionPercentage}%` }}
                            />
                          </div>
                          <div className="flex justify-between mt-2">
                            {configAreas.map((area) => {
                              const IconComponent = area.icon;
                              return (
                                <div
                                  key={area.name}
                                  className={`p-1 rounded ${area.configured ? 'bg-green-100' : 'bg-gray-100'}`}
                                  title={area.name}
                                >
                                  <IconComponent
                                    className={`h-3 w-3 ${area.configured ? 'text-green-600' : 'text-gray-400'}`}
                                  />
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Asset Types</CardTitle>
                <CardDescription>Recently created or modified asset types</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {assetTypes.slice(0, 5).map((type) => (
                    <div key={type.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                          <Settings className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <p className="font-medium">{type.name}</p>
                          <p className="text-sm text-muted-foreground">{type.category.name}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={type.isActive ? "default" : "secondary"}>
                          {type.isActive ? "Active" : "Inactive"}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          asChild
                          title="View Details"
                        >
                          <Link href={`/admin/asset-types/${type.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          asChild
                          title="Configure"
                        >
                          <Link href={`/admin/asset-types/${type.id}/forms`}>
                            <Cog className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common configuration tasks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-center gap-2"
                    asChild
                  >
                    <Link href="/admin/asset-types/create">
                      <Plus className="h-5 w-5" />
                      <span className="text-sm">Create Asset Type</span>
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-center gap-2"
                    onClick={() => setIsTemplateDialogOpen(true)}
                  >
                    <Copy className="h-5 w-5" />
                    <span className="text-sm">Use Template</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-center gap-2"
                  >
                    <Download className="h-5 w-5" />
                    <span className="text-sm">Export Config</span>
                  </Button>
                  <Button
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-center gap-2"
                  >
                    <Upload className="h-5 w-5" />
                    <span className="text-sm">Import Config</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      case "types":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Asset Types</CardTitle>
                    <CardDescription>Manage and configure asset types for your organization</CardDescription>
                  </div>
                  <Button asChild>
                    <Link href="/admin/asset-types/create">
                      <Plus className="mr-2 h-4 w-4" />
                      Add Asset Type
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Configuration Status Summary */}
                  <div className="grid gap-4 md:grid-cols-4 p-4 bg-muted rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {assetTypes.filter(type => {
                          const configCount = [
                            type.customFields?.length > 0,
                            type.lifecycleStages?.length > 0,
                            type.maintenanceSchedules?.length > 0,
                            type.depreciationSettings,
                          ].filter(Boolean).length;
                          return configCount === 4;
                        }).length}
                      </div>
                      <div className="text-xs text-muted-foreground">Fully Configured</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">
                        {assetTypes.filter(type => {
                          const configCount = [
                            type.customFields?.length > 0,
                            type.lifecycleStages?.length > 0,
                            type.maintenanceSchedules?.length > 0,
                            type.depreciationSettings,
                          ].filter(Boolean).length;
                          return configCount >= 2 && configCount < 4;
                        }).length}
                      </div>
                      <div className="text-xs text-muted-foreground">Partially Configured</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {assetTypes.filter(type => {
                          const configCount = [
                            type.customFields?.length > 0,
                            type.lifecycleStages?.length > 0,
                            type.maintenanceSchedules?.length > 0,
                            type.depreciationSettings,
                          ].filter(Boolean).length;
                          return configCount < 2;
                        }).length}
                      </div>
                      <div className="text-xs text-muted-foreground">Needs Configuration</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{assetTypes.length}</div>
                      <div className="text-xs text-muted-foreground">Total Types</div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <Input
                      placeholder="Search asset types..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="max-w-sm"
                    />
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <ScrollArea className="h-[600px]">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Configuration</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredAssetTypes.map((type) => {
                          const configCount = [
                            type.customFields?.length > 0,
                            type.lifecycleStages?.length > 0,
                            type.maintenanceSchedules?.length > 0,
                            type.depreciationSettings,
                            // TODO: Add forms and workflows count when available
                          ].filter(Boolean).length;

                          return (
                            <TableRow key={type.id}>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{type.name}</div>
                                  <div className="text-sm text-muted-foreground">{type.code}</div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant="outline">{type.category.name}</Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  <div className="flex items-center gap-1">
                                    <span className="text-sm font-medium">{configCount}/6</span>
                                    <span className="text-xs text-muted-foreground">configured</span>
                                  </div>
                                  <div className="flex gap-1">
                                    {type.customFields?.length > 0 && (
                                      <div className="w-2 h-2 rounded-full bg-green-500" title="Custom Fields" />
                                    )}
                                    {type.lifecycleStages?.length > 0 && (
                                      <div className="w-2 h-2 rounded-full bg-blue-500" title="Lifecycle Stages" />
                                    )}
                                    {type.maintenanceSchedules?.length > 0 && (
                                      <div className="w-2 h-2 rounded-full bg-orange-500" title="Maintenance Schedules" />
                                    )}
                                    {type.depreciationSettings && (
                                      <div className="w-2 h-2 rounded-full bg-red-500" title="Depreciation Settings" />
                                    )}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge variant={type.isActive ? "default" : "secondary"}>
                                  {type.isActive ? "Active" : "Inactive"}
                                </Badge>
                              </TableCell>
                              <TableCell>{new Date(type.createdAt).toLocaleDateString()}</TableCell>
                              <TableCell className="text-right">
                                <div className="flex items-center justify-end space-x-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    asChild
                                    title="View Details"
                                  >
                                    <Link href={`/admin/asset-types/${type.id}`}>
                                      <Eye className="h-4 w-4" />
                                    </Link>
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    asChild
                                    title="Configure"
                                  >
                                    <Link href={`/admin/asset-types/${type.id}/forms`}>
                                      <Cog className="h-4 w-4" />
                                    </Link>
                                  </Button>
                                  <Button variant="ghost" size="sm" onClick={() => handleEdit(type)}>
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button variant="ghost" size="sm" onClick={() => handleDelete(type.id)}>
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </ScrollArea>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      case "categories":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Asset Categories</CardTitle>
                <CardDescription>Organize asset types into logical categories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center h-[400px]">
                  <div className="text-center space-y-4">
                    <Settings className="mx-auto h-12 w-12 text-muted-foreground" />
                    <p className="text-lg font-medium">Categories Management</p>
                    <p className="text-sm text-muted-foreground">Hierarchical organization of asset types</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      case "templates":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Asset Type Templates</CardTitle>
                    <CardDescription>Pre-configured templates for common asset types</CardDescription>
                  </div>
                  <Button onClick={() => setIsTemplateDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Browse Templates
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <TemplateLibrary onTemplateSelect={handleTemplateSelect} />
              </CardContent>
            </Card>
          </div>
        );
      case "analytics":
        return (
          <div className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Usage Analytics</CardTitle>
                  <CardDescription>Asset type usage and trends</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-[300px]">
                    <div className="text-center space-y-4">
                      <TrendingUp className="mx-auto h-12 w-12 text-muted-foreground" />
                      <p className="text-lg font-medium">Analytics Dashboard</p>
                      <p className="text-sm text-muted-foreground">Usage patterns and insights</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                  <CardDescription>Asset type performance indicators</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-[300px]">
                    <div className="text-center space-y-4">
                      <CheckCircle className="mx-auto h-12 w-12 text-muted-foreground" />
                      <p className="text-lg font-medium">Performance Dashboard</p>
                      <p className="text-sm text-muted-foreground">Key performance indicators</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        );
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Asset Type</DialogTitle>
            <DialogDescription>Define a new asset type with custom fields and configuration</DialogDescription>
          </DialogHeader>
          <AssetTypeFormComponent
            onSave={() => {
              setIsCreateDialogOpen(false)
              loadData()
            }}
          />
        </DialogContent>
      </Dialog>

      <AssetTypeTemplateSelector
        isOpen={isTemplateDialogOpen}
        onOpenChange={setIsTemplateDialogOpen}
        onTemplateSelect={handleTemplateSelect}
        onCancel={() => setIsTemplateDialogOpen(false)}
      />

      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "types", content: getTabContent("types") },
          { id: "categories", content: getTabContent("categories") },
          { id: "templates", content: getTabContent("templates") },
          { id: "analytics", content: getTabContent("analytics") },
        ]}
      />




      {/* Asset Type Detail Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[1000px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Asset Type Details</DialogTitle>
            <DialogDescription>View and edit asset type configuration</DialogDescription>
          </DialogHeader>
          {selectedAssetType && (
            <AssetTypeDetailView
              assetType={selectedAssetType}
              onSave={() => {
                setIsEditDialogOpen(false)
                loadData()
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Asset Type Form Component
function AssetTypeForm({ onSave }: { onSave: () => void }) {
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    description: "",
    category: "",
    color: "#3B82F6",
    isActive: true,
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    // Implementation would create the asset type
    onSave()
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Asset Type Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="Enter asset type name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="code">Code *</Label>
          <Input
            id="code"
            value={formData.code}
            onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
            placeholder="Enter unique code"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Describe this asset type..."
          rows={3}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="it">IT Equipment</SelectItem>
              <SelectItem value="vehicles">Vehicles</SelectItem>
              <SelectItem value="machinery">Machinery</SelectItem>
              <SelectItem value="furniture">Furniture</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="color">Color</Label>
          <Input
            id="color"
            type="color"
            value={formData.color}
            onChange={(e) => setFormData({ ...formData, color: e.target.value })}
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="isActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
        />
        <Label htmlFor="isActive">Active</Label>
      </div>

      <DialogFooter>
        <Button type="submit">Create Asset Type</Button>
      </DialogFooter>
    </form>
  )
}

// Asset Type Detail View Component
function AssetTypeDetailView({ assetType, onSave }: { assetType: AssetType; onSave: () => void }) {
  return (
    <Tabs defaultValue="general" className="w-full">
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="general">General</TabsTrigger>
        <TabsTrigger value="fields">Custom Fields</TabsTrigger>
        <TabsTrigger value="lifecycle">Lifecycle</TabsTrigger>
        <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
        <TabsTrigger value="depreciation">Depreciation</TabsTrigger>
      </TabsList>

      <TabsContent value="general" className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Name</Label>
            <Input value={assetType.name} readOnly />
          </div>
          <div className="space-y-2">
            <Label>Code</Label>
            <Input value={assetType.code} readOnly />
          </div>
        </div>
        <div className="space-y-2">
          <Label>Description</Label>
          <Textarea value={assetType.description} readOnly rows={3} />
        </div>
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label>Category</Label>
            <Input value={assetType.category.name} readOnly />
          </div>
          <div className="space-y-2">
            <Label>Color</Label>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 rounded border" style={{ backgroundColor: assetType.color }} />
              <Input value={assetType.color} readOnly />
            </div>
          </div>
          <div className="space-y-2">
            <Label>Status</Label>
            <div className="pt-2">{getStatusBadge(assetType.isActive)}</div>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="fields" className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Custom Fields ({assetType.customFields.length})</h3>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Field
          </Button>
        </div>
        <ScrollArea className="h-[400px]">
          <div className="space-y-4">
            {assetType.customFields.map((field) => (
              <Card key={field.id}>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{field.label}</p>
                        <Badge variant="outline">{field.type}</Badge>
                        {field.isRequired && <Badge variant="secondary">Required</Badge>}
                      </div>
                      <p className="text-sm text-muted-foreground">{field.description}</p>
                      <p className="text-xs text-muted-foreground">Field Name: {field.name}</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </TabsContent>

      <TabsContent value="lifecycle" className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Lifecycle Stages ({assetType.lifecycleStages.length})</h3>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Stage
          </Button>
        </div>
        <ScrollArea className="h-[400px]">
          <div className="space-y-4">
            {assetType.lifecycleStages
              .sort((a, b) => a.order - b.order)
              .map((stage, index) => (
                <Card key={stage.id}>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted text-sm font-medium">
                          {index + 1}
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <p className="font-medium">{stage.name}</p>
                            <Badge variant="outline" style={{ backgroundColor: stage.color, borderColor: stage.color }}>
                              {stage.code}
                            </Badge>
                            {stage.isInitial && <Badge variant="secondary">Initial</Badge>}
                            {stage.isFinal && <Badge variant="secondary">Final</Badge>}
                          </div>
                          <p className="text-sm text-muted-foreground">{stage.description}</p>
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                            <span>Transitions to: {stage.allowedTransitions.length} stages</span>
                            <span>•</span>
                            <span>Required fields: {stage.requiredFields.length}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </ScrollArea>
      </TabsContent>

      <TabsContent value="maintenance" className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Maintenance Schedules ({assetType.maintenanceSchedules.length})</h3>
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Schedule
          </Button>
        </div>
        <ScrollArea className="h-[400px]">
          <div className="space-y-4">
            {assetType.maintenanceSchedules.map((schedule) => (
              <Card key={schedule.id}>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{schedule.name}</p>
                        <Badge variant="outline">{schedule.type}</Badge>
                        <Badge
                          variant="outline"
                          className={
                            schedule.priority === "critical"
                              ? "border-red-500 text-red-700"
                              : schedule.priority === "high"
                                ? "border-orange-500 text-orange-700"
                                : schedule.priority === "medium"
                                  ? "border-yellow-500 text-yellow-700"
                                  : "border-green-500 text-green-700"
                          }
                        >
                          {schedule.priority}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{schedule.description}</p>
                      <div className="grid grid-cols-3 gap-4 text-xs text-muted-foreground">
                        <div>
                          <span className="font-medium">Frequency:</span> Every {schedule.frequency.interval}{" "}
                          {schedule.frequency.type}
                        </div>
                        <div>
                          <span className="font-medium">Duration:</span> {schedule.estimatedDuration} minutes
                        </div>
                        <div>
                          <span className="font-medium">Cost:</span> ${schedule.estimatedCost}
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        <span className="font-medium">Checklist items:</span> {schedule.checklistItems.length}
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </TabsContent>

      <TabsContent value="depreciation" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Depreciation Settings</CardTitle>
            <CardDescription>Configure how assets of this type depreciate over time</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Depreciation Method</Label>
                <Input value={assetType.depreciationSettings.method.replace("_", " ")} readOnly />
              </div>
              <div className="space-y-2">
                <Label>Useful Life</Label>
                <Input
                  value={`${assetType.depreciationSettings.usefulLife} ${assetType.depreciationSettings.usefulLifeUnit}`}
                  readOnly
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Salvage Value</Label>
                <Input
                  value={
                    assetType.depreciationSettings.salvageValueType === "percentage"
                      ? `${assetType.depreciationSettings.salvageValue}%`
                      : `$R{assetType.depreciationSettings.salvageValue}`
                  }
                  readOnly
                />
              </div>
              <div className="space-y-2">
                <Label>Start Date</Label>
                <Input value={new Date(assetType.depreciationSettings.startDate).toLocaleDateString()} readOnly />
              </div>
            </div>

            {/* Depreciation Calculator */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-base">Depreciation Calculator</CardTitle>
                <CardDescription>Calculate depreciation for a sample asset value</CardDescription>
              </CardHeader>
              <CardContent>
                <DepreciationCalculator depreciationSettings={assetType.depreciationSettings} />
              </CardContent>
            </Card>
          </CardContent>
        </Card>
      </TabsContent>

      <DialogFooter>
        <Button variant="outline" onClick={onSave}>
          Close
        </Button>
        <Button onClick={onSave}>Save Changes</Button>
      </DialogFooter>
    </Tabs>
  )
}

// Depreciation Calculator Component
function DepreciationCalculator({ depreciationSettings }: { depreciationSettings: any }) {
  const [assetValue, setAssetValue] = useState(10000)
  const [calculation, setCalculation] = useState<any>(null)
  const assetTypeService = AssetTypeService.getInstance()

  useEffect(() => {
    const result = assetTypeService.calculateDepreciation(assetValue, depreciationSettings)
    setCalculation(result)
  }, [assetValue, depreciationSettings])

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="assetValue">Asset Value ($)</Label>
        <Input
          id="assetValue"
          type="number"
          value={assetValue}
          onChange={(e) => setAssetValue(Number(e.target.value))}
          min="0"
          step="100"
        />
      </div>

      {calculation && (
        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardContent className="pt-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Annual Depreciation</p>
                <p className="text-2xl font-bold text-red-600">${calculation.annualDepreciation.toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Current Book Value</p>
                <p className="text-2xl font-bold text-green-600">${calculation.bookValue.toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Accumulated Depreciation</p>
                <p className="text-2xl font-bold text-orange-600">
                  ${calculation.accumulatedDepreciation.toLocaleString()}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Remaining Life</p>
                <p className="text-2xl font-bold text-blue-600">{calculation.remainingLife.toFixed(1)} years</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
