"use client";

import React, { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { useHeaderTabs } from "@/hooks/use-admin-tabs";
import { HeaderTabContent } from "@/components/ui/header-tab-content";
import { getCustomFieldsHeaderConfig } from "@/lib/utils/asset-type-config-header-configs";
import { getCustomFieldsHeaderTabs } from "@/lib/utils/asset-type-config-tabs-configs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  FileText, 
  Settings, 
  GitBranch,
  Workflow,
  Plus,
  Edit,
  Trash2,
  Eye,
  AlertCircle,
  CheckCircle,
  Info,
  Type,
  Hash,
  Calendar,
  ToggleLeft,
  List,
  FileImage
} from "lucide-react";
import { CustomField } from "@/lib/modules/asset-types/types";
import { toast } from "@/components/ui/use-toast";

interface AssetTypeData {
  id: string;
  name: string;
  code: string;
  description: string;
  category: {
    id: string;
    name: string;
  };
  customFields: CustomField[];
}

const FIELD_TYPES = [
  { value: "text", label: "Text", icon: Type },
  { value: "number", label: "Number", icon: Hash },
  { value: "date", label: "Date", icon: Calendar },
  { value: "boolean", label: "Boolean", icon: ToggleLeft },
  { value: "select", label: "Select", icon: List },
  { value: "textarea", label: "Text Area", icon: FileText },
  { value: "file", label: "File", icon: FileImage },
];

export default function CustomFieldsConfigPage() {
  const params = useParams();
  const router = useRouter();
  const assetTypeId = params.id as string;

  const [assetType, setAssetType] = useState<AssetTypeData | null>(null);
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isAddFieldDialogOpen, setIsAddFieldDialogOpen] = useState(false);
  const [editingField, setEditingField] = useState<CustomField | null>(null);

  // New field form state
  const [newField, setNewField] = useState({
    name: "",
    label: "",
    type: "text",
    description: "",
    isRequired: false,
    isUnique: false,
    defaultValue: "",
    options: [] as string[],
    groupName: "",
  });

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [assetTypeId]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load asset type data with custom fields
      const response = await fetch(`/api/asset-types/${assetTypeId}`);
      if (!response.ok) {
        throw new Error("Failed to load asset type");
      }
      const data = await response.json();
      setAssetType(data);
      setCustomFields(data.customFields || []);

    } catch (error) {
      console.error("Error loading data:", error);
      setError("Failed to load configuration data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      // Implement save logic here
      toast({
        title: "Success",
        description: "Custom fields configuration saved successfully.",
      });
    } catch (error) {
      console.error("Error saving:", error);
      toast({
        title: "Error",
        description: "Failed to save custom fields configuration.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/admin/asset-types/${assetTypeId}`);
  };

  const handleAddField = () => {
    setIsAddFieldDialogOpen(true);
  };

  const handleCreateField = async () => {
    try {
      const response = await fetch(`/api/asset-types/${assetTypeId}/custom-fields`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newField),
      });

      if (!response.ok) {
        throw new Error("Failed to create custom field");
      }

      const createdField = await response.json();
      setCustomFields([...customFields, createdField]);
      setIsAddFieldDialogOpen(false);
      setNewField({
        name: "",
        label: "",
        type: "text",
        description: "",
        isRequired: false,
        isUnique: false,
        defaultValue: "",
        options: [],
        groupName: "",
      });

      toast({
        title: "Success",
        description: "Custom field created successfully.",
      });

    } catch (error) {
      console.error("Error creating field:", error);
      toast({
        title: "Error",
        description: "Failed to create custom field.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteField = async (fieldId: string) => {
    try {
      const response = await fetch(`/api/asset-types/${assetTypeId}/custom-fields/${fieldId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete custom field");
      }

      setCustomFields(customFields.filter(field => field.id !== fieldId));
      toast({
        title: "Success",
        description: "Custom field deleted successfully.",
      });

    } catch (error) {
      console.error("Error deleting field:", error);
      toast({
        title: "Error",
        description: "Failed to delete custom field.",
        variant: "destructive",
      });
    }
  };

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => 
    getCustomFieldsHeaderConfig(
      assetTypeId,
      assetType?.name || "Asset Type",
      handleSave,
      handleBack,
      handleAddField
    ), 
    [assetTypeId, assetType?.name]
  );

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getCustomFieldsHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  const getFieldTypeIcon = (type: string) => {
    const fieldType = FIELD_TYPES.find(ft => ft.value === type);
    return fieldType ? fieldType.icon : Type;
  };

  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Custom Fields Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Total Fields</p>
                        <p className="text-2xl font-bold">{customFields.length}</p>
                      </div>
                      <FileText className="h-8 w-8 text-blue-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Required Fields</p>
                        <p className="text-2xl font-bold">{customFields.filter(f => f.isRequired).length}</p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Unique Fields</p>
                        <p className="text-2xl font-bold">{customFields.filter(f => f.isUnique).length}</p>
                      </div>
                      <Settings className="h-8 w-8 text-orange-500" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Custom Fields</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {customFields.map((field) => {
                    const IconComponent = getFieldTypeIcon(field.type);
                    return (
                      <div key={field.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <IconComponent className="h-5 w-5 text-blue-500" />
                          <div>
                            <p className="font-medium">{field.label}</p>
                            <p className="text-sm text-muted-foreground">{field.name} • {field.type}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {field.isRequired && <Badge variant="secondary">Required</Badge>}
                          {field.isUnique && <Badge variant="outline">Unique</Badge>}
                          <Button variant="outline" size="sm" onClick={() => setEditingField(field)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleDeleteField(field.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                  {customFields.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No custom fields configured yet. Start by adding custom fields for this asset type.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return (
          <div className="space-y-6">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This configuration section is under development.
              </AlertDescription>
            </Alert>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading custom fields configuration...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "basic-fields", content: getTabContent("basic-fields") },
          { id: "advanced-fields", content: getTabContent("advanced-fields") },
          { id: "field-groups", content: getTabContent("field-groups") },
          { id: "conditional-logic", content: getTabContent("conditional-logic") },
        ]}
      />

      {/* Add Field Dialog */}
      <Dialog open={isAddFieldDialogOpen} onOpenChange={setIsAddFieldDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add Custom Field</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="field-name">Field Name</Label>
                <Input
                  id="field-name"
                  value={newField.name}
                  onChange={(e) => setNewField({ ...newField, name: e.target.value })}
                  placeholder="field_name"
                />
              </div>
              <div>
                <Label htmlFor="field-label">Field Label</Label>
                <Input
                  id="field-label"
                  value={newField.label}
                  onChange={(e) => setNewField({ ...newField, label: e.target.value })}
                  placeholder="Field Label"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="field-type">Field Type</Label>
              <Select value={newField.type} onValueChange={(value) => setNewField({ ...newField, type: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {FIELD_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center gap-2">
                        <type.icon className="h-4 w-4" />
                        {type.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="field-description">Description</Label>
              <Textarea
                id="field-description"
                value={newField.description}
                onChange={(e) => setNewField({ ...newField, description: e.target.value })}
                placeholder="Field description..."
              />
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="required"
                  checked={newField.isRequired}
                  onCheckedChange={(checked) => setNewField({ ...newField, isRequired: checked })}
                />
                <Label htmlFor="required">Required</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="unique"
                  checked={newField.isUnique}
                  onCheckedChange={(checked) => setNewField({ ...newField, isUnique: checked })}
                />
                <Label htmlFor="unique">Unique</Label>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsAddFieldDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateField}>
                Create Field
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
