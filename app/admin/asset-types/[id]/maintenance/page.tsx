"use client";

import React, { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { useHeaderTabs } from "@/hooks/use-admin-tabs";
import { HeaderTabContent } from "@/components/ui/header-tab-content";
import { getMaintenanceSchedulesHeaderConfig } from "@/lib/utils/asset-type-config-header-configs";
import { getMaintenanceSchedulesHeaderTabs } from "@/lib/utils/asset-type-config-tabs-configs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  Wrench,
  Clock,
  DollarSign,
  Plus,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  Info,
  Calendar,
  Settings,
  TrendingUp
} from "lucide-react";
import { MaintenanceSchedule } from "@/lib/modules/asset-types/types";
import { toast } from "@/components/ui/use-toast";

interface AssetTypeData {
  id: string;
  name: string;
  code: string;
  description: string;
  category: {
    id: string;
    name: string;
  };
  maintenanceSchedules: MaintenanceSchedule[];
}

const MAINTENANCE_TYPES = [
  { value: "preventive", label: "Preventive", icon: Wrench },
  { value: "predictive", label: "Predictive", icon: TrendingUp },
  { value: "corrective", label: "Corrective", icon: Settings },
  { value: "emergency", label: "Emergency", icon: AlertCircle },
];

const PRIORITY_LEVELS = [
  { value: "low", label: "Low", color: "bg-blue-100 text-blue-800" },
  { value: "medium", label: "Medium", color: "bg-yellow-100 text-yellow-800" },
  { value: "high", label: "High", color: "bg-orange-100 text-orange-800" },
  { value: "critical", label: "Critical", color: "bg-red-100 text-red-800" },
];

export default function MaintenanceSchedulesConfigPage() {
  const params = useParams();
  const router = useRouter();
  const assetTypeId = params.id as string;

  const [assetType, setAssetType] = useState<AssetTypeData | null>(null);
  const [maintenanceSchedules, setMaintenanceSchedules] = useState<MaintenanceSchedule[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isAddScheduleDialogOpen, setIsAddScheduleDialogOpen] = useState(false);
  const [editingSchedule, setEditingSchedule] = useState<MaintenanceSchedule | null>(null);

  // New schedule form state
  const [newSchedule, setNewSchedule] = useState({
    name: "",
    type: "preventive",
    description: "",
    intervalDays: 30,
    estimatedDuration: 60,
    estimatedCost: 0,
    priority: "medium",
    checklist: [] as string[],
    isActive: true,
  });

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [assetTypeId]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load asset type data with maintenance schedules
      const response = await fetch(`/api/asset-types/${assetTypeId}`);
      if (!response.ok) {
        throw new Error("Failed to load asset type");
      }
      const data = await response.json();
      setAssetType(data);
      setMaintenanceSchedules(data.maintenanceSchedules || []);

    } catch (error) {
      console.error("Error loading data:", error);
      setError("Failed to load configuration data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      // Implement save logic here
      toast({
        title: "Success",
        description: "Maintenance schedules configuration saved successfully.",
      });
    } catch (error) {
      console.error("Error saving:", error);
      toast({
        title: "Error",
        description: "Failed to save maintenance schedules configuration.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/admin/asset-types/${assetTypeId}`);
  };

  const handleAddSchedule = () => {
    setIsAddScheduleDialogOpen(true);
  };

  const handleCreateSchedule = async () => {
    try {
      const response = await fetch(`/api/maintenance/schedules`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...newSchedule,
          assetTypeId,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create maintenance schedule");
      }

      const result = await response.json();
      setMaintenanceSchedules([...maintenanceSchedules, result.schedule]);
      setIsAddScheduleDialogOpen(false);
      setNewSchedule({
        name: "",
        type: "preventive",
        description: "",
        intervalDays: 30,
        estimatedDuration: 60,
        estimatedCost: 0,
        priority: "medium",
        checklist: [],
        isActive: true,
      });

      toast({
        title: "Success",
        description: "Maintenance schedule created successfully.",
      });

    } catch (error) {
      console.error("Error creating schedule:", error);
      toast({
        title: "Error",
        description: "Failed to create maintenance schedule.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteSchedule = async (scheduleId: string) => {
    try {
      const response = await fetch(`/api/maintenance/schedules/${scheduleId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete maintenance schedule");
      }

      setMaintenanceSchedules(maintenanceSchedules.filter(schedule => schedule.id !== scheduleId));
      toast({
        title: "Success",
        description: "Maintenance schedule deleted successfully.",
      });

    } catch (error) {
      console.error("Error deleting schedule:", error);
      toast({
        title: "Error",
        description: "Failed to delete maintenance schedule.",
        variant: "destructive",
      });
    }
  };

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => 
    getMaintenanceSchedulesHeaderConfig(
      assetTypeId,
      assetType?.name || "Asset Type",
      handleSave,
      handleBack,
      handleAddSchedule
    ), 
    [assetTypeId, assetType?.name]
  );

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getMaintenanceSchedulesHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  const getMaintenanceTypeIcon = (type: string) => {
    const maintenanceType = MAINTENANCE_TYPES.find(mt => mt.value === type);
    return maintenanceType ? maintenanceType.icon : Wrench;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityLevel = PRIORITY_LEVELS.find(pl => pl.value === priority);
    return priorityLevel ? priorityLevel : PRIORITY_LEVELS[1]; // Default to medium
  };

  const formatInterval = (days: number) => {
    if (days < 7) return `${days} day${days !== 1 ? 's' : ''}`;
    if (days < 30) return `${Math.floor(days / 7)} week${Math.floor(days / 7) !== 1 ? 's' : ''}`;
    if (days < 365) return `${Math.floor(days / 30)} month${Math.floor(days / 30) !== 1 ? 's' : ''}`;
    return `${Math.floor(days / 365)} year${Math.floor(days / 365) !== 1 ? 's' : ''}`;
  };

  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Maintenance Schedules Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Total Schedules</p>
                        <p className="text-2xl font-bold">{maintenanceSchedules.length}</p>
                      </div>
                      <Wrench className="h-8 w-8 text-blue-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Active Schedules</p>
                        <p className="text-2xl font-bold">{maintenanceSchedules.filter(s => s.isActive).length}</p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Avg. Interval</p>
                        <p className="text-2xl font-bold">
                          {maintenanceSchedules.length > 0 
                            ? Math.round(maintenanceSchedules.reduce((sum, s) => sum + s.intervalDays, 0) / maintenanceSchedules.length)
                            : 0} days
                        </p>
                      </div>
                      <Clock className="h-8 w-8 text-orange-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Avg. Cost</p>
                        <p className="text-2xl font-bold">
                          ${maintenanceSchedules.length > 0 
                            ? Math.round(maintenanceSchedules.reduce((sum, s) => sum + (s.estimatedCost || 0), 0) / maintenanceSchedules.length)
                            : 0}
                        </p>
                      </div>
                      <DollarSign className="h-8 w-8 text-purple-500" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Maintenance Schedules</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {maintenanceSchedules.map((schedule) => {
                    const IconComponent = getMaintenanceTypeIcon(schedule.type);
                    const priorityBadge = getPriorityBadge(schedule.priority);
                    return (
                      <div key={schedule.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <IconComponent className="h-5 w-5 text-blue-500" />
                          <div>
                            <div className="flex items-center gap-2">
                              <p className="font-medium">{schedule.name}</p>
                              <Badge className={priorityBadge.color}>{priorityBadge.label}</Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {schedule.type} • Every {formatInterval(schedule.intervalDays)}
                              {schedule.estimatedDuration && ` • ${schedule.estimatedDuration} min`}
                              {schedule.estimatedCost && ` • $${schedule.estimatedCost}`}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {schedule.isActive ? (
                            <Badge variant="default">Active</Badge>
                          ) : (
                            <Badge variant="outline">Inactive</Badge>
                          )}
                          <Button variant="outline" size="sm" onClick={() => setEditingSchedule(schedule)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleDeleteSchedule(schedule.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                  {maintenanceSchedules.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No maintenance schedules configured yet. Start by adding schedules for this asset type.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return (
          <div className="space-y-6">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This configuration section is under development.
              </AlertDescription>
            </Alert>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading maintenance schedules configuration...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "preventive", content: getTabContent("preventive") },
          { id: "predictive", content: getTabContent("predictive") },
          { id: "corrective", content: getTabContent("corrective") },
          { id: "templates", content: getTabContent("templates") },
        ]}
      />

      {/* Add Schedule Dialog */}
      <Dialog open={isAddScheduleDialogOpen} onOpenChange={setIsAddScheduleDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add Maintenance Schedule</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="schedule-name">Schedule Name</Label>
                <Input
                  id="schedule-name"
                  value={newSchedule.name}
                  onChange={(e) => setNewSchedule({ ...newSchedule, name: e.target.value })}
                  placeholder="Schedule Name"
                />
              </div>
              <div>
                <Label htmlFor="schedule-type">Type</Label>
                <Select value={newSchedule.type} onValueChange={(value) => setNewSchedule({ ...newSchedule, type: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {MAINTENANCE_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center gap-2">
                          <type.icon className="h-4 w-4" />
                          {type.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="schedule-description">Description</Label>
              <Textarea
                id="schedule-description"
                value={newSchedule.description}
                onChange={(e) => setNewSchedule({ ...newSchedule, description: e.target.value })}
                placeholder="Schedule description..."
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="interval-days">Interval (Days)</Label>
                <Input
                  id="interval-days"
                  type="number"
                  value={newSchedule.intervalDays}
                  onChange={(e) => setNewSchedule({ ...newSchedule, intervalDays: parseInt(e.target.value) })}
                />
              </div>
              <div>
                <Label htmlFor="duration">Duration (Minutes)</Label>
                <Input
                  id="duration"
                  type="number"
                  value={newSchedule.estimatedDuration}
                  onChange={(e) => setNewSchedule({ ...newSchedule, estimatedDuration: parseInt(e.target.value) })}
                />
              </div>
              <div>
                <Label htmlFor="cost">Estimated Cost ($)</Label>
                <Input
                  id="cost"
                  type="number"
                  step="0.01"
                  value={newSchedule.estimatedCost}
                  onChange={(e) => setNewSchedule({ ...newSchedule, estimatedCost: parseFloat(e.target.value) })}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select value={newSchedule.priority} onValueChange={(value) => setNewSchedule({ ...newSchedule, priority: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PRIORITY_LEVELS.map((priority) => (
                    <SelectItem key={priority.value} value={priority.value}>
                      {priority.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={newSchedule.isActive}
                onCheckedChange={(checked) => setNewSchedule({ ...newSchedule, isActive: checked })}
              />
              <Label htmlFor="active">Active</Label>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsAddScheduleDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateSchedule}>
                Create Schedule
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
