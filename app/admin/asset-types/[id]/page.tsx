"use client";

import React, { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { useHeaderTabs } from "@/hooks/use-admin-tabs";
import { HeaderTabContent } from "@/components/ui/header-tab-content";
import { getAssetTypeDetailHeaderConfig } from "@/lib/utils/asset-type-config-header-configs";
import { getAssetTypeDetailHeaderTabs } from "@/lib/utils/asset-type-config-tabs-configs";

import { CustomField, DepreciationSettings, LifecycleStage, MaintenanceSchedule } from "@/lib/modules/asset-types/types";
import { AssetTypeConfigNavigation } from "@/components/asset-types/asset-type-config-navigation";
import { AssetTypeFormsGrid } from "@/components/asset-types/asset-type-forms-grid";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Settings,
  TrendingDown,
  GitBranch,
  Wrench,
  AlertCircle,
  Eye
} from "lucide-react";

interface AssetTypeData {
  id: string;
  name: string;
  code: string;
  description: string;
  categoryId: string;
  category: {
    id: string;
    name: string;
  };
  customFields: CustomField[];
  depreciationSettings?: DepreciationSettings;
  lifecycleStages: LifecycleStage[];
  maintenanceSchedules: MaintenanceSchedule[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function AssetTypeDetailPage() {
  const params = useParams();
  const router = useRouter();
  const assetTypeId = params.id as string;

  const [assetType, setAssetType] = useState<AssetTypeData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAssetType();
  }, [assetTypeId]);

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => {
    if (!assetType) return null;
    return getAssetTypeDetailHeaderConfig(
      assetTypeId,
      assetType.name,
      assetType.code,
      assetType.isActive,
      () => {
        // Handle edit
        console.log("Edit asset type");
      },
      () => router.back()
    );
  }, [assetTypeId, assetType, router]);

  // Set up the header for this page
  useAdminHeader(headerConfig || {
    title: "Asset Type",
    description: "Loading...",
    breadcrumbs: [
      { label: "Admin", url: "/admin" },
      { label: "Asset Types", url: "/admin/asset-types" },
    ],
    variant: 'management',
    actions: [],
  });

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getAssetTypeDetailHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  const loadAssetType = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-types/${assetTypeId}`);
      
      if (!response.ok) {
        throw new Error("Failed to load asset type");
      }

      const data = await response.json();
      setAssetType(data);

    } catch (error) {
      console.error("Error loading asset type:", error);
      setError("Failed to load asset type. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };



  const getTabContent = (tabId: string) => {
    if (!assetType) return null;

    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-6">
            {/* Description */}
            {assetType.description && (
              <Card>
                <CardContent className="p-4">
                  <p className="text-muted-foreground">{assetType.description}</p>
                </CardContent>
              </Card>
            )}

            {/* Configuration Navigation */}
            <AssetTypeConfigNavigation
              assetTypeId={assetTypeId}
              assetTypeName={assetType.name}
              stats={{
                forms: 0, // TODO: Get actual counts
                customFields: assetType.customFields?.length || 0,
                lifecycleStages: assetType.lifecycleStages?.length || 0,
                maintenanceSchedules: assetType.maintenanceSchedules?.length || 0,
                hasDepreciationSettings: !!assetType.depreciationSettings,
                workflows: 0, // TODO: Get actual count
              }}
            />
          </div>
        );

      case "forms":
        return (
          <AssetTypeFormsGrid
            assetTypeId={assetTypeId}
            assetTypeName={assetType?.name || "Asset Type"}
            customFields={assetType?.customFields || []}
            onCreateForm={(operationType) => {
              router.push(`/admin/asset-types/${assetTypeId}/forms`);
            }}
            onEditForm={(form) => {
              router.push(`/admin/asset-types/${assetTypeId}/forms/${form.id}/edit`);
            }}
            onPreviewForm={(form) => {
              // Could implement a preview modal or navigate to preview page
              console.log("Preview form:", form);
            }}
          />
        );

      case "fields":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Custom Fields
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Configure custom fields to capture additional information specific to this asset type.
                </p>
                {assetType.customFields && assetType.customFields.length > 0 && (
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm font-medium mb-2">Configured Fields: {assetType.customFields.length}</p>
                    <div className="flex flex-wrap gap-2">
                      {assetType.customFields.map((field) => (
                        <Badge key={field.id} variant="outline" className="flex items-center gap-1">
                          {field.isRequired && <span className="text-red-500">*</span>}
                          {field.label}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                <div className="flex gap-2">
                  <Button onClick={() => router.push(`/admin/asset-types/${assetTypeId}/fields`)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Fields
                  </Button>
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Form
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case "lifecycle":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Lifecycle Stages
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Configure lifecycle stages to track asset progression from acquisition to disposal.
                </p>
                {assetType.lifecycleStages && assetType.lifecycleStages.length > 0 && (
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm font-medium mb-2">Configured Stages: {assetType.lifecycleStages.length}</p>
                    <div className="flex flex-wrap gap-2">
                      {assetType.lifecycleStages
                        .sort((a, b) => a.order - b.order)
                        .map((stage) => (
                          <Badge key={stage.id} variant="outline" className="flex items-center gap-1">
                            <div
                              className="w-2 h-2 rounded-full"
                              style={{ backgroundColor: stage.color }}
                            />
                            {stage.name}
                          </Badge>
                        ))}
                    </div>
                  </div>
                )}
                <div className="flex gap-2">
                  <Button onClick={() => router.push(`/admin/asset-types/${assetTypeId}/lifecycle`)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Lifecycle
                  </Button>
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Flow
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case "maintenance":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                Maintenance Schedules
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Configure maintenance schedules to automate preventive, predictive, and corrective maintenance for this asset type.
                </p>
                {assetType.maintenanceSchedules && assetType.maintenanceSchedules.length > 0 && (
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm font-medium mb-2">Configured Schedules: {assetType.maintenanceSchedules.length}</p>
                    <div className="flex flex-wrap gap-2">
                      {assetType.maintenanceSchedules.map((schedule) => (
                        <Badge key={schedule.id} variant="outline">
                          {schedule.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                <div className="flex gap-2">
                  <Button onClick={() => router.push(`/admin/asset-types/${assetTypeId}/maintenance`)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Maintenance
                  </Button>
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Schedules
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case "depreciation":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingDown className="h-5 w-5" />
                Depreciation Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Configure depreciation methods, useful life, salvage values, and calculation parameters for this asset type.
                </p>
                {assetType.depreciationSettings && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted rounded-lg">
                    <div>
                      <label className="text-sm font-medium">Method</label>
                      <p className="text-sm text-muted-foreground capitalize">
                        {assetType.depreciationSettings.method?.replace('_', ' ') || 'Not configured'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Useful Life</label>
                      <p className="text-sm text-muted-foreground">
                        {assetType.depreciationSettings.usefulLife || 'Not set'} years
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Status</label>
                      <p className="text-sm text-muted-foreground">
                        {assetType.depreciationSettings.isActive ? 'Active' : 'Inactive'}
                      </p>
                    </div>
                  </div>
                )}
                <div className="flex gap-2">
                  <Button onClick={() => router.push(`/admin/asset-types/${assetTypeId}/depreciation`)}>
                    <Settings className="h-4 w-4 mr-2" />
                    Configure Depreciation
                  </Button>
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Calculation
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      default:
        return (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              This configuration section is under development.
            </AlertDescription>
          </Alert>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading asset type...</span>
        </div>
      </div>
    );
  }

  if (error || !assetType) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || "Asset type not found"}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "forms", content: getTabContent("forms") },
          { id: "fields", content: getTabContent("fields") },
          { id: "lifecycle", content: getTabContent("lifecycle") },
          { id: "maintenance", content: getTabContent("maintenance") },
          { id: "depreciation", content: getTabContent("depreciation") },
          { id: "workflows", content: getTabContent("workflows") },
        ]}
      />
    </div>
  );
}