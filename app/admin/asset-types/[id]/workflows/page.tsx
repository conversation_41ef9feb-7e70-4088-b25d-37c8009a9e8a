"use client";

import React, { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { useHeaderTabs } from "@/hooks/use-admin-tabs";
import { HeaderTabContent } from "@/components/ui/header-tab-content";
import { getWorkflowsHeaderConfig } from "@/lib/utils/asset-type-config-header-configs";
import { getWorkflowsHeaderTabs } from "@/lib/utils/asset-type-config-tabs-configs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  Workflow,
  Zap,
  GitBranch,
  Settings,
  Plus,
  Edit,
  Trash2,
  Play,
  Pause,
  AlertCircle,
  CheckCircle,
  Info,
  Clock,
  Target
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface AssetTypeData {
  id: string;
  name: string;
  code: string;
  description: string;
  category: {
    id: string;
    name: string;
  };
}

interface WorkflowData {
  id: string;
  name: string;
  description?: string;
  type: string;
  nodes: any[];
  edges: any[];
  webhooks?: any[];
  isActive: boolean;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

const WORKFLOW_TYPES = [
  { value: "asset-automation", label: "Asset Automation", icon: Zap },
  { value: "maintenance-trigger", label: "Maintenance Trigger", icon: Settings },
  { value: "lifecycle-transition", label: "Lifecycle Transition", icon: GitBranch },
  { value: "notification", label: "Notification", icon: Target },
];

const TRIGGER_EVENTS = [
  { value: "asset.created", label: "Asset Created" },
  { value: "asset.updated", label: "Asset Updated" },
  { value: "asset.transferred", label: "Asset Transferred" },
  { value: "asset.disposed", label: "Asset Disposed" },
  { value: "maintenance.due", label: "Maintenance Due" },
  { value: "lifecycle.changed", label: "Lifecycle Stage Changed" },
];

export default function WorkflowsConfigPage() {
  const params = useParams();
  const router = useRouter();
  const assetTypeId = params.id as string;

  const [assetType, setAssetType] = useState<AssetTypeData | null>(null);
  const [workflows, setWorkflows] = useState<WorkflowData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isAddWorkflowDialogOpen, setIsAddWorkflowDialogOpen] = useState(false);
  const [editingWorkflow, setEditingWorkflow] = useState<WorkflowData | null>(null);

  // New workflow form state
  const [newWorkflow, setNewWorkflow] = useState({
    name: "",
    description: "",
    type: "asset-automation",
    triggerEvent: "asset.created",
    isActive: true,
    tags: [] as string[],
  });

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [assetTypeId]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load asset type data
      const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`);
      if (!assetTypeResponse.ok) {
        throw new Error("Failed to load asset type");
      }
      const assetTypeData = await assetTypeResponse.json();
      setAssetType(assetTypeData);

      // Load workflows for this asset type
      const workflowsResponse = await fetch(`/api/workflows?assetTypeId=${assetTypeId}`);
      if (workflowsResponse.ok) {
        const workflowsData = await workflowsResponse.json();
        setWorkflows(workflowsData);
      }

    } catch (error) {
      console.error("Error loading data:", error);
      setError("Failed to load configuration data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      // Implement save logic here
      toast({
        title: "Success",
        description: "Workflows configuration saved successfully.",
      });
    } catch (error) {
      console.error("Error saving:", error);
      toast({
        title: "Error",
        description: "Failed to save workflows configuration.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/admin/asset-types/${assetTypeId}`);
  };

  const handleAddWorkflow = () => {
    setIsAddWorkflowDialogOpen(true);
  };

  const handleCreateWorkflow = async () => {
    try {
      const response = await fetch(`/api/workflows`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...newWorkflow,
          nodes: [],
          edges: [],
          createdBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create workflow");
      }

      const createdWorkflow = await response.json();
      setWorkflows([...workflows, createdWorkflow]);
      setIsAddWorkflowDialogOpen(false);
      setNewWorkflow({
        name: "",
        description: "",
        type: "asset-automation",
        triggerEvent: "asset.created",
        isActive: true,
        tags: [],
      });

      toast({
        title: "Success",
        description: "Workflow created successfully.",
      });

    } catch (error) {
      console.error("Error creating workflow:", error);
      toast({
        title: "Error",
        description: "Failed to create workflow.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteWorkflow = async (workflowId: string) => {
    try {
      const response = await fetch(`/api/workflows/${workflowId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete workflow");
      }

      setWorkflows(workflows.filter(workflow => workflow.id !== workflowId));
      toast({
        title: "Success",
        description: "Workflow deleted successfully.",
      });

    } catch (error) {
      console.error("Error deleting workflow:", error);
      toast({
        title: "Error",
        description: "Failed to delete workflow.",
        variant: "destructive",
      });
    }
  };

  const handleToggleWorkflow = async (workflowId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/workflows/${workflowId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isActive }),
      });

      if (!response.ok) {
        throw new Error("Failed to update workflow");
      }

      setWorkflows(workflows.map(workflow => 
        workflow.id === workflowId ? { ...workflow, isActive } : workflow
      ));

      toast({
        title: "Success",
        description: `Workflow ${isActive ? 'activated' : 'deactivated'} successfully.`,
      });

    } catch (error) {
      console.error("Error updating workflow:", error);
      toast({
        title: "Error",
        description: "Failed to update workflow.",
        variant: "destructive",
      });
    }
  };

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => 
    getWorkflowsHeaderConfig(
      assetTypeId,
      assetType?.name || "Asset Type",
      handleSave,
      handleBack,
      handleAddWorkflow
    ), 
    [assetTypeId, assetType?.name]
  );

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getWorkflowsHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  const getWorkflowTypeIcon = (type: string) => {
    const workflowType = WORKFLOW_TYPES.find(wt => wt.value === type);
    return workflowType ? workflowType.icon : Workflow;
  };

  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Workflows Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Total Workflows</p>
                        <p className="text-2xl font-bold">{workflows.length}</p>
                      </div>
                      <Workflow className="h-8 w-8 text-blue-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Active Workflows</p>
                        <p className="text-2xl font-bold">{workflows.filter(w => w.isActive).length}</p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Automation Workflows</p>
                        <p className="text-2xl font-bold">{workflows.filter(w => w.type === 'asset-automation').length}</p>
                      </div>
                      <Zap className="h-8 w-8 text-orange-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Notification Workflows</p>
                        <p className="text-2xl font-bold">{workflows.filter(w => w.type === 'notification').length}</p>
                      </div>
                      <Target className="h-8 w-8 text-purple-500" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Configured Workflows</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {workflows.map((workflow) => {
                    const IconComponent = getWorkflowTypeIcon(workflow.type);
                    return (
                      <div key={workflow.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <IconComponent className="h-5 w-5 text-blue-500" />
                          <div>
                            <div className="flex items-center gap-2">
                              <p className="font-medium">{workflow.name}</p>
                              <Badge variant="outline">{WORKFLOW_TYPES.find(wt => wt.value === workflow.type)?.label}</Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">{workflow.description}</p>
                            {workflow.tags.length > 0 && (
                              <div className="flex gap-1 mt-1">
                                {workflow.tags.map((tag, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {workflow.isActive ? (
                            <Badge variant="default">Active</Badge>
                          ) : (
                            <Badge variant="outline">Inactive</Badge>
                          )}
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleToggleWorkflow(workflow.id, !workflow.isActive)}
                          >
                            {workflow.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => setEditingWorkflow(workflow)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleDeleteWorkflow(workflow.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                  {workflows.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No workflows configured yet. Start by adding automated workflows for this asset type.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return (
          <div className="space-y-6">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This configuration section is under development.
              </AlertDescription>
            </Alert>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading workflows configuration...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "automation", content: getTabContent("automation") },
          { id: "triggers", content: getTabContent("triggers") },
          { id: "actions", content: getTabContent("actions") },
          { id: "monitoring", content: getTabContent("monitoring") },
        ]}
      />

      {/* Add Workflow Dialog */}
      <Dialog open={isAddWorkflowDialogOpen} onOpenChange={setIsAddWorkflowDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add Workflow</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="workflow-name">Workflow Name</Label>
                <Input
                  id="workflow-name"
                  value={newWorkflow.name}
                  onChange={(e) => setNewWorkflow({ ...newWorkflow, name: e.target.value })}
                  placeholder="Workflow Name"
                />
              </div>
              <div>
                <Label htmlFor="workflow-type">Type</Label>
                <Select value={newWorkflow.type} onValueChange={(value) => setNewWorkflow({ ...newWorkflow, type: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {WORKFLOW_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center gap-2">
                          <type.icon className="h-4 w-4" />
                          {type.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="workflow-description">Description</Label>
              <Textarea
                id="workflow-description"
                value={newWorkflow.description}
                onChange={(e) => setNewWorkflow({ ...newWorkflow, description: e.target.value })}
                placeholder="Workflow description..."
              />
            </div>

            <div>
              <Label htmlFor="trigger-event">Trigger Event</Label>
              <Select value={newWorkflow.triggerEvent} onValueChange={(value) => setNewWorkflow({ ...newWorkflow, triggerEvent: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TRIGGER_EVENTS.map((event) => (
                    <SelectItem key={event.value} value={event.value}>
                      {event.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={newWorkflow.isActive}
                onCheckedChange={(checked) => setNewWorkflow({ ...newWorkflow, isActive: checked })}
              />
              <Label htmlFor="active">Active</Label>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsAddWorkflowDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateWorkflow}>
                Create Workflow
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
