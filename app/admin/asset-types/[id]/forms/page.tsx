"use client";

import React, { useState, useEffect, use<PERSON>em<PERSON> } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { useHeaderTabs } from "@/hooks/use-admin-tabs";
import { HeaderTabContent } from "@/components/ui/header-tab-content";
import { getAssetTypeFormsHeaderConfig } from "@/lib/utils/asset-type-config-header-configs";
import { getAssetTypeFormsHeaderTabs } from "@/lib/utils/asset-type-config-tabs-configs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  TrendingDown, 
  Git<PERSON><PERSON><PERSON>,
  Plus,
  Edit,
  Trash2,
  Eye,
  AlertCircle,
  CheckCircle,
  Info
} from "lucide-react";
import { AssetOperationFormBuilder } from "@/components/form-builder/asset-operation-form-builder";
import { FormDefinition } from "@/components/form-builder";
import { AssetOperationType, ASSET_OPERATION_CONFIGS } from "@/lib/types/asset-type-forms";
import { CustomField } from "@/lib/modules/asset-types/types";
import { toast } from "@/components/ui/use-toast";

interface AssetTypeData {
  id: string;
  name: string;
  code: string;
  description: string;
  category: {
    id: string;
    name: string;
  };
  customFields: CustomField[];
}

interface AssetTypeForm {
  id: string;
  assetTypeId: string;
  formId: string;
  operationType: AssetOperationType;
  version: number;
  isDefault: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  form: {
    id: string;
    name: string;
    description?: string;
    sections: any[];
    settings: any;
  };
}

export default function AssetTypeFormsConfigPage() {
  const params = useParams();
  const router = useRouter();
  const assetTypeId = params.id as string;

  const [assetType, setAssetType] = useState<AssetTypeData | null>(null);
  const [forms, setForms] = useState<AssetTypeForm[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedOperationType, setSelectedOperationType] = useState<AssetOperationType | null>(null);
  const [isFormBuilderOpen, setIsFormBuilderOpen] = useState(false);
  const [editingForm, setEditingForm] = useState<AssetTypeForm | null>(null);

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [assetTypeId]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load asset type data
      const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`);
      if (!assetTypeResponse.ok) {
        throw new Error("Failed to load asset type");
      }
      const assetTypeData = await assetTypeResponse.json();
      setAssetType(assetTypeData);

      // Load forms data - using existing asset-type-forms API
      const formsResponse = await fetch(`/api/asset-type-forms?assetTypeId=${assetTypeId}&getAllForms=true`);
      if (formsResponse.ok) {
        const formsData = await formsResponse.json();
        setForms(formsData);
      }

    } catch (error) {
      console.error("Error loading data:", error);
      setError("Failed to load configuration data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSave = async (form: FormDefinition, operationType: AssetOperationType) => {
    try {
      setIsSaving(true);

      // First save the form definition
      const formResponse = await fetch("/api/form-definitions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: form.name,
          description: form.description,
          sections: form.sections,
          settings: form.settings,
          createdBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (!formResponse.ok) {
        throw new Error("Failed to save form definition");
      }

      const savedForm = await formResponse.json();

      // Then associate it with the asset type
      const associationResponse = await fetch("/api/asset-type-forms", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          assetTypeId,
          formId: savedForm.id,
          operationType,
          isDefault: true,
          createdBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (!associationResponse.ok) {
        throw new Error("Failed to associate form with asset type");
      }

      toast({
        title: "Success",
        description: "Form saved successfully.",
      });

      // Reload data and close modal
      await loadData();
      setIsFormBuilderOpen(false);
      setSelectedOperationType(null);
      setEditingForm(null);

    } catch (error) {
      console.error("Error saving form:", error);
      toast({
        title: "Error",
        description: "Failed to save form.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleFormPreview = (form: FormDefinition, operationType: AssetOperationType) => {
    // Open preview modal or navigate to preview page
    console.log("Preview form:", form, operationType);
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      toast({
        title: "Success",
        description: "Forms configuration saved successfully.",
      });
    } catch (error) {
      console.error("Error saving:", error);
      toast({
        title: "Error",
        description: "Failed to save forms configuration.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/admin/asset-types/${assetTypeId}`);
  };

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => 
    getAssetTypeFormsHeaderConfig(
      assetTypeId,
      assetType?.name || "Asset Type",
      handleSave,
      handleBack
    ), 
    [assetTypeId, assetType?.name]
  );

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getAssetTypeFormsHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  // Helper function to render form configuration tab
  const renderFormConfigTab = (operationType: AssetOperationType, title: string, description: string, icon: React.ElementType) => {
    const form = forms.find(f => f.operationType === operationType);
    const Icon = icon;

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon className="h-5 w-5" />
              {title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              {description}
            </p>
            {form ? (
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{form.form.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {form.form.description || "No description"}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        {form.isDefault && <Badge variant="secondary">Default</Badge>}
                        {form.isActive ? (
                          <Badge variant="default">Active</Badge>
                        ) : (
                          <Badge variant="outline">Inactive</Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/admin/asset-types/${assetTypeId}/forms/${form.id}/edit`)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Form
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedOperationType(operationType);
                          setEditingForm(form);
                          setIsFormBuilderOpen(true);
                        }}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Preview
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <Button onClick={() => {
                setSelectedOperationType(operationType);
                setIsFormBuilderOpen(true);
              }}>
                <Plus className="mr-2 h-4 w-4" />
                Configure {title}
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    );
  };

  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Forms Configuration Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Total Forms</p>
                        <p className="text-2xl font-bold">{forms.length}</p>
                      </div>
                      <FileText className="h-8 w-8 text-blue-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Active Forms</p>
                        <p className="text-2xl font-bold">{forms.filter(f => f.isActive).length}</p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Default Forms</p>
                        <p className="text-2xl font-bold">{forms.filter(f => f.isDefault).length}</p>
                      </div>
                      <Settings className="h-8 w-8 text-orange-500" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Configured Forms</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {forms.map((form) => (
                    <div key={form.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="font-medium">{form.form.name}</p>
                          <p className="text-sm text-muted-foreground">{form.operationType}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {form.isDefault && <Badge variant="secondary">Default</Badge>}
                        {form.isActive ? (
                          <Badge variant="default">Active</Badge>
                        ) : (
                          <Badge variant="outline">Inactive</Badge>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/asset-types/${assetTypeId}/forms/${form.id}/edit`)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                  {forms.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No forms configured yet. Start by creating forms for different operations.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "create-form":
        return renderFormConfigTab(
          "asset.create",
          "Asset Creation Form",
          "Configure the form used when creating new assets of this type.",
          FileText
        );

      case "update-form":
        return renderFormConfigTab(
          "asset.update",
          "Asset Update Form",
          "Configure the form used when updating existing assets of this type.",
          Settings
        );

      case "maintenance-form":
        return renderFormConfigTab(
          "maintenance.log",
          "Maintenance Log Form",
          "Configure the form used for logging maintenance activities.",
          Wrench
        );

      case "disposal-form":
        return renderFormConfigTab(
          "asset.disposal",
          "Asset Disposal Form",
          "Configure the form used when disposing of assets.",
          TrendingDown
        );

      case "transfer-form":
        return renderFormConfigTab(
          "asset.transfer",
          "Asset Transfer Form",
          "Configure the form used when transferring assets between locations or departments.",
          GitBranch
        );

      default:
        return (
          <div className="space-y-6">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This configuration section is under development.
              </AlertDescription>
            </Alert>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading forms configuration...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "create-form", content: getTabContent("create-form") },
          { id: "update-form", content: getTabContent("update-form") },
          { id: "maintenance-form", content: getTabContent("maintenance-form") },
          { id: "disposal-form", content: getTabContent("disposal-form") },
          { id: "transfer-form", content: getTabContent("transfer-form") },
        ]}
      />

      {/* Form Builder Modal */}
      {isFormBuilderOpen && selectedOperationType && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold">Configure Form: {selectedOperationType}</h2>
              <Button variant="outline" onClick={() => setIsFormBuilderOpen(false)}>
                Close
              </Button>
            </div>
            <AssetOperationFormBuilder
              assetTypeId={assetTypeId}
              assetTypeName={assetType?.name || "Asset Type"}
              availableFields={assetType?.customFields || []}
              onSave={handleFormSave}
              onPreview={handleFormPreview}
              initialForms={{}}
            />
          </div>
        </div>
      )}
    </div>
  );
}
