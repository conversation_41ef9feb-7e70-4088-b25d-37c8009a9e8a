"use client";

import React, { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { FormBuilder, FormDefinition } from "@/components/form-builder";
import { FormVersionHistory } from "@/components/form-builder/form-version-history";
import { CustomField } from "@/lib/modules/asset-types/types";
import { AssetOperationType, ASSET_OPERATION_CONFIGS } from "@/lib/types/asset-type-forms";
import { getFormEditorHeaderConfig } from "@/lib/utils/asset-type-config-header-configs";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON>L<PERSON><PERSON>, 
  <PERSON>ert<PERSON>ircle, 
  CheckCircle,
  Clock,
  FileText
} from "lucide-react";

interface AssetTypeData {
  id: string;
  name: string;
  code: string;
  description: string;
  category: {
    id: string;
    name: string;
  };
  customFields: CustomField[];
}

interface AssetTypeForm {
  id: string;
  assetTypeId: string;
  formId: string;
  operationType: AssetOperationType;
  version: number;
  isDefault: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  form: {
    id: string;
    name: string;
    description?: string;
    sections: any[];
    settings: any;
  };
}

export default function FormEditorPage() {
  const params = useParams();
  const router = useRouter();
  const assetTypeId = params.id as string;
  const formId = params.formId as string;

  const [assetType, setAssetType] = useState<AssetTypeData | null>(null);
  const [assetTypeForm, setAssetTypeForm] = useState<AssetTypeForm | null>(null);
  const [currentForm, setCurrentForm] = useState<FormDefinition | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [activeTab, setActiveTab] = useState("editor");

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [assetTypeId, formId]);

  // Auto-save functionality
  useEffect(() => {
    if (!hasUnsavedChanges || !currentForm) return;

    const autoSaveTimer = setTimeout(() => {
      handleAutoSave();
    }, 30000); // Auto-save after 30 seconds of inactivity

    return () => clearTimeout(autoSaveTimer);
  }, [hasUnsavedChanges, currentForm]);

  // Handle beforeunload to warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load asset type data
      const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`);
      if (!assetTypeResponse.ok) {
        throw new Error("Failed to load asset type");
      }
      const assetTypeData = await assetTypeResponse.json();
      setAssetType(assetTypeData);

      // Load specific form data
      const formResponse = await fetch(`/api/asset-type-forms/${formId}`);
      if (!formResponse.ok) {
        throw new Error("Failed to load form");
      }
      const formData = await formResponse.json();
      setAssetTypeForm(formData);
      setCurrentForm(formData.form);

    } catch (error) {
      console.error("Error loading data:", error);
      setError("Failed to load form data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormChange = (updatedForm: FormDefinition) => {
    setCurrentForm(updatedForm);
    setHasUnsavedChanges(true);
    setSaveStatus('idle');
  };

  const handleAutoSave = async () => {
    if (!currentForm || !hasUnsavedChanges) return;

    try {
      setSaveStatus('saving');

      // Update the form definition
      const formResponse = await fetch(`/api/form-definitions/${currentForm.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: currentForm.name,
          description: currentForm.description,
          sections: currentForm.sections,
          settings: currentForm.settings,
          updatedBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (formResponse.ok) {
        const savedForm = await formResponse.json();
        setCurrentForm(savedForm);
        setHasUnsavedChanges(false);
        setLastSaved(new Date());
        setSaveStatus('saved');

        // Reset status after 2 seconds
        setTimeout(() => setSaveStatus('idle'), 2000);
      }
    } catch (error) {
      console.error("Auto-save failed:", error);
      // Don't show error toast for auto-save failures
    }
  };

  const handleSave = async (form: FormDefinition) => {
    if (!assetTypeForm) return;

    try {
      setIsSaving(true);
      setSaveStatus('saving');

      // Update the form definition
      const formResponse = await fetch(`/api/form-definitions/${form.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: form.name,
          description: form.description,
          sections: form.sections,
          settings: form.settings,
          updatedBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (!formResponse.ok) {
        throw new Error("Failed to save form definition");
      }

      const savedForm = await formResponse.json();
      setCurrentForm(savedForm);
      setHasUnsavedChanges(false);
      setLastSaved(new Date());
      setSaveStatus('saved');

      toast({
        title: "Success",
        description: "Form saved successfully.",
      });

      // Reset status after 2 seconds
      setTimeout(() => setSaveStatus('idle'), 2000);

    } catch (error) {
      console.error("Error saving form:", error);
      setSaveStatus('error');
      toast({
        title: "Error",
        description: "Failed to save form.",
        variant: "destructive",
      });
      // Reset status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handlePreview = (form: FormDefinition) => {
    // Open preview in new tab or modal
    console.log("Preview form:", form);
    toast({
      title: "Preview",
      description: "Form preview functionality will be implemented.",
    });
  };

  const handleExport = (form: FormDefinition) => {
    // Export form as JSON
    const dataStr = JSON.stringify(form, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${form.name.replace(/\s+/g, '_')}_form.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const handleAddField = () => {
    // This would open a field selector dialog
    console.log("Add field clicked");
  };

  const handleRestoreVersion = async (version: any) => {
    const confirmed = window.confirm(
      `Are you sure you want to restore to version ${version.version}? This will overwrite your current changes.`
    );

    if (!confirmed) return;

    try {
      // Create a new form definition based on the version
      const restoredForm: FormDefinition = {
        id: currentForm?.id || version.id,
        name: version.name,
        description: version.description,
        sections: version.sections,
        settings: version.settings,
      };

      setCurrentForm(restoredForm);
      setHasUnsavedChanges(true);
      setSaveStatus('idle');

      toast({
        title: "Version Restored",
        description: `Form has been restored to version ${version.version}. Don't forget to save your changes.`,
      });
    } catch (error) {
      console.error("Error restoring version:", error);
      toast({
        title: "Error",
        description: "Failed to restore version.",
        variant: "destructive",
      });
    }
  };

  const handlePreviewVersion = (version: any) => {
    // This could open a preview modal or navigate to a preview page
    console.log("Preview version:", version);
    toast({
      title: "Version Preview",
      description: `Previewing version ${version.version}`,
    });
  };

  const handleBack = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm("You have unsaved changes. Are you sure you want to leave?");
      if (!confirmed) return;
    }
    router.push(`/admin/asset-types/${assetTypeId}/forms`);
  };

  // Get operation configuration
  const operationConfig = assetTypeForm ? ASSET_OPERATION_CONFIGS[assetTypeForm.operationType] : null;

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() =>
    getFormEditorHeaderConfig(
      assetTypeId,
      assetType?.name || "Asset Type",
      formId,
      currentForm?.name || "Form Editor",
      operationConfig?.displayName || 'operation',
      () => currentForm && handleSave(currentForm),
      handleBack,
      saveStatus,
      hasUnsavedChanges
    ),
    [assetTypeId, assetType?.name, currentForm?.name, operationConfig?.displayName, saveStatus, hasUnsavedChanges, formId]
  );

  // Set up the header for this page
  useAdminHeader(headerConfig);

  if (isLoading) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading form editor...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!currentForm || !assetType) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>Form not found.</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex-1 h-full flex flex-col">
      {/* Status Bar */}
      <div className="px-4 py-2 bg-muted/30 border-b text-sm text-muted-foreground flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span>Editing: {operationConfig?.displayName || 'Form'}</span>
          </div>
          {lastSaved && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          {hasUnsavedChanges && (
            <div className="flex items-center gap-1 text-orange-600">
              <AlertCircle className="h-3 w-3" />
              <span>Unsaved changes</span>
            </div>
          )}
          {saveStatus === 'saving' && (
            <div className="flex items-center gap-1 text-blue-600">
              <Clock className="h-3 w-3 animate-spin" />
              <span>Saving...</span>
            </div>
          )}
          {saveStatus === 'saved' && (
            <div className="flex items-center gap-1 text-green-600">
              <CheckCircle className="h-3 w-3" />
              <span>Saved</span>
            </div>
          )}
        </div>
      </div>

      {/* Main Content with Tabs */}
      <div className="flex-1">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="px-4 py-2 border-b">
            <TabsList>
              <TabsTrigger value="editor">Form Editor</TabsTrigger>
              <TabsTrigger value="history">Version History</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="editor" className="flex-1 m-0">
            <FormBuilder
              initialForm={currentForm}
              availableFields={assetType.customFields}
              onSave={handleFormChange}
              onPreview={handlePreview}
              onExport={handleExport}
              onAddField={handleAddField}
            />
          </TabsContent>

          <TabsContent value="history" className="flex-1 m-0 p-4">
            <FormVersionHistory
              formId={currentForm?.id || formId}
              currentVersion={currentForm?.version || 1}
              onRestoreVersion={handleRestoreVersion}
              onPreviewVersion={handlePreviewVersion}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
