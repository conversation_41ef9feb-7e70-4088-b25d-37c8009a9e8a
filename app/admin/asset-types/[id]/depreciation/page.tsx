"use client";

import React, { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { useHeaderTabs } from "@/hooks/use-admin-tabs";
import { HeaderTabContent } from "@/components/ui/header-tab-content";
import { getDepreciationSettingsHeaderConfig } from "@/lib/utils/asset-type-config-header-configs";
import { getDepreciationSettingsHeaderTabs } from "@/lib/utils/asset-type-config-tabs-configs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { 
  TrendingDown,
  Calculator,
  DollarSign,
  Calendar,
  AlertCircle,
  CheckCircle,
  Info,
  Settings,
  BarChart3
} from "lucide-react";
import { DepreciationSettings } from "@/lib/modules/asset-types/types";
import { toast } from "@/components/ui/use-toast";

interface AssetTypeData {
  id: string;
  name: string;
  code: string;
  description: string;
  category: {
    id: string;
    name: string;
  };
  depreciationSettings?: DepreciationSettings;
}

const DEPRECIATION_METHODS = [
  { value: "straight-line", label: "Straight Line", description: "Equal depreciation each year" },
  { value: "double-declining", label: "Double Declining Balance", description: "Accelerated depreciation" },
  { value: "sum-of-years", label: "Sum of Years' Digits", description: "Accelerated depreciation method" },
  { value: "units-of-production", label: "Units of Production", description: "Based on usage/production" },
];

export default function DepreciationSettingsConfigPage() {
  const params = useParams();
  const router = useRouter();
  const assetTypeId = params.id as string;

  const [assetType, setAssetType] = useState<AssetTypeData | null>(null);
  const [depreciationSettings, setDepreciationSettings] = useState<DepreciationSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Form state
  const [settings, setSettings] = useState({
    method: "straight-line",
    usefulLifeYears: 5,
    salvageValuePercent: 10,
    isActive: true,
    autoCalculate: true,
    customRates: {} as Record<string, number>,
    notes: "",
  });

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [assetTypeId]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load asset type data with depreciation settings
      const response = await fetch(`/api/asset-types/${assetTypeId}`);
      if (!response.ok) {
        throw new Error("Failed to load asset type");
      }
      const data = await response.json();
      setAssetType(data);
      
      if (data.depreciationSettings) {
        setDepreciationSettings(data.depreciationSettings);
        setSettings({
          method: data.depreciationSettings.method,
          usefulLifeYears: data.depreciationSettings.usefulLifeYears,
          salvageValuePercent: data.depreciationSettings.salvageValuePercent,
          isActive: data.depreciationSettings.isActive,
          autoCalculate: data.depreciationSettings.autoCalculate,
          customRates: data.depreciationSettings.customRates || {},
          notes: data.depreciationSettings.notes || "",
        });
      }

    } catch (error) {
      console.error("Error loading data:", error);
      setError("Failed to load configuration data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      
      const response = await fetch(`/api/depreciation/asset-types`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          assetTypeId,
          ...settings,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to save depreciation settings");
      }

      const result = await response.json();
      setDepreciationSettings(result.depreciationSettings);
      setHasChanges(false);

      toast({
        title: "Success",
        description: "Depreciation settings saved successfully.",
      });
    } catch (error) {
      console.error("Error saving:", error);
      toast({
        title: "Error",
        description: "Failed to save depreciation settings.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/admin/asset-types/${assetTypeId}`);
  };

  const handleSettingsChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const calculateDepreciationExample = () => {
    const purchasePrice = 10000; // Example purchase price
    const salvageValue = (purchasePrice * settings.salvageValuePercent) / 100;
    const depreciableAmount = purchasePrice - salvageValue;

    switch (settings.method) {
      case "straight-line":
        return depreciableAmount / settings.usefulLifeYears;
      case "double-declining":
        return (purchasePrice * 2) / settings.usefulLifeYears;
      default:
        return depreciableAmount / settings.usefulLifeYears;
    }
  };

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => 
    getDepreciationSettingsHeaderConfig(
      assetTypeId,
      assetType?.name || "Asset Type",
      handleSave,
      handleBack
    ), 
    [assetTypeId, assetType?.name]
  );

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getDepreciationSettingsHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Depreciation Settings Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Method</p>
                        <p className="text-lg font-bold">{DEPRECIATION_METHODS.find(m => m.value === settings.method)?.label}</p>
                      </div>
                      <TrendingDown className="h-8 w-8 text-blue-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Useful Life</p>
                        <p className="text-lg font-bold">{settings.usefulLifeYears} years</p>
                      </div>
                      <Calendar className="h-8 w-8 text-green-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Salvage Value</p>
                        <p className="text-lg font-bold">{settings.salvageValuePercent}%</p>
                      </div>
                      <DollarSign className="h-8 w-8 text-orange-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Status</p>
                        <p className="text-lg font-bold">{settings.isActive ? "Active" : "Inactive"}</p>
                      </div>
                      {settings.isActive ? (
                        <CheckCircle className="h-8 w-8 text-green-500" />
                      ) : (
                        <AlertCircle className="h-8 w-8 text-red-500" />
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {hasChanges && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  You have unsaved changes. Click "Save Changes" to apply your modifications.
                </AlertDescription>
              </Alert>
            )}
          </div>
        );

      case "methods":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Depreciation Method</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="method">Depreciation Method</Label>
                  <Select 
                    value={settings.method} 
                    onValueChange={(value) => handleSettingsChange("method", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {DEPRECIATION_METHODS.map((method) => (
                        <SelectItem key={method.value} value={method.value}>
                          <div>
                            <div className="font-medium">{method.label}</div>
                            <div className="text-sm text-muted-foreground">{method.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="p-4 bg-muted rounded-lg">
                  <h4 className="font-medium mb-2">Method Description</h4>
                  <p className="text-sm text-muted-foreground">
                    {DEPRECIATION_METHODS.find(m => m.value === settings.method)?.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "parameters":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Depreciation Parameters</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="useful-life">Useful Life (Years)</Label>
                    <Input
                      id="useful-life"
                      type="number"
                      min="1"
                      max="50"
                      value={settings.usefulLifeYears}
                      onChange={(e) => handleSettingsChange("usefulLifeYears", parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="salvage-value">Salvage Value (%)</Label>
                    <Input
                      id="salvage-value"
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      value={settings.salvageValuePercent}
                      onChange={(e) => handleSettingsChange("salvageValuePercent", parseFloat(e.target.value))}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    value={settings.notes}
                    onChange={(e) => handleSettingsChange("notes", e.target.value)}
                    placeholder="Additional notes about depreciation settings..."
                  />
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="active"
                      checked={settings.isActive}
                      onCheckedChange={(checked) => handleSettingsChange("isActive", checked)}
                    />
                    <Label htmlFor="active">Active</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="auto-calculate"
                      checked={settings.autoCalculate}
                      onCheckedChange={(checked) => handleSettingsChange("autoCalculate", checked)}
                    />
                    <Label htmlFor="auto-calculate">Auto Calculate</Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "calculation":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Depreciation Calculation Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">Example Calculation</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Based on a $10,000 asset purchase price:
                    </p>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Purchase Price:</span> $10,000
                      </div>
                      <div>
                        <span className="font-medium">Salvage Value:</span> ${(10000 * settings.salvageValuePercent / 100).toFixed(2)}
                      </div>
                      <div>
                        <span className="font-medium">Depreciable Amount:</span> ${(10000 - (10000 * settings.salvageValuePercent / 100)).toFixed(2)}
                      </div>
                      <div>
                        <span className="font-medium">Annual Depreciation:</span> ${calculateDepreciationExample().toFixed(2)}
                      </div>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Depreciation Schedule Preview</h4>
                    <div className="space-y-2">
                      {Array.from({ length: Math.min(settings.usefulLifeYears, 5) }, (_, i) => {
                        const year = i + 1;
                        const annualDepreciation = calculateDepreciationExample();
                        const accumulatedDepreciation = annualDepreciation * year;
                        const bookValue = 10000 - accumulatedDepreciation;
                        
                        return (
                          <div key={year} className="flex justify-between text-sm">
                            <span>Year {year}:</span>
                            <span>${annualDepreciation.toFixed(2)} (Book Value: ${Math.max(bookValue, 10000 * settings.salvageValuePercent / 100).toFixed(2)})</span>
                          </div>
                        );
                      })}
                      {settings.usefulLifeYears > 5 && (
                        <div className="text-sm text-muted-foreground">
                          ... and {settings.usefulLifeYears - 5} more years
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return (
          <div className="space-y-6">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This configuration section is under development.
              </AlertDescription>
            </Alert>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading depreciation settings...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "methods", content: getTabContent("methods") },
          { id: "parameters", content: getTabContent("parameters") },
          { id: "calculation", content: getTabContent("calculation") },
          { id: "history", content: getTabContent("history") },
        ]}
      />
    </div>
  );
}
