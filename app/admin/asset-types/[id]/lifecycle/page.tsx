"use client";

import React, { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAdminHeader } from "@/hooks/use-admin-header";
import { useHeaderTabs } from "@/hooks/use-admin-tabs";
import { HeaderTabContent } from "@/components/ui/header-tab-content";
import { getLifecycleStagesHeaderConfig } from "@/lib/utils/asset-type-config-header-configs";
import { getLifecycleStagesHeaderTabs } from "@/lib/utils/asset-type-config-tabs-configs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  GitBranch,
  Workflow,
  Settings,
  Plus,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  Info,
  ArrowRight,
  Circle,
  Play,
  Square
} from "lucide-react";
import { LifecycleStage } from "@/lib/modules/asset-types/types";
import { toast } from "@/components/ui/use-toast";

interface AssetTypeData {
  id: string;
  name: string;
  code: string;
  description: string;
  category: {
    id: string;
    name: string;
  };
  lifecycleStages: LifecycleStage[];
}

export default function LifecycleStagesConfigPage() {
  const params = useParams();
  const router = useRouter();
  const assetTypeId = params.id as string;

  const [assetType, setAssetType] = useState<AssetTypeData | null>(null);
  const [lifecycleStages, setLifecycleStages] = useState<LifecycleStage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isAddStageDialogOpen, setIsAddStageDialogOpen] = useState(false);
  const [editingStage, setEditingStage] = useState<LifecycleStage | null>(null);

  // New stage form state
  const [newStage, setNewStage] = useState({
    name: "",
    code: "",
    description: "",
    order: 0,
    isInitial: false,
    isFinal: false,
    color: "#3B82F6",
    icon: "Circle",
    allowedTransitions: [] as string[],
    requiredFields: [] as string[],
  });

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [assetTypeId]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load asset type data with lifecycle stages
      const response = await fetch(`/api/asset-types/${assetTypeId}`);
      if (!response.ok) {
        throw new Error("Failed to load asset type");
      }
      const data = await response.json();
      setAssetType(data);
      setLifecycleStages(data.lifecycleStages || []);

    } catch (error) {
      console.error("Error loading data:", error);
      setError("Failed to load configuration data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      // Implement save logic here
      toast({
        title: "Success",
        description: "Lifecycle stages configuration saved successfully.",
      });
    } catch (error) {
      console.error("Error saving:", error);
      toast({
        title: "Error",
        description: "Failed to save lifecycle stages configuration.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    router.push(`/admin/asset-types/${assetTypeId}`);
  };

  const handleAddStage = () => {
    setNewStage({
      ...newStage,
      order: lifecycleStages.length + 1,
    });
    setIsAddStageDialogOpen(true);
  };

  const handleCreateStage = async () => {
    try {
      const response = await fetch(`/api/asset-types/${assetTypeId}/lifecycle-stages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newStage),
      });

      if (!response.ok) {
        throw new Error("Failed to create lifecycle stage");
      }

      const createdStage = await response.json();
      setLifecycleStages([...lifecycleStages, createdStage]);
      setIsAddStageDialogOpen(false);
      setNewStage({
        name: "",
        code: "",
        description: "",
        order: 0,
        isInitial: false,
        isFinal: false,
        color: "#3B82F6",
        icon: "Circle",
        allowedTransitions: [],
        requiredFields: [],
      });

      toast({
        title: "Success",
        description: "Lifecycle stage created successfully.",
      });

    } catch (error) {
      console.error("Error creating stage:", error);
      toast({
        title: "Error",
        description: "Failed to create lifecycle stage.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteStage = async (stageId: string) => {
    try {
      const response = await fetch(`/api/asset-types/${assetTypeId}/lifecycle-stages/${stageId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete lifecycle stage");
      }

      setLifecycleStages(lifecycleStages.filter(stage => stage.id !== stageId));
      toast({
        title: "Success",
        description: "Lifecycle stage deleted successfully.",
      });

    } catch (error) {
      console.error("Error deleting stage:", error);
      toast({
        title: "Error",
        description: "Failed to delete lifecycle stage.",
        variant: "destructive",
      });
    }
  };

  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => 
    getLifecycleStagesHeaderConfig(
      assetTypeId,
      assetType?.name || "Asset Type",
      handleSave,
      handleBack,
      handleAddStage
    ), 
    [assetTypeId, assetType?.name]
  );

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getLifecycleStagesHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  const getStageIcon = (iconName: string) => {
    switch (iconName) {
      case "Play": return Play;
      case "Square": return Square;
      case "Circle": return Circle;
      default: return Circle;
    }
  };

  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Lifecycle Stages Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Total Stages</p>
                        <p className="text-2xl font-bold">{lifecycleStages.length}</p>
                      </div>
                      <GitBranch className="h-8 w-8 text-blue-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Initial Stages</p>
                        <p className="text-2xl font-bold">{lifecycleStages.filter(s => s.isInitial).length}</p>
                      </div>
                      <Play className="h-8 w-8 text-green-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Final Stages</p>
                        <p className="text-2xl font-bold">{lifecycleStages.filter(s => s.isFinal).length}</p>
                      </div>
                      <Square className="h-8 w-8 text-red-500" />
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Active Stages</p>
                        <p className="text-2xl font-bold">{lifecycleStages.filter(s => s.isActive).length}</p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-orange-500" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Lifecycle Flow</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {lifecycleStages
                    .sort((a, b) => a.order - b.order)
                    .map((stage, index) => {
                      const IconComponent = getStageIcon(stage.icon);
                      return (
                        <div key={stage.id} className="flex items-center gap-4">
                          <div 
                            className="flex items-center gap-3 p-4 border rounded-lg flex-1"
                            style={{ borderColor: stage.color }}
                          >
                            <div 
                              className="p-2 rounded-full"
                              style={{ backgroundColor: stage.color + "20", color: stage.color }}
                            >
                              <IconComponent className="h-5 w-5" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <p className="font-medium">{stage.name}</p>
                                {stage.isInitial && <Badge variant="secondary">Initial</Badge>}
                                {stage.isFinal && <Badge variant="destructive">Final</Badge>}
                              </div>
                              <p className="text-sm text-muted-foreground">{stage.description}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button variant="outline" size="sm" onClick={() => setEditingStage(stage)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={() => handleDeleteStage(stage.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          {index < lifecycleStages.length - 1 && (
                            <ArrowRight className="h-5 w-5 text-muted-foreground" />
                          )}
                        </div>
                      );
                    })}
                  {lifecycleStages.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No lifecycle stages configured yet. Start by adding stages for this asset type.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return (
          <div className="space-y-6">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This configuration section is under development.
              </AlertDescription>
            </Alert>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading lifecycle stages configuration...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "stages", content: getTabContent("stages") },
          { id: "transitions", content: getTabContent("transitions") },
          { id: "automation", content: getTabContent("automation") },
          { id: "history", content: getTabContent("history") },
        ]}
      />

      {/* Add Stage Dialog */}
      <Dialog open={isAddStageDialogOpen} onOpenChange={setIsAddStageDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add Lifecycle Stage</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="stage-name">Stage Name</Label>
                <Input
                  id="stage-name"
                  value={newStage.name}
                  onChange={(e) => setNewStage({ ...newStage, name: e.target.value })}
                  placeholder="Stage Name"
                />
              </div>
              <div>
                <Label htmlFor="stage-code">Stage Code</Label>
                <Input
                  id="stage-code"
                  value={newStage.code}
                  onChange={(e) => setNewStage({ ...newStage, code: e.target.value })}
                  placeholder="stage_code"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="stage-description">Description</Label>
              <Textarea
                id="stage-description"
                value={newStage.description}
                onChange={(e) => setNewStage({ ...newStage, description: e.target.value })}
                placeholder="Stage description..."
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="stage-color">Color</Label>
                <Input
                  id="stage-color"
                  type="color"
                  value={newStage.color}
                  onChange={(e) => setNewStage({ ...newStage, color: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="stage-order">Order</Label>
                <Input
                  id="stage-order"
                  type="number"
                  value={newStage.order}
                  onChange={(e) => setNewStage({ ...newStage, order: parseInt(e.target.value) })}
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="initial"
                  checked={newStage.isInitial}
                  onCheckedChange={(checked) => setNewStage({ ...newStage, isInitial: checked })}
                />
                <Label htmlFor="initial">Initial Stage</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="final"
                  checked={newStage.isFinal}
                  onCheckedChange={(checked) => setNewStage({ ...newStage, isFinal: checked })}
                />
                <Label htmlFor="final">Final Stage</Label>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsAddStageDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateStage}>
                Create Stage
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
