"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Download, FileText, TrendingDown, DollarSign, Wrench } from "lucide-react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { HeaderTabContent } from "@/components/ui/header-tab-content"
import { getReportsHeaderConfig } from "@/lib/utils/admin-header-configs"
import { getReportsHeaderTabs } from "@/lib/utils/admin-tabs-configs"

// Mock report data
const reports = [
  {
    id: "RPT-001",
    name: "Asset Depreciation Report",
    type: "Financial",
    generatedDate: "2024-01-10",
    status: "Ready",
    description: "Monthly depreciation analysis for all assets",
  },
  {
    id: "RPT-002",
    name: "Maintenance Cost Analysis",
    type: "Maintenance",
    generatedDate: "2024-01-08",
    status: "Ready",
    description: "Quarterly maintenance cost breakdown by category",
  },
  {
    id: "RPT-003",
    name: "Asset Utilization Report",
    type: "Operational",
    generatedDate: "2024-01-05",
    status: "Ready",
    description: "Asset usage and efficiency metrics",
  },
  {
    id: "RPT-004",
    name: "Compliance Audit Report",
    type: "Compliance",
    generatedDate: "2024-01-03",
    status: "Processing",
    description: "Annual compliance and regulatory audit",
  },
]

const depreciationData = [
  { category: "IT Equipment", originalValue: 125000, currentValue: 87500, depreciation: 37500, rate: "30%" },
  { category: "Machinery", originalValue: 450000, currentValue: 360000, depreciation: 90000, rate: "20%" },
  { category: "Furniture", originalValue: 75000, currentValue: 67500, depreciation: 7500, rate: "10%" },
  { category: "Vehicles", originalValue: 200000, currentValue: 140000, depreciation: 60000, rate: "30%" },
  { category: "Security", originalValue: 50000, currentValue: 42500, depreciation: 7500, rate: "15%" },
]

const maintenanceCosts = [
  { month: "January", preventive: 5200, repair: 3800, inspection: 1200, total: 10200 },
  { month: "February", preventive: 4800, repair: 4200, inspection: 1000, total: 10000 },
  { month: "March", preventive: 5500, repair: 2900, inspection: 1300, total: 9700 },
  { month: "April", preventive: 4900, repair: 3600, inspection: 1100, total: 9600 },
  { month: "May", preventive: 5300, repair: 4100, inspection: 1400, total: 10800 },
  { month: "June", preventive: 5100, repair: 3200, inspection: 1200, total: 9500 },
]

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("monthly")
  const [selectedCategory, setSelectedCategory] = useState("all")

  
  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => getReportsHeaderConfig(), []);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getReportsHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Ready":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Ready</Badge>
      case "Processing":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Processing</Badge>
      case "Failed":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Failed</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  
  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-4">
            <div>Content for overview tab - TODO: Move existing content here</div>
          </div>
        );
      case "financial":
        return (
          <div className="space-y-4">
            <div>Content for financial tab - TODO: Move existing content here</div>
          </div>
        );
      case "operational":
        return (
          <div className="space-y-4">
            <div>Content for operational tab - TODO: Move existing content here</div>
          </div>
        );
      case "compliance":
        return (
          <div className="space-y-4">
            <div>Content for compliance tab - TODO: Move existing content here</div>
          </div>
        );
      case "custom":
        return (
          <div className="space-y-4">
            <div>Content for custom tab - TODO: Move existing content here</div>
          </div>
        );
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

            {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "financial", content: getTabContent("financial") },
          { id: "operational", content: getTabContent("operational") },
          { id: "compliance", content: getTabContent("compliance") },
          { id: "custom", content: getTabContent("custom") },
        ]}
      />
    </div>
  )
}
