"use client"

import { useState, useEffect, use<PERSON>em<PERSON> } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { HeaderTabContent } from "@/components/ui/header-tab-content"
import { getInventoryHeaderConfig } from "@/lib/utils/admin-header-configs"
import { getInventoryHeaderTabs } from "@/lib/utils/admin-tabs-configs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Plus,
  Package,
  DollarSign,
  TrendingDown,
  AlertTriangle,
  Truck,
  BarChart3,
  Search,
  Filter,
  Eye,
  Edit,
  ShoppingCart,
  Users,
  CheckCircle,
  Clock,
  XCircle,
} from "lucide-react"
import { InventoryService } from "@/lib/modules/inventory/services"
import type { InventoryItem, InventoryMetrics, StockAlert } from "@/lib/modules/inventory/types"

export default function InventoryPage() {
  
  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => getInventoryHeaderConfig(), []);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getInventoryHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  const [items, setItems] = useState<InventoryItem[]>([])
  const [metrics, setMetrics] = useState<InventoryMetrics | null>(null)
  const [alerts, setAlerts] = useState<StockAlert[]>([])
  const [isAddItemOpen, setIsAddItemOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const inventoryService = InventoryService.getInstance()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    const allItems = inventoryService.getItems()
    const inventoryMetrics = await inventoryService.getInventoryMetrics()
    const stockAlerts = inventoryService.getAlerts(false) // Get unacknowledged alerts
    setItems(allItems)
    setMetrics(inventoryMetrics)
    setAlerts(stockAlerts)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        
  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-4">
            <div>Content for overview tab - TODO: Move existing content here</div>
          </div>
        );
      case "stock":
        return (
          <div className="space-y-4">
            <div>Content for stock tab - TODO: Move existing content here</div>
          </div>
        );
      case "movements":
        return (
          <div className="space-y-4">
            <div>Content for movements tab - TODO: Move existing content here</div>
          </div>
        );
      case "suppliers":
        return (
          <div className="space-y-4">
            <div>Content for suppliers tab - TODO: Move existing content here</div>
          </div>
        );
      case "analytics":
        return (
          <div className="space-y-4">
            <div>Content for analytics tab - TODO: Move existing content here</div>
          </div>
        );
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };

  return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        )
      case "Inactive":
        return (
          <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">
            <Clock className="w-3 h-3 mr-1" />
            Inactive
          </Badge>
        )
      case "Discontinued":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            <XCircle className="w-3 h-3 mr-1" />
            Discontinued
          </Badge>
        )
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getStockLevelBadge = (item: InventoryItem) => {
    if (item.currentStock === 0) {
      return <Badge variant="destructive">Out of Stock</Badge>
    } else if (item.currentStock <= item.reorderPoint) {
      return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">Low Stock</Badge>
    } else if (item.currentStock > item.maxStockLevel) {
      return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Overstock</Badge>
    } else {
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">In Stock</Badge>
    }
  }

  const getAlertSeverityBadge = (severity: string) => {
    switch (severity) {
      case "Critical":
        return <Badge variant="destructive">Critical</Badge>
      case "High":
        return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">High</Badge>
      case "Medium":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Medium</Badge>
      case "Low":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Low</Badge>
      default:
        return <Badge variant="secondary">{severity}</Badge>
    }
  }

  const filteredItems = items.filter((item) => {
    const matchesSearch =
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "All" || item.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const categories = ["All", ...Array.from(new Set(items.map((item) => item.category)))]

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Inventory Management</h2>
          <p className="text-muted-foreground">Comprehensive stock management and procurement system</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Truck className="mr-2 h-4 w-4" />
            Purchase Orders
          </Button>
          <Dialog open={isAddItemOpen} onOpenChange={setIsAddItemOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Add New Inventory Item</DialogTitle>
                <DialogDescription>Create a new inventory item with detailed specifications.</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="itemName">Item Name *</Label>
                    <Input id="itemName" placeholder="Enter item name" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sku">SKU *</Label>
                    <Input id="sku" placeholder="Enter SKU" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category *</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="electronics">Electronics</SelectItem>
                        <SelectItem value="furniture">Furniture</SelectItem>
                        <SelectItem value="supplies">Office Supplies</SelectItem>
                        <SelectItem value="equipment">Equipment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unitOfMeasure">Unit of Measure</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="each">Each</SelectItem>
                        <SelectItem value="box">Box</SelectItem>
                        <SelectItem value="kg">Kilogram</SelectItem>
                        <SelectItem value="liter">Liter</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="costPrice">Cost Price *</Label>
                    <Input id="costPrice" type="number" placeholder="0.00" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sellingPrice">Selling Price</Label>
                    <Input id="sellingPrice" type="number" placeholder="0.00" />
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentStock">Current Stock</Label>
                    <Input id="currentStock" type="number" placeholder="0" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="reorderPoint">Reorder Point</Label>
                    <Input id="reorderPoint" type="number" placeholder="0" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxStock">Max Stock Level</Label>
                    <Input id="maxStock" type="number" placeholder="0" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea id="description" placeholder="Enter item description..." />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddItemOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" onClick={() => setIsAddItemOpen(false)}>
                  Create Item
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

            {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "stock", content: getTabContent("stock") },
          { id: "movements", content: getTabContent("movements") },
          { id: "suppliers", content: getTabContent("suppliers") },
          { id: "analytics", content: getTabContent("analytics") },
        ]}
      />
    </div>
  )
}
