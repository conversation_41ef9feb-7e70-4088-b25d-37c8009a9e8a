"use client"

import { useState, useMemo } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { HeaderTabContent } from "@/components/ui/header-tab-content"
import { getAssetLeasingHeaderConfig } from "@/lib/utils/admin-header-configs"
import { getAssetLeasingHeaderTabs } from "@/lib/utils/admin-tabs-configs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { PlusCircle } from "lucide-react"
import LeaseList from "@/components/asset-leasing/lease-list"
import LeaseCalendar from "@/components/asset-leasing/lease-calendar"
import PaymentsList from "@/components/asset-leasing/payments-list"
import LeaseMetricsDisplay from "@/components/asset-leasing/lease-metrics"
import LeaseDialog from "@/components/asset-leasing/lease-dialog"

export default function AssetLeasingPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  
  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => getAssetLeasingHeaderConfig(), []);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getAssetLeasingHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "overview");

  
  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "overview":
        return (
          <div className="space-y-4">
            <div>Content for overview tab - TODO: Move existing content here</div>
          </div>
        );
      case "contracts":
        return (
          <div className="space-y-4">
            <div>Content for contracts tab - TODO: Move existing content here</div>
          </div>
        );
      case "payments":
        return (
          <div className="space-y-4">
            <div>Content for payments tab - TODO: Move existing content here</div>
          </div>
        );
      case "analytics":
        return (
          <div className="space-y-4">
            <div>Content for analytics tab - TODO: Move existing content here</div>
          </div>
        );
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };

  return (
    <div className="container mx-auto py-6">

      <LeaseMetricsDisplay />

            {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "overview", content: getTabContent("overview") },
          { id: "contracts", content: getTabContent("contracts") },
          { id: "payments", content: getTabContent("payments") },
          { id: "analytics", content: getTabContent("analytics") },
        ]}
      />

      <LeaseDialog 
        open={isCreateDialogOpen} 
        onOpenChange={setIsCreateDialogOpen} 
      />
    </div>
  )
}