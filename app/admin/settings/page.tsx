"use client"

import { useState, useMemo } from "react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { useHeaderTabs } from "@/hooks/use-admin-tabs"
import { HeaderTabContent } from "@/components/ui/header-tab-content"
import { getSettingsHeaderConfig } from "@/lib/utils/admin-header-configs"
import { getSettingsHeaderTabs } from "@/lib/utils/admin-tabs-configs"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Trash2 } from "lucide-react"

// Mock settings data
const systemSettings = {
  companyName: "WizeAssets Corp",
  timezone: "UTC-5",
  currency: "USD",
  dateFormat: "MM/DD/YYYY",
  autoBackup: true,
  maintenanceReminders: true,
  emailNotifications: true,
  smsNotifications: false,
}

const userRoles = [
  { id: 1, name: "Administrator", permissions: ["All"], users: 2, description: "Full system access" },
  {
    id: 2,
    name: "Asset Manager",
    permissions: ["Assets", "Maintenance", "Reports"],
    users: 5,
    description: "Manage assets and maintenance",
  },
  { id: 3, name: "Technician", permissions: ["Maintenance"], users: 8, description: "Maintenance tasks only" },
  { id: 4, name: "Viewer", permissions: ["View Only"], users: 12, description: "Read-only access" },
]

const integrations = [
  { name: "Microsoft Active Directory", status: "Connected", type: "Authentication" },
  { name: "SAP ERP", status: "Disconnected", type: "ERP Integration" },
  { name: "Slack", status: "Connected", type: "Notifications" },
  { name: "QuickBooks", status: "Connected", type: "Financial" },
]

export default function SettingsPage() {
  const [settings, setSettings] = useState(systemSettings)

  
  // Memoize header config to prevent infinite re-renders
  const headerConfig = useMemo(() => getSettingsHeaderConfig(), []);

  // Set up the header for this page
  useAdminHeader(headerConfig);

  // Memoize tabs to prevent infinite re-renders
  const headerTabs = useMemo(() => getSettingsHeaderTabs(), []);

  // Set up header-integrated tabs
  useHeaderTabs(headerTabs, "general");

  const handleSettingChange = (key: string, value: any) => {
    setSettings((prev) => ({ ...prev, [key]: value }))
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Connected":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Connected</Badge>
      case "Disconnected":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Disconnected</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  
  // Function to get tab content based on tab ID
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case "general":
        return (
          <div className="space-y-4">
            <div>Content for general tab - TODO: Move existing content here</div>
          </div>
        );
      case "users":
        return (
          <div className="space-y-4">
            <div>Content for users tab - TODO: Move existing content here</div>
          </div>
        );
      case "customFields":
        return (
          <div className="space-y-4">
            <div>Content for customFields tab - TODO: Move existing content here</div>
          </div>
        );
      case "formBuilder":
        return (
          <div className="space-y-4">
            <div>Content for formBuilder tab - TODO: Move existing content here</div>
          </div>
        );
      case "notifications":
        return (
          <div className="space-y-4">
            <div>Content for notifications tab - TODO: Move existing content here</div>
          </div>
        );
      case "integrations":
        return (
          <div className="space-y-4">
            <div>Content for integrations tab - TODO: Move existing content here</div>
          </div>
        );
      case "security":
        return (
          <div className="space-y-4">
            <div>Content for security tab - TODO: Move existing content here</div>
          </div>
        );
      default:
        return <div>Content for {tabId} tab</div>;
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">

            {/* Header-Integrated Tabs Content */}
      <HeaderTabContent
        tabContents={[
          { id: "general", content: getTabContent("general") },
          { id: "users", content: getTabContent("users") },
          { id: "customFields", content: getTabContent("customFields") },
          { id: "formBuilder", content: getTabContent("formBuilder") },
          { id: "notifications", content: getTabContent("notifications") },
          { id: "integrations", content: getTabContent("integrations") },
          { id: "security", content: getTabContent("security") },
        ]}
      />
    </div>
  )
}
