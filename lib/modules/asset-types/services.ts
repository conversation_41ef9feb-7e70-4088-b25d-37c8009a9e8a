import type {
  AssetType,
  AssetCategory,
  CustomField,
  LifecycleStage,
  MaintenanceSchedule,
  DepreciationSettings,
  AssetTypeTemplate,
  AssetTypeMetrics,
  AssetTypeValidationResult,
} from "./types"
import { useAssetTypeStore } from "@/store/asset-type-store"

export class AssetTypeService {
  private static instance: AssetTypeService

  static getInstance(): AssetTypeService {
    if (!AssetTypeService.instance) {
      AssetTypeService.instance = new AssetTypeService()
    }
    return AssetTypeService.instance
  }

  constructor() {
    // Client-side service - uses API calls instead of direct database access
  }

  // Initialize service with data from API
  async initialize(): Promise<void> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      // Load categories first
      const categoriesResponse = await fetch("/api/asset-types/categories")
      if (!categoriesResponse.ok) throw new Error("Failed to fetch categories")
      const categories = await categoriesResponse.json()
      store.setCategories(categories)

      // Load asset types
      const assetTypesResponse = await fetch("/api/asset-types")
      if (!assetTypesResponse.ok) throw new Error("Failed to fetch asset types")
      const assetTypes = await assetTypesResponse.json()
      store.setAssetTypes(assetTypes)

      // Load templates
      const templatesResponse = await fetch("/api/asset-types/templates")
      if (!templatesResponse.ok) throw new Error("Failed to fetch templates")
      const templates = await templatesResponse.json()
      store.setTemplates(templates)

      // Load metrics
      const metricsResponse = await fetch("/api/asset-types/metrics")
      if (!metricsResponse.ok) throw new Error("Failed to fetch metrics")
      const metrics = await metricsResponse.json()
      store.setMetrics(metrics)

    } catch (error) {
      const store = useAssetTypeStore.getState()
      store.setError(error instanceof Error ? error.message : "Failed to initialize asset type service")
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Asset Type CRUD Operations
  async createAssetType(assetType: Omit<AssetType, "id" | "createdAt" | "updatedAt" | "version">): Promise<AssetType> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch("/api/asset-types", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(assetType),
      })
      
      if (!response.ok) throw new Error("Failed to create asset type")
      const newAssetType = await response.json()
      
      // Update store
      store.addAssetType(newAssetType)
      
      // Refresh metrics
      const metricsResponse = await fetch("/api/asset-types/metrics")
      if (metricsResponse.ok) {
        const metrics = await metricsResponse.json()
        store.setMetrics(metrics)
      }

      return newAssetType
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to create asset type"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async updateAssetType(id: string, updates: Partial<AssetType>): Promise<AssetType | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch(`/api/asset-types/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updates),
      })
      
      if (!response.ok) throw new Error("Failed to update asset type")
      const updatedAssetType = await response.json()
      
      if (updatedAssetType) {
        // Update store
        store.updateAssetType(id, updatedAssetType)
        
        // Refresh metrics
        const metricsResponse = await fetch("/api/asset-types/metrics")
        if (metricsResponse.ok) {
          const metrics = await metricsResponse.json()
          store.setMetrics(metrics)
        }
      }

      return updatedAssetType
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to update asset type"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async deleteAssetType(id: string): Promise<boolean> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch(`/api/asset-types/${id}`, {
        method: "DELETE",
      })
      
      if (!response.ok) throw new Error("Failed to delete asset type")
      const success = response.ok
      
      if (success) {
        // Update store
        store.removeAssetType(id)
        
        // Refresh metrics
        const metricsResponse = await fetch("/api/asset-types/metrics")
        if (metricsResponse.ok) {
          const metrics = await metricsResponse.json()
          store.setMetrics(metrics)
        }
      }

      return success
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to delete asset type"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async getAssetType(id: string): Promise<AssetType | null> {
    try {
      const store = useAssetTypeStore.getState()
      
      // First check store
      const cachedAssetType = store.assetTypes.find((at) => at.id === id)
      if (cachedAssetType) {
        return cachedAssetType
      }

      // If not in store, fetch from API
      store.setLoading(true)
      const response = await fetch(`/api/asset-types/${id}`)
      
      if (!response.ok) {
        if (response.status === 404) return null
        throw new Error("Failed to fetch asset type")
      }
      
      const assetType = await response.json()
      
      if (assetType) {
        // Update store with fresh data
        store.addAssetType(assetType)
      }

      return assetType
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to get asset type"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async getAssetTypes(filters?: {
    category?: string
    isActive?: boolean
    search?: string
  }): Promise<AssetType[]> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      // Update filters in store
      if (filters) {
        store.setFilters(filters)
      }

      // Build query parameters
      const params = new URLSearchParams()
      if (filters?.category) params.append("category", filters.category)
      if (filters?.isActive !== undefined) params.append("isActive", filters.isActive.toString())
      if (filters?.search) params.append("search", filters.search)

      const response = await fetch(`/api/asset-types?${params.toString()}`)
      if (!response.ok) throw new Error("Failed to fetch asset types")
      
      const assetTypes = await response.json()
      
      // Update store
      store.setAssetTypes(assetTypes)

      return assetTypes
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to get asset types"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Custom Fields Management
  async addCustomField(assetTypeId: string, field: Omit<CustomField, "id">): Promise<CustomField | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch(`/api/asset-types/${assetTypeId}/custom-fields`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(field),
      })

      if (!response.ok) throw new Error("Failed to add custom field")
      const newField = await response.json()

      if (newField) {
        // Refresh the asset type in store
        const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`)
        if (assetTypeResponse.ok) {
          const updatedAssetType = await assetTypeResponse.json()
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return newField
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to add custom field"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async updateCustomField(
    assetTypeId: string,
    fieldId: string,
    updates: Partial<CustomField>,
  ): Promise<CustomField | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch(`/api/asset-types/${assetTypeId}/custom-fields/${fieldId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updates),
      })

      if (!response.ok) throw new Error("Failed to update custom field")
      const updatedField = await response.json()

      if (updatedField) {
        // Refresh the asset type in store
        const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`)
        if (assetTypeResponse.ok) {
          const updatedAssetType = await assetTypeResponse.json()
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return updatedField
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to update custom field"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async removeCustomField(assetTypeId: string, fieldId: string): Promise<boolean> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch(`/api/asset-types/${assetTypeId}/custom-fields/${fieldId}`, {
        method: "DELETE",
      })

      if (!response.ok) throw new Error("Failed to remove custom field")
      const success = response.ok

      if (success) {
        // Refresh the asset type in store
        const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`)
        if (assetTypeResponse.ok) {
          const updatedAssetType = await assetTypeResponse.json()
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return success
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to remove custom field"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Lifecycle Management
  async addLifecycleStage(assetTypeId: string, stage: Omit<LifecycleStage, "id">): Promise<LifecycleStage | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch(`/api/asset-types/${assetTypeId}/lifecycle-stages`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(stage),
      })

      if (!response.ok) throw new Error("Failed to add lifecycle stage")
      const newStage = await response.json()

      if (newStage) {
        // Refresh the asset type in store
        const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`)
        if (assetTypeResponse.ok) {
          const updatedAssetType = await assetTypeResponse.json()
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return newStage
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to add lifecycle stage"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async updateLifecycleStage(
    assetTypeId: string,
    stageId: string,
    updates: Partial<LifecycleStage>,
  ): Promise<LifecycleStage | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch(`/api/asset-types/${assetTypeId}/lifecycle-stages/${stageId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updates),
      })

      if (!response.ok) throw new Error("Failed to update lifecycle stage")
      const updatedStage = await response.json()

      if (updatedStage) {
        // Refresh the asset type in store
        const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`)
        if (assetTypeResponse.ok) {
          const updatedAssetType = await assetTypeResponse.json()
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return updatedStage
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to update lifecycle stage"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Maintenance Scheduling
  async addMaintenanceSchedule(
    assetTypeId: string,
    schedule: Omit<MaintenanceSchedule, "id">,
  ): Promise<MaintenanceSchedule | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch(`/api/asset-types/${assetTypeId}/maintenance-schedules`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(schedule),
      })

      if (!response.ok) throw new Error("Failed to add maintenance schedule")
      const newSchedule = await response.json()

      if (newSchedule) {
        // Refresh the asset type in store
        const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`)
        if (assetTypeResponse.ok) {
          const updatedAssetType = await assetTypeResponse.json()
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return newSchedule
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to add maintenance schedule"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async updateMaintenanceSchedule(
    assetTypeId: string,
    scheduleId: string,
    updates: Partial<MaintenanceSchedule>,
  ): Promise<MaintenanceSchedule | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch(`/api/asset-types/${assetTypeId}/maintenance-schedules/${scheduleId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updates),
      })

      if (!response.ok) throw new Error("Failed to update maintenance schedule")
      const updatedSchedule = await response.json()

      if (updatedSchedule) {
        // Refresh the asset type in store
        const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`)
        if (assetTypeResponse.ok) {
          const updatedAssetType = await assetTypeResponse.json()
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return updatedSchedule
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to update maintenance schedule"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Depreciation Management
  async updateDepreciationSettings(
    assetTypeId: string,
    settings: DepreciationSettings,
  ): Promise<DepreciationSettings | null> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch(`/api/asset-types/${assetTypeId}/depreciation-settings`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(settings),
      })

      if (!response.ok) throw new Error("Failed to update depreciation settings")
      const updatedSettings = await response.json()

      if (updatedSettings) {
        // Refresh the asset type in store
        const assetTypeResponse = await fetch(`/api/asset-types/${assetTypeId}`)
        if (assetTypeResponse.ok) {
          const updatedAssetType = await assetTypeResponse.json()
          store.updateAssetType(assetTypeId, updatedAssetType)
        }
      }

      return updatedSettings
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to update depreciation settings"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Depreciation calculation (client-side only)
  calculateDepreciation(
    assetValue: number,
    settings: DepreciationSettings,
    currentDate: string = new Date().toISOString(),
  ): {
    annualDepreciation: number
    accumulatedDepreciation: number
    bookValue: number
    remainingLife: number
  } {
    const startDate = new Date(settings.startDate)
    const current = new Date(currentDate)
    const yearsElapsed = (current.getTime() - startDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000)

    const salvageValue =
      settings.salvageValueType === "percentage" ? assetValue * (settings.salvageValue / 100) : settings.salvageValue

    const depreciableAmount = assetValue - salvageValue

    let annualDepreciation = 0
    let accumulatedDepreciation = 0

    switch (settings.method) {
      case "straight_line":
        annualDepreciation = depreciableAmount / settings.usefulLife
        accumulatedDepreciation = Math.min(annualDepreciation * yearsElapsed, depreciableAmount)
        break

      case "declining_balance":
        const rate = 1 / settings.usefulLife
        annualDepreciation = (assetValue - accumulatedDepreciation) * rate
        accumulatedDepreciation = assetValue * (1 - Math.pow(1 - rate, yearsElapsed)) - salvageValue
        break

      case "double_declining_balance":
        const doubleRate = 2 / settings.usefulLife
        annualDepreciation = (assetValue - accumulatedDepreciation) * doubleRate
        accumulatedDepreciation = Math.min(assetValue * (1 - Math.pow(1 - doubleRate, yearsElapsed)), depreciableAmount)
        break

      case "sum_of_years_digits":
        const sumOfYears = (settings.usefulLife * (settings.usefulLife + 1)) / 2
        const currentYear = Math.floor(yearsElapsed) + 1
        const remainingYears = Math.max(settings.usefulLife - currentYear + 1, 0)
        annualDepreciation = (depreciableAmount * remainingYears) / sumOfYears

        let totalAccumulated = 0
        for (let year = 1; year <= Math.min(currentYear, settings.usefulLife); year++) {
          const yearFraction = (settings.usefulLife - year + 1) / sumOfYears
          totalAccumulated += depreciableAmount * yearFraction
        }
        accumulatedDepreciation = totalAccumulated
        break

      default:
        annualDepreciation = depreciableAmount / settings.usefulLife
        accumulatedDepreciation = Math.min(annualDepreciation * yearsElapsed, depreciableAmount)
    }

    const bookValue = assetValue - accumulatedDepreciation
    const remainingLife = Math.max(settings.usefulLife - yearsElapsed, 0)

    return {
      annualDepreciation: Math.max(annualDepreciation, 0),
      accumulatedDepreciation: Math.max(accumulatedDepreciation, 0),
      bookValue: Math.max(bookValue, salvageValue),
      remainingLife: Math.max(remainingLife, 0),
    }
  }

  // Category Management
  async createCategory(category: Omit<AssetCategory, "id">): Promise<AssetCategory> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch("/api/asset-categories", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(category),
      })

      if (!response.ok) throw new Error("Failed to create category")
      const newCategory = await response.json()

      // Update store
      store.addCategory(newCategory)

      return newCategory
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to create category"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  async getCategories(parentId?: string): Promise<AssetCategory[]> {
    try {
      const store = useAssetTypeStore.getState()

      // First check store
      const cachedCategories = store.getCategoriesByParent(parentId)
      if (cachedCategories.length > 0) {
        return cachedCategories
      }

      // If not in store, fetch from API
      store.setLoading(true)
      const params = new URLSearchParams()
      if (parentId) params.append("parentId", parentId)

      const response = await fetch(`/api/asset-types/categories?${params.toString()}`)
      if (!response.ok) throw new Error("Failed to fetch categories")

      const categories = await response.json()

      // Update store
      store.setCategories(categories)

      return categories
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to get categories"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Analytics and Metrics
  async getMetrics(): Promise<AssetTypeMetrics> {
    try {
      const store = useAssetTypeStore.getState()
      store.setLoading(true)
      store.clearError()

      const response = await fetch("/api/asset-types/metrics")
      if (!response.ok) throw new Error("Failed to fetch metrics")

      const metrics = await response.json()

      // Update store
      store.setMetrics(metrics)

      return metrics
    } catch (error) {
      const store = useAssetTypeStore.getState()
      const errorMessage = error instanceof Error ? error.message : "Failed to get metrics"
      store.setError(errorMessage)
      throw error
    } finally {
      const store = useAssetTypeStore.getState()
      store.setLoading(false)
    }
  }

  // Refresh data from API
  async refreshData(): Promise<void> {
    await this.initialize()
  }

  // Clear cache and reload from API
  async clearCache(): Promise<void> {
    const store = useAssetTypeStore.getState()
    store.reset()
    await this.initialize()
  }
}
