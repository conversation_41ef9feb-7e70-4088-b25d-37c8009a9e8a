# Asset Type Service - Production Grade Implementation

This module provides a complete, production-ready asset type management system with database integration, state management using Zustand, and React hooks.

## Features

- **Database Integration**: Full CRUD operations with Prisma ORM
- **State Management**: Zustand store for efficient state management
- **React Hooks**: Custom hooks for easy component integration
- **Validation**: Comprehensive validation system
- **Error Handling**: Robust error handling throughout
- **Caching**: Intelligent caching with store synchronization
- **Modular Design**: Extensible and maintainable architecture

## Architecture

```
├── services.ts          # Main service class with business logic
├── db-service.ts        # Database operations with Prisma
├── hooks.ts            # React hooks for component integration
├── types.ts            # TypeScript type definitions
├── store/              # Zustand store (in /store directory)
└── index.ts            # Public API exports
```

## Usage

### Basic Usage with Hooks

```tsx
import { useAssetTypeService } from "@/lib/modules/asset-types"

function AssetTypeList() {
  const {
    assetTypes,
    loading,
    error,
    getAssetTypes,
    createAssetType,
    updateAssetType,
    deleteAssetType,
  } = useAssetTypeService()

  useEffect(() => {
    getAssetTypes()
  }, [getAssetTypes])

  const handleCreate = async (assetTypeData) => {
    try {
      await createAssetType(assetTypeData)
      // Asset type automatically added to store
    } catch (error) {
      console.error("Failed to create asset type:", error)
    }
  }

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>

  return (
    <div>
      {assetTypes.map((assetType) => (
        <div key={assetType.id}>{assetType.name}</div>
      ))}
    </div>
  )
}
```

### Custom Fields Management

```tsx
import { useCustomFields } from "@/lib/modules/asset-types"

function CustomFieldsManager({ assetTypeId }) {
  const { addCustomField, updateCustomField, removeCustomField } = useCustomFields(assetTypeId)

  const handleAddField = async () => {
    await addCustomField({
      name: "serialNumber",
      label: "Serial Number",
      type: "text",
      isRequired: true,
      displayOrder: 1,
      groupName: "Hardware",
      isActive: true,
    })
  }

  // ... rest of component
}
```

### Categories Management

```tsx
import { useAssetCategories } from "@/lib/modules/asset-types"

function CategoryManager() {
  const { categories, createCategory, getCategories } = useAssetCategories()

  const handleCreateCategory = async () => {
    await createCategory({
      name: "IT Equipment",
      description: "Information Technology assets",
      level: 1,
      path: "/IT Equipment",
      isActive: true,
    })
  }

  // ... rest of component
}
```

### Templates Usage

```tsx
import { useAssetTypeTemplates } from "@/lib/modules/asset-types"

function TemplateSelector() {
  const { templates, getTemplates, applyTemplate } = useAssetTypeTemplates()

  const handleApplyTemplate = async (templateId: string) => {
    const assetTypeData = await applyTemplate(templateId, {
      name: "Custom Laptop Type",
      // ... other customizations
    })
    
    // Use assetTypeData to create new asset type
  }

  // ... rest of component
}
```

### Direct Store Access

```tsx
import { useAssetTypeStore } from "@/store/asset-type-store"

function AssetTypeStats() {
  const store = useAssetTypeStore()
  const filteredAssetTypes = store.getFilteredAssetTypes()

  return (
    <div>
      <p>Total Asset Types: {store.assetTypes.length}</p>
      <p>Filtered Results: {filteredAssetTypes.length}</p>
      <p>Loading: {store.loading ? "Yes" : "No"}</p>
    </div>
  )
}
```

### Validation

```tsx
import { useAssetTypeValidation } from "@/lib/modules/asset-types"

function AssetTypeForm({ assetType }) {
  const { validateAssetType } = useAssetTypeValidation()
  const [validationResult, setValidationResult] = useState(null)

  const handleValidate = async () => {
    const result = await validateAssetType(assetType)
    setValidationResult(result)
  }

  return (
    <div>
      {validationResult && !validationResult.isValid && (
        <div className="errors">
          {validationResult.errors.map((error, index) => (
            <div key={index} className="error">
              {error.message}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
```

### Depreciation Calculations

```tsx
import { useDepreciationSettings } from "@/lib/modules/asset-types"

function DepreciationCalculator({ assetTypeId }) {
  const { calculateDepreciation } = useDepreciationSettings(assetTypeId)

  const calculateAssetDepreciation = () => {
    const settings = {
      method: "straight_line",
      usefulLife: 3,
      usefulLifeUnit: "years",
      salvageValue: 10,
      salvageValueType: "percentage",
      startDate: new Date().toISOString(),
      isActive: true,
    }

    const result = calculateDepreciation(10000, settings)
    console.log("Annual Depreciation:", result.annualDepreciation)
    console.log("Book Value:", result.bookValue)
  }

  // ... rest of component
}
```

## Service Methods

### AssetTypeService

- `initialize()` - Initialize service with database data
- `createAssetType(assetType)` - Create new asset type
- `updateAssetType(id, updates)` - Update existing asset type
- `deleteAssetType(id)` - Delete asset type
- `getAssetType(id)` - Get single asset type
- `getAssetTypes(filters)` - Get filtered asset types
- `refreshData()` - Refresh data from database
- `clearCache()` - Clear cache and reload

### Custom Fields

- `addCustomField(assetTypeId, field)` - Add custom field
- `updateCustomField(assetTypeId, fieldId, updates)` - Update custom field
- `removeCustomField(assetTypeId, fieldId)` - Remove custom field

### Lifecycle Stages

- `addLifecycleStage(assetTypeId, stage)` - Add lifecycle stage
- `updateLifecycleStage(assetTypeId, stageId, updates)` - Update lifecycle stage

### Maintenance Schedules

- `addMaintenanceSchedule(assetTypeId, schedule)` - Add maintenance schedule
- `updateMaintenanceSchedule(assetTypeId, scheduleId, updates)` - Update maintenance schedule

### Categories

- `createCategory(category)` - Create new category
- `getCategories(parentId)` - Get categories by parent

### Templates

- `createTemplate(template)` - Create new template
- `getTemplates(category)` - Get templates by category
- `applyTemplate(templateId, customizations)` - Apply template with customizations

## Store State

The Zustand store maintains:

- `assetTypes` - Array of asset types
- `categories` - Array of categories
- `templates` - Array of templates
- `metrics` - Analytics metrics
- `loading` - Loading state
- `error` - Error message
- `selectedAssetType` - Currently selected asset type
- `filters` - Current filters

## Error Handling

All service methods include comprehensive error handling:

- Database errors are caught and logged
- User-friendly error messages are stored in the Zustand store
- Loading states are properly managed
- Validation errors are returned with detailed information

## Performance Optimizations

- **Caching**: Store acts as cache for frequently accessed data
- **Selective Updates**: Only affected data is updated in store
- **Lazy Loading**: Data loaded on demand
- **Memoization**: React hooks use useCallback for performance
- **Batch Operations**: Multiple operations batched when possible

## Database Schema

The service works with the following Prisma models:

- `AssetType` - Main asset type entity
- `AssetCategory` - Hierarchical categories
- `CustomField` - Dynamic custom fields
- `LifecycleStage` - Asset lifecycle stages
- `MaintenanceSchedule` - Maintenance schedules
- `DepreciationSettings` - Depreciation configuration
- `AssetTypeTemplate` - Reusable templates

## Migration from Mock Data

The service has been completely refactored to remove all mock data and use production-grade patterns:

1. **Database Integration**: All data operations use Prisma ORM
2. **State Management**: Zustand store replaces in-memory arrays
3. **Error Handling**: Comprehensive error handling added
4. **Validation**: Database-backed validation system
5. **Caching**: Intelligent caching with store synchronization
6. **Hooks**: React hooks for easy component integration

This implementation is ready for production use and can handle real-world asset management requirements.