// Main service classes (client-side only)
export { AssetTypeService } from "./services"
// Note: AssetTypeDbService is server-side only and should not be exported for client use

// React hooks
export {
  useAssetTypeService,
  useCustomFields,
  useLifecycleStages,
  useMaintenanceSchedules,
  useDepreciationSettings,
  useAssetCategories,
  useAssetTypeTemplates,
  useAssetTypeAnalytics,
  useAssetTypeValidation,
  // Store hooks
  useAssetTypes,
  useCategories,
  useTemplates,
  useAssetTypeMetrics,
  useAssetTypeLoading,
  useAssetTypeError,
  useSelectedAssetType,
  useAssetTypeFilters,
  useFilteredAssetTypes,
} from "./hooks"

// Zustand store
export {
  useAssetTypeStore,
  useAssetTypes as useAssetTypesSelector,
  useCategories as useCategoriesSelector,
  useTemplates as useTemplatesSelector,
  useAssetTypeMetrics as useAssetTypeMetricsSelector,
  useAssetTypeLoading as useAssetTypeLoadingSelector,
  useAssetTypeError as useAssetTypeErrorSelector,
  useSelectedAssetType as useSelectedAssetTypeSelector,
  useAssetTypeFilters as useAssetTypeFiltersSelector,
  useFilteredAssetTypes as useFilteredAssetTypesSelector,
} from "@/store/asset-type-store"

// Types
export type {
  AssetType,
  AssetCategory,
  CustomField,
  CustomFieldType,
  FieldValidation,
  FieldOption,
  ConditionalLogic,
  LifecycleStage,
  AutomatedAction,
  NotificationRule,
  MaintenanceSchedule,
  MaintenanceType,
  MaintenancePriority,
  MaintenanceFrequency,
  RequiredPart,
  ChecklistItem,
  MaintenanceTrigger,
  DepreciationSettings,
  DepreciationMethod,
  DepreciationRate,
  AcceleratedDepreciation,
  ImpairmentSettings,
  AssetTypeTemplate,
  AssetTypeMetrics,
  AssetTypeValidationResult,
  ValidationError,
  ValidationWarning,
} from "./types"