import { useEffect, useCallback } from "react"
import { AssetTypeService } from "./services"
import {
  useAssetTypeStore,
  useAssetTypes,
  useCategories,
  useTemplates,
  useAssetTypeMetrics,
  useAssetTypeLoading,
  useAssetTypeError,
  useSelectedAssetType,
  useAssetTypeFilters,
  useFilteredAssetTypes,
} from "@/store/asset-type-store"
import type {
  AssetType,
  AssetCategory,
  CustomField,
  LifecycleStage,
  MaintenanceSchedule,
  DepreciationSettings,
  AssetTypeTemplate,
} from "./types"

// Main hook for asset type operations
export function useAssetTypeService() {
  const service = AssetTypeService.getInstance()
  const store = useAssetTypeStore()

  // Initialize service on mount
  useEffect(() => {
    service.initialize().catch(console.error)
  }, [service])

  const createAssetType = useCallback(
    async (assetType: Omit<AssetType, "id" | "createdAt" | "updatedAt" | "version">) => {
      return await service.createAssetType(assetType)
    },
    [service]
  )

  const updateAssetType = useCallback(
    async (id: string, updates: Partial<AssetType>) => {
      return await service.updateAssetType(id, updates)
    },
    [service]
  )

  const deleteAssetType = useCallback(
    async (id: string) => {
      return await service.deleteAssetType(id)
    },
    [service]
  )

  const getAssetType = useCallback(
    async (id: string) => {
      return await service.getAssetType(id)
    },
    [service]
  )

  const getAssetTypes = useCallback(
    async (filters?: { category?: string; isActive?: boolean; search?: string }) => {
      return await service.getAssetTypes(filters)
    },
    [service]
  )

  const refreshData = useCallback(async () => {
    await service.refreshData()
  }, [service])

  const clearCache = useCallback(async () => {
    await service.clearCache()
  }, [service])

  return {
    // Service methods
    createAssetType,
    updateAssetType,
    deleteAssetType,
    getAssetType,
    getAssetTypes,
    refreshData,
    clearCache,
    
    // Store state
    assetTypes: store.assetTypes,
    categories: store.categories,
    templates: store.templates,
    metrics: store.metrics,
    loading: store.loading,
    error: store.error,
    selectedAssetType: store.selectedAssetType,
    filters: store.filters,
    
    // Store actions
    setSelectedAssetType: store.setSelectedAssetType,
    setFilters: store.setFilters,
    clearError: store.clearError,
    
    // Computed values
    filteredAssetTypes: store.getFilteredAssetTypes(),
  }
}

// Hook for custom field operations
export function useCustomFields(assetTypeId: string) {
  const service = AssetTypeService.getInstance()

  const addCustomField = useCallback(
    async (field: Omit<CustomField, "id">) => {
      return await service.addCustomField(assetTypeId, field)
    },
    [service, assetTypeId]
  )

  const updateCustomField = useCallback(
    async (fieldId: string, updates: Partial<CustomField>) => {
      return await service.updateCustomField(assetTypeId, fieldId, updates)
    },
    [service, assetTypeId]
  )

  const removeCustomField = useCallback(
    async (fieldId: string) => {
      return await service.removeCustomField(assetTypeId, fieldId)
    },
    [service, assetTypeId]
  )

  return {
    addCustomField,
    updateCustomField,
    removeCustomField,
  }
}

// Hook for lifecycle stage operations
export function useLifecycleStages(assetTypeId: string) {
  const service = AssetTypeService.getInstance()

  const addLifecycleStage = useCallback(
    async (stage: Omit<LifecycleStage, "id">) => {
      return await service.addLifecycleStage(assetTypeId, stage)
    },
    [service, assetTypeId]
  )

  const updateLifecycleStage = useCallback(
    async (stageId: string, updates: Partial<LifecycleStage>) => {
      return await service.updateLifecycleStage(assetTypeId, stageId, updates)
    },
    [service, assetTypeId]
  )

  return {
    addLifecycleStage,
    updateLifecycleStage,
  }
}

// Hook for maintenance schedule operations
export function useMaintenanceSchedules(assetTypeId: string) {
  const service = AssetTypeService.getInstance()

  const addMaintenanceSchedule = useCallback(
    async (schedule: Omit<MaintenanceSchedule, "id">) => {
      return await service.addMaintenanceSchedule(assetTypeId, schedule)
    },
    [service, assetTypeId]
  )

  const updateMaintenanceSchedule = useCallback(
    async (scheduleId: string, updates: Partial<MaintenanceSchedule>) => {
      return await service.updateMaintenanceSchedule(assetTypeId, scheduleId, updates)
    },
    [service, assetTypeId]
  )

  return {
    addMaintenanceSchedule,
    updateMaintenanceSchedule,
  }
}

// Hook for depreciation settings
export function useDepreciationSettings(assetTypeId: string) {
  const service = AssetTypeService.getInstance()

  const updateDepreciationSettings = useCallback(
    async (settings: DepreciationSettings) => {
      return await service.updateDepreciationSettings(assetTypeId, settings)
    },
    [service, assetTypeId]
  )

  const calculateDepreciation = useCallback(
    (assetValue: number, settings: DepreciationSettings, currentDate?: string) => {
      return service.calculateDepreciation(assetValue, settings, currentDate)
    },
    [service]
  )

  return {
    updateDepreciationSettings,
    calculateDepreciation,
  }
}

// Hook for category operations
export function useAssetCategories() {
  const service = AssetTypeService.getInstance()
  const categories = useCategories()

  const createCategory = useCallback(
    async (category: Omit<AssetCategory, "id">) => {
      return await service.createCategory(category)
    },
    [service]
  )

  const getCategories = useCallback(
    async (parentId?: string) => {
      return await service.getCategories(parentId)
    },
    [service]
  )

  return {
    categories,
    createCategory,
    getCategories,
  }
}

// Hook for template operations
export function useAssetTypeTemplates() {
  const service = AssetTypeService.getInstance()
  const templates = useTemplates()

  const createTemplate = useCallback(
    async (template: Omit<AssetTypeTemplate, "id" | "createdAt" | "usageCount" | "rating">) => {
      return await service.createTemplate(template)
    },
    [service]
  )

  const getTemplates = useCallback(
    async (category?: string) => {
      return await service.getTemplates(category)
    },
    [service]
  )

  const applyTemplate = useCallback(
    async (templateId: string, customizations?: Partial<AssetType>) => {
      return await service.applyTemplate(templateId, customizations)
    },
    [service]
  )

  return {
    templates,
    createTemplate,
    getTemplates,
    applyTemplate,
  }
}

// Hook for metrics and analytics
export function useAssetTypeAnalytics() {
  const service = AssetTypeService.getInstance()
  const metrics = useAssetTypeMetrics()

  const getMetrics = useCallback(async () => {
    return await service.getMetrics()
  }, [service])

  return {
    metrics,
    getMetrics,
  }
}

// Hook for validation
export function useAssetTypeValidation() {
  const service = AssetTypeService.getInstance()

  const validateAssetType = useCallback(
    async (assetType: AssetType) => {
      return await service.validateAssetType(assetType)
    },
    [service]
  )

  const isAssetTypeInUse = useCallback(
    async (assetTypeId: string) => {
      return await service.isAssetTypeInUse(assetTypeId)
    },
    [service]
  )

  const isCustomFieldInUse = useCallback(
    async (assetTypeId: string, fieldId: string) => {
      return await service.isCustomFieldInUse(assetTypeId, fieldId)
    },
    [service]
  )

  return {
    validateAssetType,
    isAssetTypeInUse,
    isCustomFieldInUse,
  }
}

// Convenience hooks for accessing store state directly
export {
  useAssetTypes,
  useCategories,
  useTemplates,
  useAssetTypeMetrics,
  useAssetTypeLoading,
  useAssetTypeError,
  useSelectedAssetType,
  useAssetTypeFilters,
  useFilteredAssetTypes,
}