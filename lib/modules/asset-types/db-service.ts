import { PrismaClient } from "@prisma/client"
import type {
  AssetType,
  AssetCategory,
  CustomField,
  LifecycleStage,
  MaintenanceSchedule,
  DepreciationSettings,
  AssetTypeTemplate,
  AssetTypeMetrics,
  AssetTypeValidationResult,
  ValidationError,
  ValidationWarning,
  CustomFieldType,
  DepreciationMethod,
  MaintenanceType,
  MaintenancePriority,
} from "./types"

const prisma = new PrismaClient()

export class AssetTypeDbService {
  private static instance: AssetTypeDbService

  static getInstance(): AssetTypeDbService {
    if (!AssetTypeDbService.instance) {
      AssetTypeDbService.instance = new AssetTypeDbService()
    }
    return AssetTypeDbService.instance
  }

  // Transform Prisma models to our interface types
  private transformAssetType(prismaAssetType: any): AssetType {
    return {
      id: prismaAssetType.id,
      name: prismaAssetType.name,
      code: prismaAssetType.code,
      description: prismaAssetType.description,
      category: this.transformCategory(prismaAssetType.category),
      subcategory: prismaAssetType.subcategory,
      icon: prismaAssetType.icon,
      color: prismaAssetType.color,
      isActive: prismaAssetType.isActive,
      customFields: prismaAssetType.customFields?.map(this.transformCustomField) || [],
      lifecycleStages: prismaAssetType.lifecycleStages?.map(this.transformLifecycleStage) || [],
      maintenanceSchedules: prismaAssetType.maintenanceSchedules?.map(this.transformMaintenanceSchedule) || [],
      depreciationSettings: prismaAssetType.depreciationSettings ? this.transformDepreciationSettings(prismaAssetType.depreciationSettings) : this.getDefaultDepreciationSettings(),
      tags: prismaAssetType.tags,
      createdAt: prismaAssetType.createdAt.toISOString(),
      updatedAt: prismaAssetType.updatedAt.toISOString(),
      createdBy: prismaAssetType.createdBy,
      version: prismaAssetType.version,
    }
  }

  private transformCategory(prismaCategory: any): AssetCategory {
    return {
      id: prismaCategory.id,
      name: prismaCategory.name,
      description: prismaCategory.description,
      parentId: prismaCategory.parentId,
      level: prismaCategory.level,
      path: prismaCategory.path,
      isActive: prismaCategory.isActive,
    }
  }

  private transformCustomField(prismaField: any): CustomField {
    return {
      id: prismaField.id,
      name: prismaField.name,
      label: prismaField.label,
      type: prismaField.type as CustomFieldType,
      description: prismaField.description,
      isRequired: prismaField.isRequired,
      isUnique: prismaField.isUnique,
      defaultValue: prismaField.defaultValue ? JSON.parse(prismaField.defaultValue) : undefined,
      validation: prismaField.validation ? JSON.parse(prismaField.validation) : { errorMessage: "Invalid value" },
      options: prismaField.options ? JSON.parse(prismaField.options) : undefined,
      conditionalLogic: prismaField.conditionalLogic ? JSON.parse(prismaField.conditionalLogic) : undefined,
      displayOrder: prismaField.displayOrder,
      groupName: prismaField.groupName,
      isActive: prismaField.isActive,
    }
  }

  private transformLifecycleStage(prismaStage: any): LifecycleStage {
    return {
      id: prismaStage.id,
      name: prismaStage.name,
      code: prismaStage.code,
      description: prismaStage.description,
      order: prismaStage.order,
      isInitial: prismaStage.isInitial,
      isFinal: prismaStage.isFinal,
      color: prismaStage.color,
      icon: prismaStage.icon,
      allowedTransitions: prismaStage.allowedTransitions,
      requiredFields: prismaStage.requiredFields,
      automatedActions: prismaStage.automatedActions ? JSON.parse(prismaStage.automatedActions) : [],
      notifications: prismaStage.notifications ? JSON.parse(prismaStage.notifications) : [],
      isActive: prismaStage.isActive,
    }
  }

  private transformMaintenanceSchedule(prismaSchedule: any): MaintenanceSchedule {
    return {
      id: prismaSchedule.id,
      name: prismaSchedule.name,
      description: prismaSchedule.description,
      type: prismaSchedule.type as MaintenanceType,
      frequency: JSON.parse(prismaSchedule.frequency),
      priority: prismaSchedule.priority as MaintenancePriority,
      estimatedDuration: prismaSchedule.estimatedDuration,
      estimatedCost: prismaSchedule.estimatedCost,
      requiredSkills: prismaSchedule.requiredSkills,
      requiredParts: prismaSchedule.requiredParts ? JSON.parse(prismaSchedule.requiredParts) : [],
      instructions: prismaSchedule.instructions,
      checklistItems: prismaSchedule.checklistItems ? JSON.parse(prismaSchedule.checklistItems) : [],
      triggers: prismaSchedule.triggers ? JSON.parse(prismaSchedule.triggers) : [],
      isActive: prismaSchedule.isActive,
    }
  }

  private transformDepreciationSettings(prismaSettings: any): DepreciationSettings {
    return {
      method: prismaSettings.method as DepreciationMethod,
      usefulLife: prismaSettings.usefulLife,
      usefulLifeUnit: prismaSettings.usefulLifeUnit as "years" | "months" | "hours" | "cycles",
      salvageValue: prismaSettings.salvageValue,
      salvageValueType: prismaSettings.salvageValueType as "fixed" | "percentage",
      startDate: prismaSettings.startDate.toISOString(),
      customRates: prismaSettings.customRates ? JSON.parse(prismaSettings.customRates) : undefined,
      acceleratedDepreciation: prismaSettings.acceleratedDepreciation ? JSON.parse(prismaSettings.acceleratedDepreciation) : undefined,
      impairmentSettings: prismaSettings.impairmentSettings ? JSON.parse(prismaSettings.impairmentSettings) : undefined,
      isActive: prismaSettings.isActive,
    }
  }

  // Default values
  private getDefaultDepreciationSettings(): DepreciationSettings {
    return {
      method: "straight_line",
      usefulLife: 5,
      usefulLifeUnit: "years",
      salvageValue: 0,
      salvageValueType: "percentage",
      startDate: new Date().toISOString(),
      isActive: true,
    }
  }

  // Asset Type CRUD Operations
  async createAssetType(assetType: Omit<AssetType, "id" | "createdAt" | "updatedAt" | "version">): Promise<AssetType> {
    const validation = await this.validateAssetType(assetType as AssetType)
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.map((e) => e.message).join(", ")}`)
    }

    const createdAssetType = await prisma.assetType.create({
      data: {
        name: assetType.name,
        code: assetType.code,
        description: assetType.description,
        categoryId: assetType.category.id,
        subcategory: assetType.subcategory,
        icon: assetType.icon,
        color: assetType.color,
        isActive: assetType.isActive,
        tags: assetType.tags,
        createdBy: assetType.createdBy,
      },
      include: {
        category: true,
        customFields: true,
        lifecycleStages: true,
        maintenanceSchedules: true,
        depreciationSettings: true,
      },
    })

    // Create related entities
    if (assetType.customFields.length > 0) {
      await Promise.all(
        assetType.customFields.map((field) =>
          this.addCustomField(createdAssetType.id, field)
        )
      )
    }

    if (assetType.lifecycleStages.length > 0) {
      await Promise.all(
        assetType.lifecycleStages.map((stage) =>
          this.addLifecycleStage(createdAssetType.id, stage)
        )
      )
    }

    if (assetType.maintenanceSchedules.length > 0) {
      await Promise.all(
        assetType.maintenanceSchedules.map((schedule) =>
          this.addMaintenanceSchedule(createdAssetType.id, schedule)
        )
      )
    }

    if (assetType.depreciationSettings) {
      await this.updateDepreciationSettings(createdAssetType.id, assetType.depreciationSettings)
    }

    return this.getAssetType(createdAssetType.id) as Promise<AssetType>
  }

  async updateAssetType(id: string, updates: Partial<AssetType>): Promise<AssetType | null> {
    const existingAssetType = await this.getAssetType(id)
    if (!existingAssetType) return null

    const updatedData: any = {}
    if (updates.name) updatedData.name = updates.name
    if (updates.description) updatedData.description = updates.description
    if (updates.category) updatedData.categoryId = updates.category.id
    if (updates.subcategory !== undefined) updatedData.subcategory = updates.subcategory
    if (updates.icon) updatedData.icon = updates.icon
    if (updates.color) updatedData.color = updates.color
    if (updates.isActive !== undefined) updatedData.isActive = updates.isActive
    if (updates.tags) updatedData.tags = updates.tags
    
    updatedData.version = existingAssetType.version + 1

    const updatedAssetType = await prisma.assetType.update({
      where: { id },
      data: updatedData,
      include: {
        category: true,
        customFields: true,
        lifecycleStages: true,
        maintenanceSchedules: true,
        depreciationSettings: true,
      },
    })

    return this.transformAssetType(updatedAssetType)
  }

  async deleteAssetType(id: string): Promise<boolean> {
    // Check if asset type is in use
    const isInUse = await this.isAssetTypeInUse(id)
    if (isInUse) {
      throw new Error("Cannot delete asset type that is currently in use")
    }

    try {
      await prisma.assetType.delete({
        where: { id },
      })
      return true
    } catch (error) {
      console.error("Error deleting asset type:", error)
      return false
    }
  }

  async getAssetType(id: string): Promise<AssetType | null> {
    const assetType = await prisma.assetType.findUnique({
      where: { id },
      include: {
        category: true,
        customFields: true,
        lifecycleStages: { orderBy: { order: "asc" } },
        maintenanceSchedules: true,
        depreciationSettings: true,
      },
    })

    return assetType ? this.transformAssetType(assetType) : null
  }

  async getAssetTypes(filters?: {
    category?: string
    isActive?: boolean
    search?: string
  }): Promise<AssetType[]> {
    const where: any = {}

    if (filters?.category) {
      where.categoryId = filters.category
    }

    if (filters?.isActive !== undefined) {
      where.isActive = filters.isActive
    }

    if (filters?.search) {
      const search = filters.search.toLowerCase()
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { code: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { tags: { hasSome: [search] } },
      ]
    }

    const assetTypes = await prisma.assetType.findMany({
      where,
      include: {
        category: true,
        customFields: true,
        lifecycleStages: { orderBy: { order: "asc" } },
        maintenanceSchedules: true,
        depreciationSettings: true,
      },
      orderBy: { name: "asc" },
    })

    return assetTypes.map(this.transformAssetType.bind(this))
  }

  // Custom Fields Management
  async addCustomField(assetTypeId: string, field: Omit<CustomField, "id">): Promise<CustomField | null> {
    const assetType = await prisma.assetType.findUnique({ where: { id: assetTypeId } })
    if (!assetType) return null

    const createdField = await prisma.customField.create({
      data: {
        assetTypeId,
        name: field.name,
        label: field.label,
        type: field.type,
        description: field.description,
        isRequired: field.isRequired,
        isUnique: field.isUnique,
        defaultValue: field.defaultValue ? JSON.stringify(field.defaultValue) : null,
        validation: JSON.stringify(field.validation),
        options: field.options ? JSON.stringify(field.options) : null,
        conditionalLogic: field.conditionalLogic ? JSON.stringify(field.conditionalLogic) : null,
        displayOrder: field.displayOrder,
        groupName: field.groupName,
        isActive: field.isActive,
      },
    })

    // Update asset type version
    await prisma.assetType.update({
      where: { id: assetTypeId },
      data: { version: { increment: 1 } },
    })

    return this.transformCustomField(createdField)
  }

  async updateCustomField(
    assetTypeId: string,
    fieldId: string,
    updates: Partial<CustomField>
  ): Promise<CustomField | null> {
    const field = await prisma.customField.findFirst({
      where: { id: fieldId, assetTypeId },
    })

    if (!field) return null

    const updateData: any = {}
    if (updates.name) updateData.name = updates.name
    if (updates.label) updateData.label = updates.label
    if (updates.type) updateData.type = updates.type
    if (updates.description !== undefined) updateData.description = updates.description
    if (updates.isRequired !== undefined) updateData.isRequired = updates.isRequired
    if (updates.isUnique !== undefined) updateData.isUnique = updates.isUnique
    if (updates.defaultValue !== undefined) updateData.defaultValue = JSON.stringify(updates.defaultValue)
    if (updates.validation) updateData.validation = JSON.stringify(updates.validation)
    if (updates.options !== undefined) updateData.options = updates.options ? JSON.stringify(updates.options) : null
    if (updates.conditionalLogic !== undefined) updateData.conditionalLogic = updates.conditionalLogic ? JSON.stringify(updates.conditionalLogic) : null
    if (updates.displayOrder !== undefined) updateData.displayOrder = updates.displayOrder
    if (updates.groupName !== undefined) updateData.groupName = updates.groupName
    if (updates.isActive !== undefined) updateData.isActive = updates.isActive

    const updatedField = await prisma.customField.update({
      where: { id: fieldId },
      data: updateData,
    })

    // Update asset type version
    await prisma.assetType.update({
      where: { id: assetTypeId },
      data: { version: { increment: 1 } },
    })

    return this.transformCustomField(updatedField)
  }

  async removeCustomField(assetTypeId: string, fieldId: string): Promise<boolean> {
    // Check if field is in use
    const isInUse = await this.isCustomFieldInUse(assetTypeId, fieldId)
    if (isInUse) {
      throw new Error("Cannot remove custom field that contains data")
    }

    const result = await prisma.customField.deleteMany({
      where: { id: fieldId, assetTypeId },
    })

    if (result.count > 0) {
      // Update asset type version
      await prisma.assetType.update({
        where: { id: assetTypeId },
        data: { version: { increment: 1 } },
      })
      return true
    }

    return false
  }

  // Lifecycle Management
  async addLifecycleStage(assetTypeId: string, stage: Omit<LifecycleStage, "id">): Promise<LifecycleStage | null> {
    const assetType = await prisma.assetType.findUnique({ where: { id: assetTypeId } })
    if (!assetType) return null

    const createdStage = await prisma.lifecycleStage.create({
      data: {
        assetTypeId,
        name: stage.name,
        code: stage.code,
        description: stage.description,
        order: stage.order,
        isInitial: stage.isInitial,
        isFinal: stage.isFinal,
        color: stage.color,
        icon: stage.icon,
        allowedTransitions: stage.allowedTransitions,
        requiredFields: stage.requiredFields,
        automatedActions: stage.automatedActions ? JSON.stringify(stage.automatedActions) : null,
        notifications: stage.notifications ? JSON.stringify(stage.notifications) : null,
        isActive: stage.isActive,
      },
    })

    // Update asset type version
    await prisma.assetType.update({
      where: { id: assetTypeId },
      data: { version: { increment: 1 } },
    })

    return this.transformLifecycleStage(createdStage)
  }

  async updateLifecycleStage(
    assetTypeId: string,
    stageId: string,
    updates: Partial<LifecycleStage>
  ): Promise<LifecycleStage | null> {
    const stage = await prisma.lifecycleStage.findFirst({
      where: { id: stageId, assetTypeId },
    })

    if (!stage) return null

    const updateData: any = {}
    if (updates.name) updateData.name = updates.name
    if (updates.code) updateData.code = updates.code
    if (updates.description) updateData.description = updates.description
    if (updates.order !== undefined) updateData.order = updates.order
    if (updates.isInitial !== undefined) updateData.isInitial = updates.isInitial
    if (updates.isFinal !== undefined) updateData.isFinal = updates.isFinal
    if (updates.color) updateData.color = updates.color
    if (updates.icon) updateData.icon = updates.icon
    if (updates.allowedTransitions) updateData.allowedTransitions = updates.allowedTransitions
    if (updates.requiredFields) updateData.requiredFields = updates.requiredFields
    if (updates.automatedActions !== undefined) updateData.automatedActions = updates.automatedActions ? JSON.stringify(updates.automatedActions) : null
    if (updates.notifications !== undefined) updateData.notifications = updates.notifications ? JSON.stringify(updates.notifications) : null
    if (updates.isActive !== undefined) updateData.isActive = updates.isActive

    const updatedStage = await prisma.lifecycleStage.update({
      where: { id: stageId },
      data: updateData,
    })

    // Update asset type version
    await prisma.assetType.update({
      where: { id: assetTypeId },
      data: { version: { increment: 1 } },
    })

    return this.transformLifecycleStage(updatedStage)
  }

  // Maintenance Scheduling
  async addMaintenanceSchedule(
    assetTypeId: string,
    schedule: Omit<MaintenanceSchedule, "id">
  ): Promise<MaintenanceSchedule | null> {
    const assetType = await prisma.assetType.findUnique({ where: { id: assetTypeId } })
    if (!assetType) return null

    const createdSchedule = await prisma.maintenanceSchedule.create({
      data: {
        assetTypeId,
        name: schedule.name,
        description: schedule.description,
        type: schedule.type,
        frequency: JSON.stringify(schedule.frequency),
        priority: schedule.priority,
        estimatedDuration: schedule.estimatedDuration,
        estimatedCost: schedule.estimatedCost,
        requiredSkills: schedule.requiredSkills,
        requiredParts: schedule.requiredParts ? JSON.stringify(schedule.requiredParts) : null,
        instructions: schedule.instructions,
        checklistItems: schedule.checklistItems ? JSON.stringify(schedule.checklistItems) : null,
        triggers: schedule.triggers ? JSON.stringify(schedule.triggers) : null,
        isActive: schedule.isActive,
      },
    })

    // Update asset type version
    await prisma.assetType.update({
      where: { id: assetTypeId },
      data: { version: { increment: 1 } },
    })

    return this.transformMaintenanceSchedule(createdSchedule)
  }

  async updateMaintenanceSchedule(
    assetTypeId: string,
    scheduleId: string,
    updates: Partial<MaintenanceSchedule>
  ): Promise<MaintenanceSchedule | null> {
    const schedule = await prisma.maintenanceSchedule.findFirst({
      where: { id: scheduleId, assetTypeId },
    })

    if (!schedule) return null

    const updateData: any = {}
    if (updates.name) updateData.name = updates.name
    if (updates.description) updateData.description = updates.description
    if (updates.type) updateData.type = updates.type
    if (updates.frequency) updateData.frequency = JSON.stringify(updates.frequency)
    if (updates.priority) updateData.priority = updates.priority
    if (updates.estimatedDuration !== undefined) updateData.estimatedDuration = updates.estimatedDuration
    if (updates.estimatedCost !== undefined) updateData.estimatedCost = updates.estimatedCost
    if (updates.requiredSkills) updateData.requiredSkills = updates.requiredSkills
    if (updates.requiredParts !== undefined) updateData.requiredParts = updates.requiredParts ? JSON.stringify(updates.requiredParts) : null
    if (updates.instructions) updateData.instructions = updates.instructions
    if (updates.checklistItems !== undefined) updateData.checklistItems = updates.checklistItems ? JSON.stringify(updates.checklistItems) : null
    if (updates.triggers !== undefined) updateData.triggers = updates.triggers ? JSON.stringify(updates.triggers) : null
    if (updates.isActive !== undefined) updateData.isActive = updates.isActive

    const updatedSchedule = await prisma.maintenanceSchedule.update({
      where: { id: scheduleId },
      data: updateData,
    })

    // Update asset type version
    await prisma.assetType.update({
      where: { id: assetTypeId },
      data: { version: { increment: 1 } },
    })

    return this.transformMaintenanceSchedule(updatedSchedule)
  }

  // Depreciation Management
  async updateDepreciationSettings(
    assetTypeId: string,
    settings: DepreciationSettings
  ): Promise<DepreciationSettings | null> {
    const assetType = await prisma.assetType.findUnique({ where: { id: assetTypeId } })
    if (!assetType) return null

    const updatedSettings = await prisma.depreciationSettings.upsert({
      where: { assetTypeId },
      update: {
        method: settings.method,
        usefulLife: settings.usefulLife,
        usefulLifeUnit: settings.usefulLifeUnit,
        salvageValue: settings.salvageValue,
        salvageValueType: settings.salvageValueType,
        startDate: new Date(settings.startDate),
        customRates: settings.customRates ? JSON.stringify(settings.customRates) : null,
        acceleratedDepreciation: settings.acceleratedDepreciation ? JSON.stringify(settings.acceleratedDepreciation) : null,
        impairmentSettings: settings.impairmentSettings ? JSON.stringify(settings.impairmentSettings) : null,
        isActive: settings.isActive,
      },
      create: {
        assetTypeId,
        method: settings.method,
        usefulLife: settings.usefulLife,
        usefulLifeUnit: settings.usefulLifeUnit,
        salvageValue: settings.salvageValue,
        salvageValueType: settings.salvageValueType,
        startDate: new Date(settings.startDate),
        customRates: settings.customRates ? JSON.stringify(settings.customRates) : null,
        acceleratedDepreciation: settings.acceleratedDepreciation ? JSON.stringify(settings.acceleratedDepreciation) : null,
        impairmentSettings: settings.impairmentSettings ? JSON.stringify(settings.impairmentSettings) : null,
        isActive: settings.isActive,
      },
    })

    // Update asset type version
    await prisma.assetType.update({
      where: { id: assetTypeId },
      data: { version: { increment: 1 } },
    })

    return this.transformDepreciationSettings(updatedSettings)
  }

  // Category Management
  async createCategory(category: Omit<AssetCategory, "id">): Promise<AssetCategory> {
    const createdCategory = await prisma.assetCategory.create({
      data: {
        name: category.name,
        description: category.description,
        parentId: category.parentId,
        level: category.level,
        path: category.path,
        isActive: category.isActive,
      },
    })

    return this.transformCategory(createdCategory)
  }

  async getCategories(parentId?: string): Promise<AssetCategory[]> {
    const categories = await prisma.assetCategory.findMany({
      where: { parentId },
      orderBy: { name: "asc" },
    })

    return categories.map(this.transformCategory.bind(this))
  }

  async getAllCategories(): Promise<AssetCategory[]> {
    const categories = await prisma.assetCategory.findMany({
      orderBy: { name: "asc" },
    })

    return categories.map(this.transformCategory.bind(this))
  }

  // Template Management
  async createTemplate(
    template: Omit<AssetTypeTemplate, "id" | "createdAt" | "usageCount" | "rating">
  ): Promise<AssetTypeTemplate> {
    const createdTemplate = await prisma.assetTypeTemplate.create({
      data: {
        name: template.name,
        description: template.description,
        category: template.category,
        assetType: JSON.stringify(template.assetType),
        isPublic: template.isPublic,
        createdBy: template.createdBy,
        tags: template.tags,
      },
    })

    return {
      id: createdTemplate.id,
      name: createdTemplate.name,
      description: createdTemplate.description,
      category: createdTemplate.category,
      assetType: JSON.parse(createdTemplate.assetType),
      isPublic: createdTemplate.isPublic,
      createdBy: createdTemplate.createdBy,
      createdAt: createdTemplate.createdAt.toISOString(),
      usageCount: createdTemplate.usageCount,
      rating: createdTemplate.rating,
      tags: createdTemplate.tags,
    }
  }

  async getTemplates(category?: string): Promise<AssetTypeTemplate[]> {
    const where: any = {}
    if (category) {
      where.category = category
    }

    const templates = await prisma.assetTypeTemplate.findMany({
      where,
      orderBy: { createdAt: "desc" },
    })

    return templates.map((template) => ({
      id: template.id,
      name: template.name,
      description: template.description,
      category: template.category,
      assetType: JSON.parse(template.assetType),
      isPublic: template.isPublic,
      createdBy: template.createdBy,
      createdAt: template.createdAt.toISOString(),
      usageCount: template.usageCount,
      rating: template.rating,
      tags: template.tags,
    }))
  }

  async updateTemplate(
    id: string,
    updates: Partial<AssetTypeTemplate>
  ): Promise<AssetTypeTemplate | null> {
    try {
      const updateData: any = {}
      
      if (updates.name) updateData.name = updates.name
      if (updates.description) updateData.description = updates.description
      if (updates.category) updateData.category = updates.category
      if (updates.assetType) updateData.assetType = JSON.stringify(updates.assetType)
      if (updates.isPublic !== undefined) updateData.isPublic = updates.isPublic
      if (updates.usageCount !== undefined) updateData.usageCount = updates.usageCount
      if (updates.rating !== undefined) updateData.rating = updates.rating
      if (updates.tags) updateData.tags = updates.tags

      const updatedTemplate = await prisma.assetTypeTemplate.update({
        where: { id },
        data: updateData,
      })

      return {
        id: updatedTemplate.id,
        name: updatedTemplate.name,
        description: updatedTemplate.description,
        category: updatedTemplate.category,
        assetType: JSON.parse(updatedTemplate.assetType),
        isPublic: updatedTemplate.isPublic,
        createdBy: updatedTemplate.createdBy,
        createdAt: updatedTemplate.createdAt.toISOString(),
        usageCount: updatedTemplate.usageCount,
        rating: updatedTemplate.rating,
        tags: updatedTemplate.tags,
      }
    } catch (error) {
      console.error("Error updating template:", error)
      return null
    }
  }

  // Metrics and Analytics
  async getMetrics(): Promise<AssetTypeMetrics> {
    const totalTypes = await prisma.assetType.count()
    const activeTypes = await prisma.assetType.count({ where: { isActive: true } })
    const totalAssets = await prisma.asset.count()

    // Assets by type
    const assetsByTypeData = await prisma.asset.groupBy({
      by: ['assetTypeId'],
      _count: { id: true },
      where: { assetTypeId: { not: null } },
    })

    const assetsByType: Record<string, number> = {}
    for (const item of assetsByTypeData) {
      if (item.assetTypeId) {
        const assetType = await prisma.assetType.findUnique({
          where: { id: item.assetTypeId },
          select: { name: true },
        })
        if (assetType) {
          assetsByType[assetType.name] = item._count.id
        }
      }
    }

    // Custom fields usage
    const customFieldsData = await prisma.customField.groupBy({
      by: ['type'],
      _count: { id: true },
      where: { isActive: true },
    })

    const customFieldsUsage: Record<string, number> = {}
    customFieldsData.forEach((item) => {
      customFieldsUsage[item.type] = item._count.id
    })

    // Lifecycle distribution
    const lifecycleData = await prisma.lifecycleStage.groupBy({
      by: ['name'],
      _count: { id: true },
      where: { isActive: true },
    })

    const lifecycleDistribution: Record<string, number> = {}
    lifecycleData.forEach((item) => {
      lifecycleDistribution[item.name] = item._count.id
    })

    // Maintenance schedule count
    const maintenanceScheduleCount = await prisma.maintenanceSchedule.count({
      where: { isActive: true },
    })

    // Depreciation methods
    const depreciationData = await prisma.depreciationSettings.groupBy({
      by: ['method'],
      _count: { id: true },
      where: { isActive: true },
    })

    const depreciationMethods: Record<string, number> = {}
    depreciationData.forEach((item) => {
      depreciationMethods[item.method] = item._count.id
    })

    return {
      totalTypes,
      activeTypes,
      totalAssets,
      assetsByType,
      customFieldsUsage,
      lifecycleDistribution,
      maintenanceScheduleCount,
      depreciationMethods,
    }
  }

  // Validation
  async validateAssetType(assetType: AssetType): Promise<AssetTypeValidationResult> {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []

    // Check required fields
    if (!assetType.name?.trim()) {
      errors.push({
        field: "name",
        message: "Asset type name is required",
        code: "REQUIRED_FIELD",
        severity: "error",
      })
    }

    if (!assetType.code?.trim()) {
      errors.push({
        field: "code",
        message: "Asset type code is required",
        code: "REQUIRED_FIELD",
        severity: "error",
      })
    }

    // Check code uniqueness (if updating)
    if (assetType.code) {
      const existingAssetType = await prisma.assetType.findFirst({
        where: {
          code: assetType.code,
          id: { not: assetType.id },
        },
      })

      if (existingAssetType) {
        errors.push({
          field: "code",
          message: "Asset type code must be unique",
          code: "DUPLICATE_CODE",
          severity: "error",
        })
      }
    }

    // Validate custom fields
    assetType.customFields?.forEach((field, index) => {
      if (!field.name?.trim()) {
        errors.push({
          field: `customFields[${index}].name`,
          message: "Custom field name is required",
          code: "REQUIRED_FIELD",
          severity: "error",
        })
      }

      if (!field.label?.trim()) {
        errors.push({
          field: `customFields[${index}].label`,
          message: "Custom field label is required",
          code: "REQUIRED_FIELD",
          severity: "error",
        })
      }
    })

    // Validate lifecycle stages
    const initialStages = assetType.lifecycleStages?.filter((stage) => stage.isInitial) || []
    if (initialStages.length === 0) {
      warnings.push({
        field: "lifecycleStages",
        message: "No initial lifecycle stage defined",
        suggestion: "Consider adding an initial stage for better asset tracking",
      })
    } else if (initialStages.length > 1) {
      errors.push({
        field: "lifecycleStages",
        message: "Only one initial lifecycle stage is allowed",
        code: "MULTIPLE_INITIAL_STAGES",
        severity: "error",
      })
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    }
  }

  // Utility methods
  async isAssetTypeInUse(assetTypeId: string): Promise<boolean> {
    const assetCount = await prisma.asset.count({
      where: { assetTypeId },
    })
    return assetCount > 0
  }

  async isCustomFieldInUse(assetTypeId: string, fieldId: string): Promise<boolean> {
    // This would require checking asset data for this field
    // For now, return false (implement based on your asset data storage strategy)
    return false
  }

  // Depreciation calculations (same as original service)
  calculateDepreciation(
    assetValue: number,
    settings: DepreciationSettings,
    currentDate: string = new Date().toISOString()
  ): {
    annualDepreciation: number
    accumulatedDepreciation: number
    bookValue: number
    remainingLife: number
  } {
    const startDate = new Date(settings.startDate)
    const current = new Date(currentDate)
    const yearsElapsed = (current.getTime() - startDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000)

    const salvageValue =
      settings.salvageValueType === "percentage" ? assetValue * (settings.salvageValue / 100) : settings.salvageValue

    const depreciableAmount = assetValue - salvageValue

    let annualDepreciation = 0
    let accumulatedDepreciation = 0

    switch (settings.method) {
      case "straight_line":
        annualDepreciation = depreciableAmount / settings.usefulLife
        accumulatedDepreciation = Math.min(annualDepreciation * yearsElapsed, depreciableAmount)
        break

      case "declining_balance":
        const rate = 1 / settings.usefulLife
        annualDepreciation = (assetValue - accumulatedDepreciation) * rate
        accumulatedDepreciation = assetValue * (1 - Math.pow(1 - rate, yearsElapsed)) - salvageValue
        break

      case "double_declining_balance":
        const doubleRate = 2 / settings.usefulLife
        annualDepreciation = (assetValue - accumulatedDepreciation) * doubleRate
        accumulatedDepreciation = Math.min(assetValue * (1 - Math.pow(1 - doubleRate, yearsElapsed)), depreciableAmount)
        break

      case "sum_of_years_digits":
        const sumOfYears = (settings.usefulLife * (settings.usefulLife + 1)) / 2
        const currentYear = Math.floor(yearsElapsed) + 1
        const remainingYears = Math.max(settings.usefulLife - currentYear + 1, 0)
        annualDepreciation = (depreciableAmount * remainingYears) / sumOfYears

        let totalAccumulated = 0
        for (let year = 1; year <= Math.min(currentYear, settings.usefulLife); year++) {
          const yearFraction = (settings.usefulLife - year + 1) / sumOfYears
          totalAccumulated += depreciableAmount * yearFraction
        }
        accumulatedDepreciation = totalAccumulated
        break

      default:
        annualDepreciation = depreciableAmount / settings.usefulLife
        accumulatedDepreciation = Math.min(annualDepreciation * yearsElapsed, depreciableAmount)
    }

    const bookValue = assetValue - accumulatedDepreciation
    const remainingLife = Math.max(settings.usefulLife - yearsElapsed, 0)

    return {
      annualDepreciation: Math.max(annualDepreciation, 0),
      accumulatedDepreciation: Math.max(accumulatedDepreciation, 0),
      bookValue: Math.max(bookValue, salvageValue),
      remainingLife: Math.max(remainingLife, 0),
    }
  }
}