import prisma from "@/lib/prisma";
import { AssetOperationType } from "@/lib/types/asset-type-forms";

export class AssetTypeFormService {
  /**
   * Get asset type form by ID with associated form definition
   */
  static async getAssetTypeFormById(id: string) {
    try {
      const assetTypeForm = await prisma.assetTypeForm.findUnique({
        where: { id },
        include: {
          form: {
            select: {
              id: true,
              name: true,
              description: true,
              sections: true,
              settings: true,
              version: true,
              isActive: true,
              createdAt: true,
              updatedAt: true,
            },
          },
          assetType: {
            select: {
              id: true,
              name: true,
              code: true,
              description: true,
              category: {
                select: {
                  id: true,
                  name: true,
                },
              },
              customFields: true,
            },
          },
        },
      });

      if (!assetTypeForm) {
        return null;
      }

      // Parse JSON fields
      return {
        ...assetTypeForm,
        form: {
          ...assetTypeForm.form,
          sections: JSON.parse(assetTypeForm.form.sections as string),
          settings: JSON.parse(assetTypeForm.form.settings as string),
        },
        assetType: {
          ...assetTypeForm.assetType,
          customFields: typeof assetTypeForm.assetType.customFields === 'string'
            ? JSON.parse(assetTypeForm.assetType.customFields)
            : assetTypeForm.assetType.customFields,
        },
      };
    } catch (error) {
      console.error("Error fetching asset type form:", error);
      throw error;
    }
  }

  /**
   * Update asset type form
   */
  static async updateAssetTypeForm(id: string, updates: any) {
    try {
      const updatedForm = await prisma.assetTypeForm.update({
        where: { id },
        data: {
          ...updates,
          updatedAt: new Date(),
        },
        include: {
          form: {
            select: {
              id: true,
              name: true,
              description: true,
              sections: true,
              settings: true,
              version: true,
              isActive: true,
              createdAt: true,
              updatedAt: true,
            },
          },
          assetType: {
            select: {
              id: true,
              name: true,
              code: true,
              description: true,
              category: {
                select: {
                  id: true,
                  name: true,
                },
              },
              customFields: true,
            },
          },
        },
      });

      if (!updatedForm) {
        return null;
      }

      // Parse JSON fields
      return {
        ...updatedForm,
        form: {
          ...updatedForm.form,
          sections: JSON.parse(updatedForm.form.sections as string),
          settings: JSON.parse(updatedForm.form.settings as string),
        },
        assetType: {
          ...updatedForm.assetType,
          customFields: typeof updatedForm.assetType.customFields === 'string'
            ? JSON.parse(updatedForm.assetType.customFields)
            : updatedForm.assetType.customFields,
        },
      };
    } catch (error) {
      console.error("Error updating asset type form:", error);
      throw error;
    }
  }

  /**
   * Delete asset type form
   */
  static async deleteAssetTypeForm(id: string) {
    try {
      await prisma.assetTypeForm.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      console.error("Error deleting asset type form:", error);
      return false;
    }
  }

  /**
   * Get all asset type forms for a specific asset type
   */
  static async getAssetTypeFormsByAssetTypeId(assetTypeId: string) {
    try {
      const forms = await prisma.assetTypeForm.findMany({
        where: { assetTypeId },
        include: {
          form: {
            select: {
              id: true,
              name: true,
              description: true,
              sections: true,
              settings: true,
              version: true,
              isActive: true,
              createdAt: true,
              updatedAt: true,
            },
          },
        },
        orderBy: [
          { operationType: "asc" },
          { isDefault: "desc" },
          { createdAt: "desc" },
        ],
      });

      // Parse JSON fields and format response
      return forms.map((form) => ({
        ...form,
        form: {
          ...form.form,
          sections: JSON.parse(form.form.sections as string),
          settings: JSON.parse(form.form.settings as string),
        },
      }));
    } catch (error) {
      console.error("Error fetching asset type forms:", error);
      throw error;
    }
  }

  /**
   * Get asset type form by asset type and operation type
   */
  static async getAssetTypeFormByOperation(
    assetTypeId: string,
    operationType: AssetOperationType
  ) {
    try {
      const assetTypeForm = await prisma.assetTypeForm.findFirst({
        where: {
          assetTypeId,
          operationType,
          isActive: true,
        },
        include: {
          form: {
            select: {
              id: true,
              name: true,
              description: true,
              sections: true,
              settings: true,
              version: true,
              isActive: true,
              createdAt: true,
              updatedAt: true,
            },
          },
        },
        orderBy: [
          { isDefault: "desc" },
          { createdAt: "desc" },
        ],
      });

      if (!assetTypeForm) {
        return null;
      }

      // Parse JSON fields
      return {
        ...assetTypeForm,
        form: {
          ...assetTypeForm.form,
          sections: JSON.parse(assetTypeForm.form.sections as string),
          settings: JSON.parse(assetTypeForm.form.settings as string),
        },
      };
    } catch (error) {
      console.error("Error fetching asset type form by operation:", error);
      throw error;
    }
  }

  /**
   * Create or update asset type form association
   */
  static async upsertAssetTypeForm(data: {
    assetTypeId: string;
    formId: string;
    operationType: AssetOperationType;
    isDefault?: boolean;
    isActive?: boolean;
    createdBy: string;
  }) {
    try {
      const { assetTypeId, formId, operationType, isDefault = false, isActive = true, createdBy } = data;

      // If setting as default, unset other defaults for this asset type and operation
      if (isDefault) {
        await prisma.assetTypeForm.updateMany({
          where: {
            assetTypeId,
            operationType,
            isDefault: true,
          },
          data: {
            isDefault: false,
          },
        });
      }

      // Check if there's already an association for this asset type and operation
      const existingAssociation = await prisma.assetTypeForm.findFirst({
        where: {
          assetTypeId,
          operationType,
        },
      });

      let assetTypeForm;

      if (existingAssociation) {
        // Update existing association
        assetTypeForm = await prisma.assetTypeForm.update({
          where: {
            id: existingAssociation.id,
          },
          data: {
            formId,
            isDefault,
            isActive,
            updatedAt: new Date(),
          },
          include: {
            form: true,
            assetType: true,
          },
        });
      } else {
        // Create new association
        assetTypeForm = await prisma.assetTypeForm.create({
          data: {
            assetTypeId,
            formId,
            operationType,
            isDefault,
            isActive,
            createdBy,
          },
          include: {
            form: true,
            assetType: true,
          },
        });
      }

      return assetTypeForm;
    } catch (error) {
      console.error("Error upserting asset type form:", error);
      throw error;
    }
  }
}
