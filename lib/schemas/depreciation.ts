import { z } from "zod";

// Depreciation method enum
export const DepreciationMethodSchema = z.enum([
  "straight_line",
  "declining_balance", 
  "double_declining_balance",
  "sum_of_years_digits",
  "units_of_production",
  "custom"
]);

// Depreciation rate schema for custom depreciation
export const DepreciationRateSchema = z.object({
  year: z.number().int().positive(),
  rate: z.number().min(0).max(1, "Rate must be between 0 and 1"),
  description: z.string().optional(),
});

// Accelerated depreciation schema
export const AcceleratedDepreciationSchema = z.object({
  enabled: z.boolean().default(false),
  bonusDepreciationRate: z.number().min(0).max(1).default(0),
  section179Limit: z.number().nonnegative().default(0),
  qualifyingAssetTypes: z.array(z.string()).default([]),
});

// Impairment settings schema
export const ImpairmentSettingsSchema = z.object({
  enabled: z.boolean().default(false),
  triggers: z.array(z.object({
    type: z.enum(["market_value_decline", "obsolescence", "damage", "regulatory_change"]),
    threshold: z.number().positive(),
    description: z.string().optional(),
  })).default([]),
  testingFrequency: z.enum(["annual", "quarterly", "monthly", "event_driven"]).default("annual"),
  recoveryMethod: z.enum(["cost_model", "revaluation_model"]).default("cost_model"),
});

// Enhanced depreciation settings with parsed JSON fields
export const EnhancedDepreciationSettingsSchema = z.object({
  id: z.string().cuid(),
  assetTypeId: z.string().cuid(),
  method: DepreciationMethodSchema,
  usefulLife: z.number().int().positive(),
  usefulLifeUnit: z.enum(["years", "months", "hours", "cycles"]).default("years"),
  salvageValue: z.number().nonnegative(),
  salvageValueType: z.enum(["fixed", "percentage"]).default("percentage"),
  startDate: z.date(),
  customRates: z.array(DepreciationRateSchema).optional(),
  acceleratedDepreciation: AcceleratedDepreciationSchema.optional(),
  impairmentSettings: ImpairmentSettingsSchema.optional(),
  isActive: z.boolean().default(true),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Depreciation schedule entry schema
export const DepreciationScheduleEntrySchema = z.object({
  year: z.number().int().positive(),
  month: z.number().int().min(1).max(12),
  depreciationAmount: z.number().nonnegative(),
  accumulatedDepreciation: z.number().nonnegative(),
  bookValue: z.number().nonnegative(),
  method: z.string(),
  isActual: z.boolean().default(false),
  units: z.number().nonnegative().optional(), // For units of production
  notes: z.string().optional(),
});

// Depreciation calculation input schema
export const DepreciationCalculationInputSchema = z.object({
  assetId: z.string().cuid(),
  purchasePrice: z.number().positive(),
  purchaseDate: z.date(),
  settings: EnhancedDepreciationSettingsSchema.omit({ 
    id: true, 
    assetTypeId: true, 
    createdAt: true, 
    updatedAt: true 
  }),
  currentDate: z.date().optional(),
});

// Depreciation calculation result schema
export const DepreciationCalculationResultSchema = z.object({
  totalDepreciation: z.number().nonnegative(),
  remainingValue: z.number().nonnegative(),
  schedule: z.array(DepreciationScheduleEntrySchema),
  metadata: z.object({
    method: DepreciationMethodSchema,
    usefulLife: z.number().positive(),
    salvageValue: z.number().nonnegative(),
    calculatedAt: z.date(),
    totalUnits: z.number().nonnegative().optional(), // For units of production
  }),
});

// Asset depreciation summary schema
export const AssetDepreciationSummarySchema = z.object({
  assetId: z.string().cuid(),
  assetName: z.string(),
  purchasePrice: z.number().positive(),
  currentBookValue: z.number().nonnegative(),
  accumulatedDepreciation: z.number().nonnegative(),
  remainingDepreciableAmount: z.number().nonnegative(),
  depreciationMethod: DepreciationMethodSchema,
  usefulLife: z.number().positive(),
  remainingUsefulLife: z.number().nonnegative(),
  salvageValue: z.number().nonnegative(),
  lastCalculatedAt: z.date(),
  nextDepreciationDate: z.date().optional(),
});

// Bulk depreciation calculation schema
export const BulkDepreciationCalculationSchema = z.object({
  assetIds: z.array(z.string().cuid()).min(1, "At least one asset ID required"),
  recalculate: z.boolean().default(false),
  effectiveDate: z.date().optional(),
});

// Depreciation report filter schema
export const DepreciationReportFilterSchema = z.object({
  assetIds: z.array(z.string().cuid()).optional(),
  assetTypeIds: z.array(z.string().cuid()).optional(),
  categories: z.array(z.string()).optional(),
  departments: z.array(z.string()).optional(),
  locations: z.array(z.string()).optional(),
  methods: z.array(DepreciationMethodSchema).optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  includeFullyDepreciated: z.boolean().default(true),
  includeDisposed: z.boolean().default(false),
});

// Depreciation adjustment schema
export const DepreciationAdjustmentSchema = z.object({
  id: z.string().cuid(),
  assetId: z.string().cuid(),
  adjustmentType: z.enum([
    "impairment",
    "revaluation", 
    "useful_life_change",
    "method_change",
    "salvage_value_change"
  ]),
  previousValue: z.number().nonnegative(),
  newValue: z.number().nonnegative(),
  adjustmentAmount: z.number(),
  effectiveDate: z.date(),
  reason: z.string().min(1, "Reason is required"),
  approvedBy: z.string().cuid().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// API request/response schemas
export const CalculateDepreciationRequestSchema = z.object({
  assetId: z.string().cuid(),
  recalculate: z.boolean().default(false),
});

export const CalculateDepreciationResponseSchema = z.object({
  success: z.boolean(),
  data: DepreciationCalculationResultSchema.optional(),
  error: z.string().optional(),
});

export const GetAssetDepreciationRequestSchema = z.object({
  assetId: z.string().cuid(),
  includeSchedule: z.boolean().default(true),
  includeHistory: z.boolean().default(false),
});

export const GetAssetDepreciationResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    asset: z.object({
      id: z.string(),
      name: z.string(),
      purchasePrice: z.number(),
      purchaseDate: z.date(),
      assetType: z.object({
        id: z.string(),
        name: z.string(),
        depreciationSettings: EnhancedDepreciationSettingsSchema.nullable(),
      }).nullable(),
    }),
    summary: AssetDepreciationSummarySchema,
    schedule: z.array(DepreciationScheduleEntrySchema).optional(),
    adjustments: z.array(DepreciationAdjustmentSchema).optional(),
  }).optional(),
  error: z.string().optional(),
});

// Type exports for TypeScript
export type DepreciationMethod = z.infer<typeof DepreciationMethodSchema>;
export type DepreciationRate = z.infer<typeof DepreciationRateSchema>;
export type AcceleratedDepreciation = z.infer<typeof AcceleratedDepreciationSchema>;
export type ImpairmentSettings = z.infer<typeof ImpairmentSettingsSchema>;
export type EnhancedDepreciationSettings = z.infer<typeof EnhancedDepreciationSettingsSchema>;
export type DepreciationScheduleEntry = z.infer<typeof DepreciationScheduleEntrySchema>;
export type DepreciationCalculationInput = z.infer<typeof DepreciationCalculationInputSchema>;
export type DepreciationCalculationResult = z.infer<typeof DepreciationCalculationResultSchema>;
export type AssetDepreciationSummary = z.infer<typeof AssetDepreciationSummarySchema>;
export type BulkDepreciationCalculation = z.infer<typeof BulkDepreciationCalculationSchema>;
export type DepreciationReportFilter = z.infer<typeof DepreciationReportFilterSchema>;
export type DepreciationAdjustment = z.infer<typeof DepreciationAdjustmentSchema>;
