import {
  DepreciationCalculationInput,
  DepreciationCalculationResult,
  DepreciationScheduleEntry,
  DepreciationRate,
  EnhancedDepreciationSettings
} from "@/lib/schemas/depreciation";
import { DepreciationSettings as PrismaDepreciationSettings } from "@prisma/client";
import prisma from "@/lib/prisma";

// Interface for Prisma-based calculation input
export interface PrismaDepreciationCalculationInput {
  assetId: string;
  purchasePrice: number;
  purchaseDate: Date;
  settings: PrismaDepreciationSettings;
  currentDate?: Date;
}

export class DepreciationEngine {
  /**
   * Calculate depreciation schedule for an asset using enhanced settings
   */
  static async calculateDepreciation(
    input: DepreciationCalculationInput
  ): Promise<DepreciationCalculationResult> {
    const { purchasePrice, purchaseDate, settings, currentDate = new Date() } = input;

    const schedule: DepreciationScheduleEntry[] = [];

    const salvageValue = settings.salvageValueType === "percentage"
      ? purchasePrice * (settings.salvageValue / 100)
      : settings.salvageValue;

    const depreciableAmount = purchasePrice - salvageValue;

    switch (settings.method) {
      case "straight_line":
        schedule.push(...this.calculateStraightLine(
          depreciableAmount,
          settings.usefulLife,
          purchaseDate,
          currentDate,
          purchasePrice,
          salvageValue
        ));
        break;

      case "declining_balance":
      case "double_declining_balance":
        schedule.push(...this.calculateDecliningBalance(
          purchasePrice,
          settings.usefulLife,
          purchaseDate,
          currentDate,
          salvageValue,
          settings.method === "double_declining_balance" ? 2 : 1.5
        ));
        break;

      case "sum_of_years_digits":
        schedule.push(...this.calculateSumOfYearsDigits(
          depreciableAmount,
          settings.usefulLife,
          purchaseDate,
          currentDate,
          purchasePrice,
          salvageValue
        ));
        break;

      case "units_of_production":
        // This would require additional usage data
        throw new Error("Units of production method requires usage data");

      case "custom":
        if (settings.customRates) {
          schedule.push(...this.calculateCustomRates(
            purchasePrice,
            settings.customRates,
            purchaseDate,
            currentDate,
            salvageValue
          ));
        }
        break;

      default:
        throw new Error(`Unsupported depreciation method: ${settings.method}`);
    }

    // Calculate totals
    const totalDepreciation = schedule.reduce((sum, entry) => sum + entry.depreciationAmount, 0);
    const remainingValue = purchasePrice - totalDepreciation;

    return {
      totalDepreciation,
      remainingValue,
      schedule,
      metadata: {
        method: settings.method,
        usefulLife: settings.usefulLife,
        salvageValue,
        calculatedAt: currentDate,
      },
    };
  }

  /**
   * Calculate depreciation schedule for an asset using Prisma settings
   */
  static async calculateDepreciationFromPrisma(
    input: PrismaDepreciationCalculationInput
  ): Promise<DepreciationCalculationResult> {
    const { settings: prismaSettings, ...rest } = input;

    // Parse JSON fields from Prisma settings
    const enhancedSettings: EnhancedDepreciationSettings = {
      id: prismaSettings.id,
      assetTypeId: prismaSettings.assetTypeId,
      method: prismaSettings.method as any,
      usefulLife: prismaSettings.usefulLife,
      usefulLifeUnit: prismaSettings.usefulLifeUnit as any,
      salvageValue: prismaSettings.salvageValue,
      salvageValueType: prismaSettings.salvageValueType as any,
      startDate: prismaSettings.startDate,
      customRates: prismaSettings.customRates
        ? JSON.parse(prismaSettings.customRates)
        : undefined,
      acceleratedDepreciation: prismaSettings.acceleratedDepreciation
        ? JSON.parse(prismaSettings.acceleratedDepreciation)
        : undefined,
      impairmentSettings: prismaSettings.impairmentSettings
        ? JSON.parse(prismaSettings.impairmentSettings)
        : undefined,
      isActive: prismaSettings.isActive,
      createdAt: prismaSettings.createdAt,
      updatedAt: prismaSettings.updatedAt,
    };

    return this.calculateDepreciation({
      ...rest,
      settings: enhancedSettings,
    });
  }
  
  /**
   * Straight-line depreciation calculation
   */
  private static calculateStraightLine(
    depreciableAmount: number,
    usefulLife: number,
    purchaseDate: Date,
    currentDate: Date,
    purchasePrice: number,
    salvageValue: number
  ): DepreciationScheduleEntry[] {
    const schedule: DepreciationScheduleEntry[] = [];
    const monthlyDepreciation = depreciableAmount / (usefulLife * 12);
    
    let accumulatedDepreciation = 0;
    let bookValue = purchasePrice;
    
    const startDate = new Date(purchaseDate);
    const endDate = new Date(startDate);
    endDate.setFullYear(endDate.getFullYear() + usefulLife);
    
    for (let date = new Date(startDate); date <= endDate && date <= currentDate; date.setMonth(date.getMonth() + 1)) {
      const depreciationAmount = Math.min(monthlyDepreciation, bookValue - salvageValue);
      
      if (depreciationAmount <= 0) break;
      
      accumulatedDepreciation += depreciationAmount;
      bookValue -= depreciationAmount;
      
      schedule.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        depreciationAmount,
        accumulatedDepreciation,
        bookValue,
        method: "straight_line",
        isActual: date <= currentDate,
      });
    }
    
    return schedule;
  }
  
  /**
   * Declining balance depreciation calculation
   */
  private static calculateDecliningBalance(
    purchasePrice: number,
    usefulLife: number,
    purchaseDate: Date,
    currentDate: Date,
    salvageValue: number,
    multiplier: number
  ): DepreciationScheduleEntry[] {
    const schedule: DepreciationScheduleEntry[] = [];
    const annualRate = multiplier / usefulLife;
    const monthlyRate = annualRate / 12;
    
    let accumulatedDepreciation = 0;
    let bookValue = purchasePrice;
    
    const startDate = new Date(purchaseDate);
    const endDate = new Date(startDate);
    endDate.setFullYear(endDate.getFullYear() + usefulLife);
    
    for (let date = new Date(startDate); date <= endDate && date <= currentDate; date.setMonth(date.getMonth() + 1)) {
      const depreciationAmount = Math.min(
        bookValue * monthlyRate,
        bookValue - salvageValue
      );
      
      if (depreciationAmount <= 0) break;
      
      accumulatedDepreciation += depreciationAmount;
      bookValue -= depreciationAmount;
      
      schedule.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        depreciationAmount,
        accumulatedDepreciation,
        bookValue,
        method: multiplier === 2 ? "double_declining_balance" : "declining_balance",
        isActual: date <= currentDate,
      });
    }
    
    return schedule;
  }
  
  /**
   * Sum of years digits depreciation calculation
   */
  private static calculateSumOfYearsDigits(
    depreciableAmount: number,
    usefulLife: number,
    purchaseDate: Date,
    currentDate: Date,
    purchasePrice: number,
    salvageValue: number
  ): DepreciationScheduleEntry[] {
    const schedule: DepreciationScheduleEntry[] = [];
    const sumOfYears = (usefulLife * (usefulLife + 1)) / 2;
    
    let accumulatedDepreciation = 0;
    let bookValue = purchasePrice;
    
    const startDate = new Date(purchaseDate);
    
    for (let year = 1; year <= usefulLife; year++) {
      const yearlyDepreciation = (depreciableAmount * (usefulLife - year + 1)) / sumOfYears;
      const monthlyDepreciation = yearlyDepreciation / 12;
      
      for (let month = 1; month <= 12; month++) {
        const date = new Date(startDate);
        date.setFullYear(date.getFullYear() + year - 1);
        date.setMonth(date.getMonth() + month - 1);
        
        if (date > currentDate) break;
        
        const depreciationAmount = Math.min(monthlyDepreciation, bookValue - salvageValue);
        
        if (depreciationAmount <= 0) break;
        
        accumulatedDepreciation += depreciationAmount;
        bookValue -= depreciationAmount;
        
        schedule.push({
          year: date.getFullYear(),
          month: date.getMonth() + 1,
          depreciationAmount,
          accumulatedDepreciation,
          bookValue,
          method: "sum_of_years_digits",
          isActual: date <= currentDate,
        });
      }
    }
    
    return schedule;
  }
  
  /**
   * Custom rates depreciation calculation
   */
  private static calculateCustomRates(
    purchasePrice: number,
    customRates: DepreciationRate[],
    purchaseDate: Date,
    currentDate: Date,
    salvageValue: number
  ): DepreciationScheduleEntry[] {
    const schedule: DepreciationScheduleEntry[] = [];
    
    let accumulatedDepreciation = 0;
    let bookValue = purchasePrice;
    
    const startDate = new Date(purchaseDate);
    
    for (const rateEntry of customRates) {
      const yearlyDepreciation = purchasePrice * rateEntry.rate;
      const monthlyDepreciation = yearlyDepreciation / 12;
      
      for (let month = 1; month <= 12; month++) {
        const date = new Date(startDate);
        date.setFullYear(date.getFullYear() + rateEntry.year - 1);
        date.setMonth(date.getMonth() + month - 1);
        
        if (date > currentDate) break;
        
        const depreciationAmount = Math.min(monthlyDepreciation, bookValue - salvageValue);
        
        if (depreciationAmount <= 0) break;
        
        accumulatedDepreciation += depreciationAmount;
        bookValue -= depreciationAmount;
        
        schedule.push({
          year: date.getFullYear(),
          month: date.getMonth() + 1,
          depreciationAmount,
          accumulatedDepreciation,
          bookValue,
          method: "custom",
          isActual: date <= currentDate,
        });
      }
    }
    
    return schedule;
  }
  
  /**
   * Save depreciation schedule to database
   */
  static async saveDepreciationSchedule(
    assetId: string,
    schedule: DepreciationScheduleEntry[]
  ): Promise<void> {
    // Delete existing schedule
    await prisma.depreciationSchedule.deleteMany({
      where: { assetId },
    });
    
    // Insert new schedule
    await prisma.depreciationSchedule.createMany({
      data: schedule.map(entry => ({
        assetId,
        year: entry.year,
        month: entry.month,
        depreciationAmount: entry.depreciationAmount,
        accumulatedDepreciation: entry.accumulatedDepreciation,
        bookValue: entry.bookValue,
        method: entry.method,
        isActual: entry.isActual,
      })),
    });
  }
  
  /**
   * Get current book value for an asset
   */
  static async getCurrentBookValue(assetId: string): Promise<number> {
    const latestEntry = await prisma.depreciationSchedule.findFirst({
      where: { assetId },
      orderBy: [
        { year: 'desc' },
        { month: 'desc' },
      ],
    });
    
    if (!latestEntry) {
      // If no depreciation schedule exists, get the purchase price
      const asset = await prisma.asset.findUnique({
        where: { id: assetId },
        select: { purchasePrice: true },
      });
      
      return asset?.purchasePrice || 0;
    }
    
    return latestEntry.bookValue;
  }
  
  /**
   * Recalculate depreciation for all assets of a specific type
   */
  static async recalculateForAssetType(assetTypeId: string): Promise<void> {
    const assets = await prisma.asset.findMany({
      where: { assetTypeId },
      include: {
        assetType: {
          include: {
            depreciationSettings: true,
          },
        },
      },
    });
    
    for (const asset of assets) {
      if (asset.assetType?.depreciationSettings) {
        const result = await this.calculateDepreciationFromPrisma({
          assetId: asset.id,
          purchasePrice: asset.purchasePrice,
          purchaseDate: asset.purchaseDate,
          settings: asset.assetType.depreciationSettings,
        });

        await this.saveDepreciationSchedule(asset.id, result.schedule);
      }
    }
  }
}