import { Asset } from "@prisma/client";
import { z } from "zod";
import { AssetCreateSchema, AssetUpdateSchema } from "@/lib/schemas/validation";

// Form data interface for creating assets
export interface AssetFormData {
  name: string;
  category: string;
  serialNumber: string;
  location: string;
  purchasePrice: number;
  purchaseDate: string; // Keep as string for HTML date input
  department: string;
  status: string;
  assetImages: string[];
  assetTypeId: string; // Add assetTypeId
}

// Interface for API request when creating assets
export interface AssetCreateRequest {
  name: string;
  category: string;
  serialNumber: string | null;
  location: string;
  purchasePrice: number;
  purchaseDate: Date;
  department: string | null;
  status: string;
  assetImages: string[];
  assetTypeId: string; // Add assetTypeId
}

// Interface for API request when updating assets
export interface AssetUpdateRequest {
  name?: string;
  category?: string;
  serialNumber?: string | null;
  location?: string;
  purchasePrice?: number;
  purchaseDate?: Date;
  department?: string;
  status?: string;
  assetImages?: string[];
  assetTypeId?: string;
}

// Extended asset type with optional relations
export interface AssetWithRelations extends Asset {
  maintenances?: any[];
  transfers?: any[];
  disposals?: any[];
  depreciations?: any[];
  leases?: any[];
}

// Asset status enum
export enum AssetStatus {
  ACTIVE = "active",
  MAINTENANCE = "maintenance",
  DISPOSED = "disposed",
}

// Asset category enum (you can extend this based on your needs)
export enum AssetCategory {
  IT_EQUIPMENT = "IT Equipment",
  MACHINERY = "Machinery",
  FURNITURE = "Furniture",
  SECURITY = "Security",
  VEHICLES = "Vehicles",
}

// Form validation schema using Zod
export const AssetFormDataSchema = z.object({
  name: z.string().min(1, "Asset name is required"),
  category: z.string().min(1, "Category is required"),
  serialNumber: z.string(),
  location: z.string().min(1, "Location is required"),
  purchasePrice: z.number().positive("Purchase price must be positive"),
  purchaseDate: z.string().min(1, "Purchase date is required"),
  department: z.string(),
  status: z.string(),
  assetImages: z.array(z.string()),
  assetTypeId: z.string().min(1, "Asset type is required"),
});

export const REQUIRED_FIELDS = ["name", "category", "location", "purchasePrice", "purchaseDate", "assetTypeId"] as const;

export type RequiredAssetField = typeof REQUIRED_FIELDS[number];

// Utility function to validate asset form data using Zod
export function validateAssetFormData(data: AssetFormData): { isValid: boolean; errors: string[]; missingFields: RequiredAssetField[] } {
  try {
    AssetFormDataSchema.parse(data);
    return {
      isValid: true,
      errors: [],
      missingFields: [],
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingFields: RequiredAssetField[] = [];
      const errors: string[] = [];

      error.errors.forEach((err) => {
        const field = err.path[0] as string;
        errors.push(err.message);

        if (REQUIRED_FIELDS.includes(field as RequiredAssetField)) {
          missingFields.push(field as RequiredAssetField);
        }
      });

      return {
        isValid: false,
        errors,
        missingFields,
      };
    }

    return {
      isValid: false,
      errors: ["Validation failed"],
      missingFields: [],
    };
  }
}

// Utility function to transform form data to API request format with validation
export function transformAssetFormData(formData: AssetFormData): AssetCreateRequest {
  // Validate form data first
  const validation = validateAssetFormData(formData);
  if (!validation.isValid) {
    throw new Error(`Validation failed: ${validation.errors.join(", ")}`);
  }

  const transformedData = {
    name: formData.name.trim(),
    category: formData.category,
    serialNumber: formData.serialNumber ? formData.serialNumber.trim() : null,
    location: formData.location.trim(),
    purchasePrice: Number(formData.purchasePrice),
    purchaseDate: new Date(formData.purchaseDate),
    department: formData.department.trim() || null,
    status: formData.status as "active" | "maintenance" | "disposed",
    assetImages: formData.assetImages,
    assetTypeId: formData.assetTypeId,
  } as AssetCreateRequest;

  // Validate against Zod schema for API request
  try {
    return AssetCreateSchema.parse(transformedData);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`API validation failed: ${error.errors.map(e => e.message).join(", ")}`);
    }
    throw error;
  }
}