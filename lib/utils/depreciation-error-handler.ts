import { NextResponse } from "next/server";
import { ZodError } from "zod";
import { Prisma } from "@prisma/client";

// Custom error types for depreciation operations
export class DepreciationError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400,
    public details?: any
  ) {
    super(message);
    this.name = "DepreciationError";
  }
}

export class AssetNotFoundError extends DepreciationError {
  constructor(assetId: string) {
    super(
      `Asset with ID ${assetId} not found`,
      "ASSET_NOT_FOUND",
      404
    );
  }
}

export class AssetTypeNotFoundError extends DepreciationError {
  constructor(assetTypeId: string) {
    super(
      `Asset type with ID ${assetTypeId} not found`,
      "ASSET_TYPE_NOT_FOUND",
      404
    );
  }
}

export class DepreciationSettingsNotFoundError extends DepreciationError {
  constructor(assetTypeId: string) {
    super(
      `No depreciation settings found for asset type ${assetTypeId}`,
      "DEPRECIATION_SETTINGS_NOT_FOUND",
      400
    );
  }
}

export class InvalidDepreciationMethodError extends DepreciationError {
  constructor(method: string) {
    super(
      `Invalid depreciation method: ${method}`,
      "INVALID_DEPRECIATION_METHOD",
      400
    );
  }
}

export class CalculationError extends DepreciationError {
  constructor(message: string, details?: any) {
    super(
      `Depreciation calculation failed: ${message}`,
      "CALCULATION_ERROR",
      500,
      details
    );
  }
}

export class ValidationError extends DepreciationError {
  constructor(message: string, details?: any) {
    super(
      `Validation failed: ${message}`,
      "VALIDATION_ERROR",
      400,
      details
    );
  }
}

export class DatabaseError extends DepreciationError {
  constructor(message: string, details?: any) {
    super(
      `Database operation failed: ${message}`,
      "DATABASE_ERROR",
      500,
      details
    );
  }
}

// Error handler function for API routes
export function handleDepreciationError(error: unknown): NextResponse {
  console.error("Depreciation operation error:", error);

  // Handle custom depreciation errors
  if (error instanceof DepreciationError) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
        details: error.details,
      },
      { status: error.statusCode }
    );
  }

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    const validationErrors = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code,
    }));

    return NextResponse.json(
      {
        success: false,
        error: "Validation failed",
        code: "VALIDATION_ERROR",
        details: validationErrors,
      },
      { status: 400 }
    );
  }

  // Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        return NextResponse.json(
          {
            success: false,
            error: "A record with this data already exists",
            code: "DUPLICATE_RECORD",
            details: { constraint: error.meta?.target },
          },
          { status: 409 }
        );
      case 'P2025':
        return NextResponse.json(
          {
            success: false,
            error: "Record not found",
            code: "RECORD_NOT_FOUND",
          },
          { status: 404 }
        );
      case 'P2003':
        return NextResponse.json(
          {
            success: false,
            error: "Foreign key constraint failed",
            code: "FOREIGN_KEY_CONSTRAINT",
            details: { field: error.meta?.field_name },
          },
          { status: 400 }
        );
      default:
        return NextResponse.json(
          {
            success: false,
            error: "Database operation failed",
            code: "DATABASE_ERROR",
            details: { prismaCode: error.code },
          },
          { status: 500 }
        );
    }
  }

  // Handle Prisma client initialization errors
  if (error instanceof Prisma.PrismaClientInitializationError) {
    return NextResponse.json(
      {
        success: false,
        error: "Database connection failed",
        code: "DATABASE_CONNECTION_ERROR",
      },
      { status: 503 }
    );
  }

  // Handle generic errors
  if (error instanceof Error) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: "INTERNAL_ERROR",
      },
      { status: 500 }
    );
  }

  // Handle unknown errors
  return NextResponse.json(
    {
      success: false,
      error: "An unknown error occurred",
      code: "UNKNOWN_ERROR",
    },
    { status: 500 }
  );
}

// Validation helper functions
export function validateAssetId(assetId: string): void {
  if (!assetId) {
    throw new ValidationError("Asset ID is required");
  }
  
  if (!/^[a-z0-9]+$/i.test(assetId)) {
    throw new ValidationError("Invalid asset ID format");
  }
}

export function validateAssetTypeId(assetTypeId: string): void {
  if (!assetTypeId) {
    throw new ValidationError("Asset type ID is required");
  }
  
  if (!/^[a-z0-9]+$/i.test(assetTypeId)) {
    throw new ValidationError("Invalid asset type ID format");
  }
}

export function validateDepreciationSettings(settings: any): void {
  if (!settings) {
    throw new ValidationError("Depreciation settings are required");
  }

  if (!settings.method) {
    throw new ValidationError("Depreciation method is required");
  }

  if (!settings.usefulLife || settings.usefulLife <= 0) {
    throw new ValidationError("Useful life must be greater than 0");
  }

  if (settings.salvageValue < 0) {
    throw new ValidationError("Salvage value cannot be negative");
  }

  if (settings.salvageValueType === 'percentage' && settings.salvageValue > 100) {
    throw new ValidationError("Salvage value percentage cannot exceed 100%");
  }
}

export function validatePurchaseData(purchasePrice: number, purchaseDate: Date): void {
  if (!purchasePrice || purchasePrice <= 0) {
    throw new ValidationError("Purchase price must be greater than 0");
  }

  if (!purchaseDate) {
    throw new ValidationError("Purchase date is required");
  }

  if (purchaseDate > new Date()) {
    throw new ValidationError("Purchase date cannot be in the future");
  }

  // Check if purchase date is too far in the past (e.g., more than 100 years)
  const hundredYearsAgo = new Date();
  hundredYearsAgo.setFullYear(hundredYearsAgo.getFullYear() - 100);
  
  if (purchaseDate < hundredYearsAgo) {
    throw new ValidationError("Purchase date is too far in the past");
  }
}

// Async validation helpers
export async function validateAssetExists(assetId: string, prisma: any): Promise<void> {
  const asset = await prisma.asset.findUnique({
    where: { id: assetId },
    select: { id: true },
  });

  if (!asset) {
    throw new AssetNotFoundError(assetId);
  }
}

export async function validateAssetTypeExists(assetTypeId: string, prisma: any): Promise<void> {
  const assetType = await prisma.assetType.findUnique({
    where: { id: assetTypeId },
    select: { id: true },
  });

  if (!assetType) {
    throw new AssetTypeNotFoundError(assetTypeId);
  }
}

export async function validateDepreciationSettingsExist(
  assetTypeId: string, 
  prisma: any
): Promise<void> {
  const settings = await prisma.depreciationSettings.findUnique({
    where: { assetTypeId },
    select: { id: true },
  });

  if (!settings) {
    throw new DepreciationSettingsNotFoundError(assetTypeId);
  }
}

// Rate limiting helper (for future implementation)
export function createRateLimitError(limit: number, window: string): DepreciationError {
  return new DepreciationError(
    `Rate limit exceeded. Maximum ${limit} requests per ${window}`,
    "RATE_LIMIT_EXCEEDED",
    429
  );
}

// Success response helper
export function createSuccessResponse(data: any, message?: string): NextResponse {
  return NextResponse.json({
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  });
}

// Paginated success response helper
export function createPaginatedResponse(
  data: any[], 
  pagination: any, 
  total: number,
  message?: string
): NextResponse {
  const totalPages = Math.ceil(total / pagination.limit);
  
  return NextResponse.json({
    success: true,
    data,
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrev: pagination.page > 1,
    },
    message,
    timestamp: new Date().toISOString(),
  });
}
