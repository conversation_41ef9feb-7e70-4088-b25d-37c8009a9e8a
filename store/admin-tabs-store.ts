import { create } from 'zustand';
import { LucideIcon } from 'lucide-react';

export interface TabItem {
  id: string;
  label: string;
  icon?: LucideIcon;
  disabled?: boolean;
  badge?: string | number;
  content?: React.ReactNode;
}

export interface TabsConfig {
  tabs: TabItem[];
  defaultTab?: string;
  variant?: 'default' | 'pills' | 'underline' | 'cards';
  size?: 'sm' | 'md' | 'lg';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  tabsListClassName?: string;
  tabsTriggerClassName?: string;
  tabsContentClassName?: string;
}

interface AdminTabsState {
  activeTab?: string;
  tabsConfig?: TabsConfig;
  isLoading?: boolean;
}

interface AdminTabsActions {
  setTabsConfig: (config: TabsConfig) => void;
  setActiveTab: (tabId: string) => void;
  updateTab: (tabId: string, updates: Partial<TabItem>) => void;
  addTab: (tab: TabItem, position?: number) => void;
  removeTab: (tabId: string) => void;
  setLoading: (loading: boolean) => void;
  resetTabs: () => void;
}

type AdminTabsStore = AdminTabsState & AdminTabsActions;

const initialState: AdminTabsState = {
  activeTab: undefined,
  tabsConfig: undefined,
  isLoading: false,
};

export const useAdminTabsStore = create<AdminTabsStore>((set, get) => ({
  ...initialState,
  
  setTabsConfig: (config) => {
    const activeTab = config.defaultTab || config.tabs[0]?.id;
    set({ 
      tabsConfig: config,
      activeTab: activeTab
    });
  },
  
  setActiveTab: (tabId) => {
    const { tabsConfig } = get();
    if (tabsConfig?.tabs.find(tab => tab.id === tabId && !tab.disabled)) {
      set({ activeTab: tabId });
    }
  },
  
  updateTab: (tabId, updates) => {
    const { tabsConfig } = get();
    if (!tabsConfig) return;
    
    const updatedTabs = tabsConfig.tabs.map(tab =>
      tab.id === tabId ? { ...tab, ...updates } : tab
    );
    
    set({
      tabsConfig: {
        ...tabsConfig,
        tabs: updatedTabs
      }
    });
  },
  
  addTab: (tab, position) => {
    const { tabsConfig } = get();
    if (!tabsConfig) return;
    
    const newTabs = [...tabsConfig.tabs];
    if (position !== undefined && position >= 0 && position <= newTabs.length) {
      newTabs.splice(position, 0, tab);
    } else {
      newTabs.push(tab);
    }
    
    set({
      tabsConfig: {
        ...tabsConfig,
        tabs: newTabs
      }
    });
  },
  
  removeTab: (tabId) => {
    const { tabsConfig, activeTab } = get();
    if (!tabsConfig) return;
    
    const updatedTabs = tabsConfig.tabs.filter(tab => tab.id !== tabId);
    let newActiveTab = activeTab;
    
    // If removing the active tab, switch to the first available tab
    if (activeTab === tabId && updatedTabs.length > 0) {
      newActiveTab = updatedTabs[0].id;
    }
    
    set({
      tabsConfig: {
        ...tabsConfig,
        tabs: updatedTabs
      },
      activeTab: newActiveTab
    });
  },
  
  setLoading: (loading) => set({ isLoading: loading }),
  
  resetTabs: () => set(initialState),
}));
