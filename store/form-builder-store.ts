import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { FormDefinition } from '@/components/form-builder';
import { AssetOperationType } from '@/lib/types/asset-type-forms';

interface FormBuilderState {
  // Current forms for each operation type
  forms: Partial<Record<AssetOperationType, FormDefinition>>;
  
  // Current editing state
  selectedOperation: AssetOperationType | null;
  isFormBuilderOpen: boolean;
  isSaving: boolean;
  saveStatus: 'idle' | 'saving' | 'saved' | 'error';
  
  // Actions
  setForms: (forms: Partial<Record<AssetOperationType, FormDefinition>>) => void;
  updateForm: (operationType: AssetOperationType, form: FormDefinition) => void;
  setSelectedOperation: (operation: AssetOperationType | null) => void;
  setFormBuilderOpen: (open: boolean) => void;
  setSaving: (saving: boolean) => void;
  setSaveStatus: (status: 'idle' | 'saving' | 'saved' | 'error') => void;
  
  // Form operations
  initializeForm: (operationType: AssetOperationType, assetTypeId: string, assetTypeName: string) => void;
  resetForms: () => void;
}

export const useFormBuilderStore = create<FormBuilderState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        forms: {},
        selectedOperation: null,
        isFormBuilderOpen: false,
        isSaving: false,
        saveStatus: 'idle',

        // Actions
        setForms: (forms) => set({ forms }),
        
        updateForm: (operationType, form) => set((state) => ({
          forms: {
            ...state.forms,
            [operationType]: form,
          },
        })),
        
        setSelectedOperation: (operation) => set({ selectedOperation: operation }),
        
        setFormBuilderOpen: (open) => set({ isFormBuilderOpen: open }),
        
        setSaving: (saving) => set({ isSaving: saving }),
        
        setSaveStatus: (status) => set({ saveStatus: status }),
        
        initializeForm: (operationType, assetTypeId, assetTypeName) => {
          const { forms } = get();
          
          if (!forms[operationType]) {
            const defaultForm: FormDefinition = {
              id: `form-${assetTypeId}-${operationType}-${Date.now()}`,
              name: `${assetTypeName} - ${operationType}`,
              description: `Form for ${operationType} operation`,
              sections: [
                {
                  id: `section-${Date.now()}`,
                  title: "Basic Information",
                  description: "Required information for this operation",
                  columns: 1,
                  fields: [], // Will be populated by the component
                },
              ],
              settings: {
                submitButtonText: "Submit",
                cancelButtonText: "Cancel",
                showProgressBar: true,
                allowSaveAsDraft: true,
                confirmOnCancel: true,
                layout: "standard",
                labelPosition: "top",
              },
            };
            
            set((state) => ({
              forms: {
                ...state.forms,
                [operationType]: defaultForm,
              },
            }));
          }
        },
        
        resetForms: () => set({ 
          forms: {},
          selectedOperation: null,
          isFormBuilderOpen: false,
          isSaving: false,
          saveStatus: 'idle',
        }),
      }),
      {
        name: 'form-builder-storage',
        partialize: (state) => ({ forms: state.forms }),
      }
    ),
    {
      name: 'form-builder-store',
    }
  )
);