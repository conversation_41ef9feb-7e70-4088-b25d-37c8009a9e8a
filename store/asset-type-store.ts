import { create } from "zustand"
import { devtools, persist } from "zustand/middleware"
import type {
  AssetType,
  AssetCategory,
  CustomField,
  LifecycleStage,
  MaintenanceSchedule,
  DepreciationSettings,
  AssetTypeTemplate,
  AssetTypeMetrics,
  AssetTypeValidationResult,
} from "@/lib/modules/asset-types/types"

interface AssetTypeFilters {
  category?: string
  isActive?: boolean
  search?: string
}

interface AssetTypeState {
  // Data
  assetTypes: AssetType[]
  categories: AssetCategory[]
  templates: AssetTypeTemplate[]
  metrics: AssetTypeMetrics | null
  
  // UI State
  loading: boolean
  error: string | null
  selectedAssetType: AssetType | null
  filters: AssetTypeFilters
  
  // Actions
  setAssetTypes: (assetTypes: AssetType[]) => void
  setCategories: (categories: AssetCategory[]) => void
  setTemplates: (templates: AssetTypeTemplate[]) => void
  setMetrics: (metrics: AssetTypeMetrics) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setSelectedAssetType: (assetType: AssetType | null) => void
  setFilters: (filters: AssetTypeFilters) => void
  
  // CRUD Operations
  addAssetType: (assetType: AssetType) => void
  updateAssetType: (id: string, updates: Partial<AssetType>) => void
  removeAssetType: (id: string) => void
  
  // Category Operations
  addCategory: (category: AssetCategory) => void
  updateCategory: (id: string, updates: Partial<AssetCategory>) => void
  removeCategory: (id: string) => void
  
  // Template Operations
  addTemplate: (template: AssetTypeTemplate) => void
  updateTemplate: (id: string, updates: Partial<AssetTypeTemplate>) => void
  removeTemplate: (id: string) => void
  
  // Utility Actions
  clearError: () => void
  reset: () => void
  getFilteredAssetTypes: () => AssetType[]
  getCategoriesByParent: (parentId?: string) => AssetCategory[]
}

const initialState = {
  assetTypes: [],
  categories: [],
  templates: [],
  metrics: null,
  loading: false,
  error: null,
  selectedAssetType: null,
  filters: {},
}

export const useAssetTypeStore = create<AssetTypeState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        // Setters
        setAssetTypes: (assetTypes) => set({ assetTypes }),
        setCategories: (categories) => set({ categories }),
        setTemplates: (templates) => set({ templates }),
        setMetrics: (metrics) => set({ metrics }),
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),
        setSelectedAssetType: (selectedAssetType) => set({ selectedAssetType }),
        setFilters: (filters) => set({ filters }),
        
        // CRUD Operations
        addAssetType: (assetType) =>
          set((state) => ({
            assetTypes: [...state.assetTypes, assetType],
          })),
          
        updateAssetType: (id, updates) =>
          set((state) => ({
            assetTypes: state.assetTypes.map((at) =>
              at.id === id ? { ...at, ...updates } : at
            ),
            selectedAssetType:
              state.selectedAssetType?.id === id
                ? { ...state.selectedAssetType, ...updates }
                : state.selectedAssetType,
          })),
          
        removeAssetType: (id) =>
          set((state) => ({
            assetTypes: state.assetTypes.filter((at) => at.id !== id),
            selectedAssetType:
              state.selectedAssetType?.id === id ? null : state.selectedAssetType,
          })),
          
        // Category Operations
        addCategory: (category) =>
          set((state) => ({
            categories: [...state.categories, category],
          })),
          
        updateCategory: (id, updates) =>
          set((state) => ({
            categories: state.categories.map((cat) =>
              cat.id === id ? { ...cat, ...updates } : cat
            ),
          })),
          
        removeCategory: (id) =>
          set((state) => ({
            categories: state.categories.filter((cat) => cat.id !== id),
          })),
          
        // Template Operations
        addTemplate: (template) =>
          set((state) => ({
            templates: [...state.templates, template],
          })),
          
        updateTemplate: (id, updates) =>
          set((state) => ({
            templates: state.templates.map((tpl) =>
              tpl.id === id ? { ...tpl, ...updates } : tpl
            ),
          })),
          
        removeTemplate: (id) =>
          set((state) => ({
            templates: state.templates.filter((tpl) => tpl.id !== id),
          })),
          
        // Utility Actions
        clearError: () => set({ error: null }),
        
        reset: () => set(initialState),
        
        getFilteredAssetTypes: () => {
          const { assetTypes, filters } = get()
          let filtered = assetTypes
          
          if (filters.category) {
            filtered = filtered.filter((at) => at.category.id === filters.category)
          }
          
          if (filters.isActive !== undefined) {
            filtered = filtered.filter((at) => at.isActive === filters.isActive)
          }
          
          if (filters.search) {
            const search = filters.search.toLowerCase()
            filtered = filtered.filter(
              (at) =>
                at.name.toLowerCase().includes(search) ||
                at.code.toLowerCase().includes(search) ||
                at.description.toLowerCase().includes(search) ||
                at.tags.some((tag) => tag.toLowerCase().includes(search))
            )
          }
          
          return filtered.sort((a, b) => a.name.localeCompare(b.name))
        },
        
        getCategoriesByParent: (parentId) => {
          const { categories } = get()
          return categories
            .filter((cat) => cat.parentId === parentId)
            .sort((a, b) => a.name.localeCompare(b.name))
        },
      }),
      {
        name: "asset-type-store",
        partialize: (state) => ({
          // Only persist essential data, not UI state
          assetTypes: state.assetTypes,
          categories: state.categories,
          templates: state.templates,
          filters: state.filters,
        }),
      }
    ),
    {
      name: "asset-type-store",
    }
  )
)

// Selectors for better performance
export const useAssetTypes = () => useAssetTypeStore((state) => state.assetTypes)
export const useCategories = () => useAssetTypeStore((state) => state.categories)
export const useTemplates = () => useAssetTypeStore((state) => state.templates)
export const useAssetTypeMetrics = () => useAssetTypeStore((state) => state.metrics)
export const useAssetTypeLoading = () => useAssetTypeStore((state) => state.loading)
export const useAssetTypeError = () => useAssetTypeStore((state) => state.error)
export const useSelectedAssetType = () => useAssetTypeStore((state) => state.selectedAssetType)
export const useAssetTypeFilters = () => useAssetTypeStore((state) => state.filters)
export const useFilteredAssetTypes = () => useAssetTypeStore((state) => state.getFilteredAssetTypes())