import { useEffect, useCallback, useRef } from 'react';
import { useAppHeaderStore, type HeaderTabItem } from '@/store/app-header-store';
import { useAdminTabsStore, type TabsConfig, type TabItem } from '@/store/admin-tabs-store';

/**
 * Custom hook for managing admin page tabs
 * Provides a clean API for setting up and managing dynamic tabs
 * Follows the same pattern as useAdminHeader for consistency
 */
export function useAdminTabs(config: TabsConfig | (() => TabsConfig)) {
  // Set tabs configuration on mount
  useEffect(() => {
    const { setTabsConfig, resetTabs } = useAdminTabsStore.getState();
    const tabsConfig = typeof config === 'function' ? config() : config;
    setTabsConfig(tabsConfig);

    // Cleanup on unmount
    return () => {
      resetTabs();
    };
  }, [config]);

  // Utility functions for dynamic tab updates
  const setActiveTab = useCallback((tabId: string) => {
    const { setActiveTab } = useAdminTabsStore.getState();
    setActiveTab(tabId);
  }, []);

  const updateTab = useCallback((tabId: string, updates: Partial<TabItem>) => {
    const { updateTab } = useAdminTabsStore.getState();
    updateTab(tabId, updates);
  }, []);

  const addTab = useCallback((tab: TabItem, position?: number) => {
    const { addTab } = useAdminTabsStore.getState();
    addTab(tab, position);
  }, []);

  const removeTab = useCallback((tabId: string) => {
    const { removeTab } = useAdminTabsStore.getState();
    removeTab(tabId);
  }, []);

  const setTabsLoading = useCallback((loading: boolean) => {
    const { setLoading } = useAdminTabsStore.getState();
    setLoading(loading);
  }, []);

  const updateTabsConfig = useCallback((newConfig: Partial<TabsConfig>) => {
    const { tabsConfig, setTabsConfig } = useAdminTabsStore.getState();
    if (tabsConfig) {
      setTabsConfig({ ...tabsConfig, ...newConfig });
    }
  }, []);

  return {
    setActiveTab,
    updateTab,
    addTab,
    removeTab,
    setTabsLoading,
    updateTabsConfig,
  };
}

/**
 * Hook for pages that need dynamic tab updates based on state
 * Provides more granular control over tab management
 */
export function useDynamicAdminTabs() {
  const setTabs = useCallback((config: TabsConfig) => {
    const { setTabsConfig } = useAdminTabsStore.getState();
    setTabsConfig(config);
  }, []);

  const resetTabs = useCallback(() => {
    const { resetTabs } = useAdminTabsStore.getState();
    resetTabs();
  }, []);

  const setActiveTab = useCallback((tabId: string) => {
    const { setActiveTab } = useAdminTabsStore.getState();
    setActiveTab(tabId);
  }, []);

  const updateTab = useCallback((tabId: string, updates: Partial<TabItem>) => {
    const { updateTab } = useAdminTabsStore.getState();
    updateTab(tabId, updates);
  }, []);

  const addTab = useCallback((tab: TabItem, position?: number) => {
    const { addTab } = useAdminTabsStore.getState();
    addTab(tab, position);
  }, []);

  const removeTab = useCallback((tabId: string) => {
    const { removeTab } = useAdminTabsStore.getState();
    removeTab(tabId);
  }, []);

  const setTabsLoading = useCallback((loading: boolean) => {
    const { setLoading } = useAdminTabsStore.getState();
    setLoading(loading);
  }, []);

  const updateTabsConfig = useCallback((newConfig: Partial<TabsConfig>) => {
    const { tabsConfig, setTabsConfig } = useAdminTabsStore.getState();
    if (tabsConfig) {
      setTabsConfig({ ...tabsConfig, ...newConfig });
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      const { resetTabs } = useAdminTabsStore.getState();
      resetTabs();
    };
  }, []);

  return {
    setTabs,
    resetTabs,
    setActiveTab,
    updateTab,
    addTab,
    removeTab,
    setTabsLoading,
    updateTabsConfig,
  };
}

/**
 * Hook to access current tabs state
 * Useful for components that need to read tab state without managing it
 */
export function useAdminTabsState() {
  const { activeTab, tabsConfig, isLoading } = useAdminTabsStore();

  return {
    activeTab,
    tabsConfig,
    isLoading,
    tabs: tabsConfig?.tabs || [],
  };
}

/**
 * Hook for header-integrated tabs
 * Sets tabs directly in the header component for a seamless look
 */
export function useHeaderTabs(tabs: HeaderTabItem[], defaultTab?: string) {
  const activeTab = useAppHeaderStore(state => state.activeTab);

  // Use a ref to store the tabs to avoid dependency issues
  const tabsRef = useRef(tabs);
  const defaultTabRef = useRef(defaultTab);

  // Update refs when props change
  tabsRef.current = tabs;
  defaultTabRef.current = defaultTab;

  useEffect(() => {
    const { setTabs, hideTabs } = useAppHeaderStore.getState();
    setTabs(tabsRef.current, defaultTabRef.current);

    // Cleanup on unmount
    return () => {
      hideTabs();
    };
  }, []); // Empty dependency array to run only once

  return {
    activeTab,
  };
}

/**
 * Hook for dynamic header tabs
 * Provides more control over header tab management
 */
export function useDynamicHeaderTabs() {
  const { setTabs, setActiveTab, updateTab, hideTabs, activeTab } = useAppHeaderStore();

  const setHeaderTabs = useCallback((tabs: HeaderTabItem[], defaultTab?: string) => {
    setTabs(tabs, defaultTab);
  }, [setTabs]);

  const changeActiveTab = useCallback((tabId: string) => {
    setActiveTab(tabId);
  }, [setActiveTab]);

  const updateHeaderTab = useCallback((tabId: string, updates: Partial<HeaderTabItem>) => {
    updateTab(tabId, updates);
  }, [updateTab]);

  const hideHeaderTabs = useCallback(() => {
    hideTabs();
  }, [hideTabs]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      hideTabs();
    };
  }, [hideTabs]);

  return {
    setHeaderTabs,
    changeActiveTab,
    updateHeaderTab,
    hideHeaderTabs,
    activeTab,
  };
}
