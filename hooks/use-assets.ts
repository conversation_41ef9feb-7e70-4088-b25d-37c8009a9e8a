import { useState, useEffect, useCallback } from "react";
import { Asset } from "@prisma/client";
import { AssetCreateInput, AssetUpdateInput, AssetWithRelations } from "@/lib/services/asset-service";

interface UseAssetsOptions {
  initialFetch?: boolean;
  includeRelations?: boolean;
}

interface AssetFilters {
  category?: string;
  status?: string;
  department?: string;
  location?: string;
  search?: string;
}

interface AssetStatistics {
  totalAssets: number;
  activeAssets: number;
  maintenanceAssets: number;
  disposedAssets: number;
  assetsByCategory: Array<{ category: string; count: number }>;
}

export function useAssets(options: UseAssetsOptions = {}) {
  const { initialFetch = true, includeRelations = false } = options;
  
  const [assets, setAssets] = useState<AssetWithRelations[]>([]);
  const [asset, setAsset] = useState<AssetWithRelations | null>(null);
  const [statistics, setStatistics] = useState<AssetStatistics | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all assets with optional filters
  const fetchAssets = useCallback(async (filters?: AssetFilters) => {
    setIsLoading(true);
    setError(null);
    
    try {
      let url = "/api/assets";
      const params = new URLSearchParams();
      
      if (includeRelations) {
        params.append("includeRelations", "true");
      }
      
      if (filters) {
        if (filters.category) params.append("category", filters.category);
        if (filters.status) params.append("status", filters.status);
        if (filters.department) params.append("department", filters.department);
        if (filters.location) params.append("location", filters.location);
        if (filters.search) params.append("search", filters.search);
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch assets: ${response.statusText}`);
      }
      
      const result = await response.json();

      // Handle different response formats from the API
      if (result.success && result.data) {
        const assets = Array.isArray(result.data) ? result.data : [];
        setAssets(assets);
        return assets;
      } else {
        // Fallback for direct array response (legacy)
        const assets = Array.isArray(result) ? result : [];
        setAssets(assets);
        return assets;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      console.error("Error fetching assets:", err);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [includeRelations]);

  // Fetch a single asset by ID
  const fetchAssetById = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const url = `/api/assets/${id}${includeRelations ? "?includeRelations=true" : ""}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch asset: ${response.statusText}`);
      }
      
      const result = await response.json();
      const data = result.success && result.data ? result.data : result;
      setAsset(data);
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      console.error(`Error fetching asset ${id}:`, err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [includeRelations]);

  // Create a new asset
  const createAsset = useCallback(async (data: AssetCreateInput) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch("/api/assets", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create asset: ${response.statusText}`);
      }
      
      const result = await response.json();
      const createdAsset = result.success && result.data ? result.data : result;
      setAssets((prevAssets) => [createdAsset, ...prevAssets]);
      return createdAsset;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      console.error("Error creating asset:", err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update an existing asset
  const updateAsset = useCallback(async (id: string, data: AssetUpdateInput) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/assets/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update asset: ${response.statusText}`);
      }
      
      const result = await response.json();
      const updatedAsset = result.success && result.data ? result.data : result;

      setAssets((prevAssets) =>
        prevAssets.map((asset) => (asset.id === id ? updatedAsset : asset))
      );

      if (asset && asset.id === id) {
        setAsset(updatedAsset);
      }

      return updatedAsset;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      console.error(`Error updating asset ${id}:`, err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [asset]);

  // Delete an asset
  const deleteAsset = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/assets/${id}`, {
        method: "DELETE",
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete asset: ${response.statusText}`);
      }
      
      setAssets((prevAssets) => prevAssets.filter((asset) => asset.id !== id));
      
      if (asset && asset.id === id) {
        setAsset(null);
      }
      
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      console.error(`Error deleting asset ${id}:`, err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [asset]);

  // Fetch asset statistics
  const fetchStatistics = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch("/api/assets/statistics");
      
      if (!response.ok) {
        throw new Error(`Failed to fetch asset statistics: ${response.statusText}`);
      }
      
      const result = await response.json();
      const data = result.success && result.data ? result.data : result;
      setStatistics(data);
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      console.error("Error fetching asset statistics:", err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    if (initialFetch) {
      fetchAssets();
    }
  }, [fetchAssets, initialFetch]);

  return {
    assets,
    asset,
    statistics,
    isLoading,
    error,
    fetchAssets,
    fetchAssetById,
    createAsset,
    updateAsset,
    deleteAsset,
    fetchStatistics,
  };
}