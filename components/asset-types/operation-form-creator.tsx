"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  Plus, 
  FileText, 
  Settings, 
  Wrench, 
  TrendingDown, 
  GitBranch,
  AlertCircle,
  CheckCircle,
  Clock,
  Activity,
  Layers
} from "lucide-react";
import { AssetOperationType, ASSET_OPERATION_CONFIGS } from "@/lib/types/asset-type-forms";
import { FormDefinition } from "@/components/form-builder";
import { CustomField } from "@/lib/modules/asset-types/types";
import { toast } from "@/components/ui/use-toast";

interface OperationFormCreatorProps {
  isOpen: boolean;
  onClose: () => void;
  assetTypeId: string;
  assetTypeName: string;
  operationType?: AssetOperationType;
  customFields?: CustomField[];
  onFormCreated?: (form: FormDefinition, operationType: AssetOperationType) => void;
}

export function OperationFormCreator({
  isOpen,
  onClose,
  assetTypeId,
  assetTypeName,
  operationType,
  customFields = [],
  onFormCreated,
}: OperationFormCreatorProps) {
  const [selectedOperation, setSelectedOperation] = useState<AssetOperationType | null>(
    operationType || null
  );
  const [formName, setFormName] = useState("");
  const [formDescription, setFormDescription] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const getOperationIcon = (operationType: AssetOperationType) => {
    const iconMap: Record<string, React.ElementType> = {
      "asset.create": Plus,
      "asset.update": Settings,
      "asset.transfer": GitBranch,
      "asset.disposal": TrendingDown,
      "maintenance.log": Wrench,
      "maintenance.schedule": Clock,
      "depreciation.calculate": TrendingDown,
      "inventory.audit": CheckCircle,
      "lifecycle.transition": Activity,
      "requisition.asset": FileText,
      "requisition.approve": CheckCircle,
      "requisition.fulfill": Activity,
    };
    
    const IconComponent = iconMap[operationType] || FileText;
    return <IconComponent className="h-4 w-4" />;
  };

  const handleCreateForm = async () => {
    if (!selectedOperation || !formName.trim()) {
      toast({
        title: "Validation Error",
        description: "Please select an operation type and provide a form name.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsCreating(true);

      const config = ASSET_OPERATION_CONFIGS[selectedOperation];
      
      // Create the form definition
      const newForm: FormDefinition = {
        id: `form-${assetTypeId}-${selectedOperation}-${Date.now()}`,
        name: formName,
        description: formDescription || config.description,
        sections: [
          {
            id: `section-${Date.now()}`,
            title: "Basic Information",
            description: "Required information for this operation",
            columns: 1,
            fields: config.requiredFields.slice(0, 3), // Start with first 3 required fields
          },
        ],
        settings: {
          ...config.defaultSettings,
          layout: "standard",
          labelPosition: "top",
          showProgressBar: true,
          allowSaveAsDraft: true,
          confirmOnCancel: true,
        },
        version: 1,
      };

      // Save the form definition
      const formResponse = await fetch("/api/form-definitions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newForm.name,
          description: newForm.description,
          sections: newForm.sections,
          settings: newForm.settings,
          createdBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (!formResponse.ok) {
        throw new Error("Failed to create form definition");
      }

      const savedForm = await formResponse.json();

      // Associate the form with the asset type
      const associationResponse = await fetch("/api/asset-type-forms", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          assetTypeId,
          formId: savedForm.id,
          operationType: selectedOperation,
          isDefault: true,
          createdBy: "current-user", // TODO: Get from auth context
        }),
      });

      if (!associationResponse.ok) {
        throw new Error("Failed to associate form with asset type");
      }

      toast({
        title: "Success",
        description: `Form "${formName}" created successfully for ${config.displayName}.`,
      });

      if (onFormCreated) {
        onFormCreated(savedForm, selectedOperation);
      }

      // Reset form and close
      setFormName("");
      setFormDescription("");
      setSelectedOperation(operationType || null);
      onClose();

    } catch (error) {
      console.error("Error creating form:", error);
      toast({
        title: "Error",
        description: "Failed to create form. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    setFormName("");
    setFormDescription("");
    setSelectedOperation(operationType || null);
    onClose();
  };

  // Generate default form name when operation is selected
  React.useEffect(() => {
    if (selectedOperation && !formName) {
      const config = ASSET_OPERATION_CONFIGS[selectedOperation];
      setFormName(`${assetTypeName} - ${config.displayName}`);
    }
  }, [selectedOperation, assetTypeName, formName]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Operation Form</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Operation Selection */}
          {!operationType && (
            <div>
              <Label className="text-base font-medium">Select Operation Type</Label>
              <p className="text-sm text-muted-foreground mb-4">
                Choose the asset operation this form will be used for.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {Object.entries(ASSET_OPERATION_CONFIGS).map(([opType, config]) => (
                  <Card
                    key={opType}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedOperation === opType
                        ? "ring-2 ring-primary border-primary"
                        : "hover:border-primary/50"
                    }`}
                    onClick={() => setSelectedOperation(opType as AssetOperationType)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div
                          className="p-2 rounded-lg"
                          style={{ 
                            backgroundColor: `${config.color}20`, 
                            color: config.color 
                          }}
                        >
                          {getOperationIcon(opType as AssetOperationType)}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{config.displayName}</h4>
                          <p className="text-xs text-muted-foreground mt-1">
                            {config.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Selected Operation Info */}
          {selectedOperation && (
            <div>
              <Label className="text-base font-medium">Selected Operation</Label>
              <Card className="mt-2">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div
                      className="p-2 rounded-lg"
                      style={{ 
                        backgroundColor: `${ASSET_OPERATION_CONFIGS[selectedOperation].color}20`, 
                        color: ASSET_OPERATION_CONFIGS[selectedOperation].color 
                      }}
                    >
                      {getOperationIcon(selectedOperation)}
                    </div>
                    <div>
                      <h4 className="font-medium">{ASSET_OPERATION_CONFIGS[selectedOperation].displayName}</h4>
                      <p className="text-sm text-muted-foreground">
                        {ASSET_OPERATION_CONFIGS[selectedOperation].description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <Separator />

          {/* Form Details */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Form Details</Label>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="form-name">Form Name *</Label>
                <Input
                  id="form-name"
                  value={formName}
                  onChange={(e) => setFormName(e.target.value)}
                  placeholder="Enter form name"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="form-description">Description</Label>
                <Textarea
                  id="form-description"
                  value={formDescription}
                  onChange={(e) => setFormDescription(e.target.value)}
                  placeholder="Enter form description (optional)"
                  className="mt-1"
                  rows={3}
                />
              </div>
            </div>
          </div>

          {/* Form Preview Info */}
          {selectedOperation && (
            <div>
              <Label className="text-base font-medium">Form Configuration Preview</Label>
              <Card className="mt-2">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Required Fields:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {ASSET_OPERATION_CONFIGS[selectedOperation].requiredFields.map((field) => (
                            <Badge key={field} variant="secondary" className="text-xs">
                              {field}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium">Optional Fields:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {ASSET_OPERATION_CONFIGS[selectedOperation].optionalFields.slice(0, 3).map((field) => (
                            <Badge key={field} variant="outline" className="text-xs">
                              {field}
                            </Badge>
                          ))}
                          {ASSET_OPERATION_CONFIGS[selectedOperation].optionalFields.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{ASSET_OPERATION_CONFIGS[selectedOperation].optionalFields.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleCreateForm}
              disabled={!selectedOperation || !formName.trim() || isCreating}
            >
              {isCreating ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Form
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
