"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  Plus, 
  Edit, 
  Eye, 
  FileText, 
  Settings, 
  Wrench, 
  TrendingDown, 
  GitBranch,
  AlertCircle,
  CheckCircle,
  Clock,
  Activity,
  Layers
} from "lucide-react";
import { AssetOperationType, ASSET_OPERATION_CONFIGS } from "@/lib/types/asset-type-forms";
import { CustomField } from "@/lib/modules/asset-types/types";
import { OperationFormCreator } from "./operation-form-creator";
import { FormDefinition } from "@/components/form-builder";

interface AssetTypeForm {
  id: string;
  assetTypeId: string;
  formId: string;
  operationType: AssetOperationType;
  version: number;
  isDefault: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  form: {
    id: string;
    name: string;
    description?: string;
    sections: any[];
    settings: any;
  };
}

interface AssetTypeFormsGridProps {
  assetTypeId: string;
  assetTypeName: string;
  customFields?: CustomField[];
  onCreateForm?: (operationType: AssetOperationType) => void;
  onEditForm?: (form: AssetTypeForm) => void;
  onPreviewForm?: (form: AssetTypeForm) => void;
  className?: string;
}

export function AssetTypeFormsGrid({
  assetTypeId,
  assetTypeName,
  customFields = [],
  onCreateForm,
  onEditForm,
  onPreviewForm,
  className,
}: AssetTypeFormsGridProps) {
  const router = useRouter();
  const [forms, setForms] = useState<AssetTypeForm[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFormCreatorOpen, setIsFormCreatorOpen] = useState(false);
  const [selectedOperationType, setSelectedOperationType] = useState<AssetOperationType | null>(null);

  useEffect(() => {
    loadForms();
  }, [assetTypeId]);

  const loadForms = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/asset-type-forms?assetTypeId=${assetTypeId}&getAllForms=true`);
      if (!response.ok) {
        throw new Error("Failed to load forms");
      }

      const formsData = await response.json();
      setForms(formsData);
    } catch (error) {
      console.error("Error loading forms:", error);
      setError("Failed to load forms. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const getOperationIcon = (operationType: AssetOperationType) => {
    const iconMap: Record<string, React.ElementType> = {
      "asset.create": Plus,
      "asset.update": Settings,
      "asset.transfer": GitBranch,
      "asset.disposal": TrendingDown,
      "maintenance.log": Wrench,
      "maintenance.schedule": Clock,
      "depreciation.calculate": TrendingDown,
      "inventory.audit": CheckCircle,
      "lifecycle.transition": Activity,
      "requisition.asset": FileText,
      "requisition.approve": CheckCircle,
      "requisition.fulfill": Activity,
    };
    
    const IconComponent = iconMap[operationType] || FileText;
    return <IconComponent className="h-5 w-5" />;
  };

  const getOperationColor = (operationType: AssetOperationType) => {
    const config = ASSET_OPERATION_CONFIGS[operationType];
    return config?.color || "#6B7280";
  };

  const handleCreateForm = (operationType: AssetOperationType) => {
    setSelectedOperationType(operationType);
    setIsFormCreatorOpen(true);
  };

  const handleEditForm = (form: AssetTypeForm) => {
    if (onEditForm) {
      onEditForm(form);
    } else {
      // Default behavior: navigate to form editor
      router.push(`/admin/asset-types/${assetTypeId}/forms/${form.id}/edit`);
    }
  };

  const handlePreviewForm = (form: AssetTypeForm) => {
    if (onPreviewForm) {
      onPreviewForm(form);
    } else {
      // Default behavior: could open a preview modal or navigate to preview page
      console.log("Preview form:", form);
    }
  };

  const handleFormCreated = async (form: FormDefinition, operationType: AssetOperationType) => {
    // Reload forms to show the new form
    await loadForms();

    if (onCreateForm) {
      onCreateForm(operationType);
    }
  };

  const renderOperationCard = (operationType: AssetOperationType) => {
    const config = ASSET_OPERATION_CONFIGS[operationType];
    const operationForms = forms.filter(f => f.operationType === operationType);
    const activeForm = operationForms.find(f => f.isActive && f.isDefault) || operationForms[0];

    return (
      <Card key={operationType} className="h-full">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-3">
            <div 
              className="p-2 rounded-lg"
              style={{ 
                backgroundColor: `${config.color}20`, 
                color: config.color 
              }}
            >
              {getOperationIcon(operationType)}
            </div>
            <div className="flex-1">
              <h3 className="text-base font-semibold">{config.displayName}</h3>
              <p className="text-sm text-muted-foreground font-normal">
                {config.description}
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="pt-0">
          {activeForm ? (
            <div className="space-y-3">
              {/* Form Info */}
              <div className="p-3 bg-muted/30 rounded-lg">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{activeForm.form.name}</h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      {activeForm.form.description || "No description"}
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      {activeForm.isDefault && (
                        <Badge variant="secondary" className="text-xs">Default</Badge>
                      )}
                      {activeForm.isActive ? (
                        <Badge variant="default" className="text-xs">Active</Badge>
                      ) : (
                        <Badge variant="outline" className="text-xs">Inactive</Badge>
                      )}
                      <Badge variant="outline" className="text-xs">
                        v{activeForm.version}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* Form Stats */}
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="text-center p-2 bg-muted/20 rounded">
                  <div className="font-medium">{activeForm.form.sections?.length || 0}</div>
                  <div className="text-muted-foreground">Sections</div>
                </div>
                <div className="text-center p-2 bg-muted/20 rounded">
                  <div className="font-medium">{operationForms.length}</div>
                  <div className="text-muted-foreground">Version{operationForms.length !== 1 ? 's' : ''}</div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditForm(activeForm)}
                  className="flex-1"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePreviewForm(activeForm)}
                >
                  <Eye className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {/* No Form State */}
              <div className="text-center py-6">
                <div className="text-muted-foreground mb-3">
                  <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No form configured</p>
                </div>
                <Button
                  size="sm"
                  onClick={() => handleCreateForm(operationType)}
                  className="w-full"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Create Form
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-sm text-muted-foreground">Loading forms...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Group operations by category for better organization
  const coreOperations: AssetOperationType[] = ["asset.create", "asset.update", "asset.transfer", "asset.disposal"];
  const maintenanceOperations: AssetOperationType[] = ["maintenance.log", "maintenance.schedule"];
  const otherOperations: AssetOperationType[] = ["depreciation.calculate", "inventory.audit", "lifecycle.transition"];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">Operation Forms</h2>
          <p className="text-sm text-muted-foreground">
            Configure forms for different asset operations
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">{forms.length} form{forms.length !== 1 ? 's' : ''} configured</Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setSelectedOperationType(null);
              setIsFormCreatorOpen(true);
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Form
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/admin/asset-types/${assetTypeId}/forms`)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Advanced Config
          </Button>
        </div>
      </div>

      {/* Core Operations */}
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
          <Layers className="h-4 w-4" />
          Core Asset Operations
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {coreOperations.map(renderOperationCard)}
        </div>
      </div>

      <Separator />

      {/* Maintenance Operations */}
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
          <Wrench className="h-4 w-4" />
          Maintenance Operations
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {maintenanceOperations.map(renderOperationCard)}
        </div>
      </div>

      <Separator />

      {/* Other Operations */}
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
          <Activity className="h-4 w-4" />
          Other Operations
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {otherOperations.map(renderOperationCard)}
        </div>
      </div>

      {/* Form Creator Modal */}
      <OperationFormCreator
        isOpen={isFormCreatorOpen}
        onClose={() => {
          setIsFormCreatorOpen(false);
          setSelectedOperationType(null);
        }}
        assetTypeId={assetTypeId}
        assetTypeName={assetTypeName}
        operationType={selectedOperationType || undefined}
        customFields={customFields}
        onFormCreated={handleFormCreated}
      />
    </div>
  );
}
