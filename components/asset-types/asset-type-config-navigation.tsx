"use client";

import React from "react";
import Link from "next/link";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Settings, 
  GitBranch, 
  Wrench, 
  TrendingDown, 
  Workflow,
  ArrowRight,
  ExternalLink
} from "lucide-react";

interface AssetTypeConfigNavigationProps {
  assetTypeId: string;
  assetTypeName: string;
  stats?: {
    forms: number;
    customFields: number;
    lifecycleStages: number;
    maintenanceSchedules: number;
    hasDepreciationSettings: boolean;
    workflows: number;
  };
}

const CONFIG_SECTIONS = [
  {
    id: "forms",
    title: "Forms Configuration",
    description: "Configure dynamic forms for asset operations",
    icon: FileText,
    path: "/forms",
    color: "text-blue-500",
    bgColor: "bg-blue-50",
  },
  {
    id: "fields",
    title: "Custom Fields",
    description: "Define custom fields for additional data capture",
    icon: Settings,
    path: "/fields",
    color: "text-green-500",
    bgColor: "bg-green-50",
  },
  {
    id: "lifecycle",
    title: "Lifecycle Stages",
    description: "Configure asset lifecycle progression stages",
    icon: GitBranch,
    path: "/lifecycle",
    color: "text-purple-500",
    bgColor: "bg-purple-50",
  },
  {
    id: "maintenance",
    title: "Maintenance Schedules",
    description: "Set up automated maintenance schedules",
    icon: Wrench,
    path: "/maintenance",
    color: "text-orange-500",
    bgColor: "bg-orange-50",
  },
  {
    id: "depreciation",
    title: "Depreciation Settings",
    description: "Configure depreciation methods and parameters",
    icon: TrendingDown,
    path: "/depreciation",
    color: "text-red-500",
    bgColor: "bg-red-50",
  },
  {
    id: "workflows",
    title: "Workflows",
    description: "Configure automated workflows and triggers",
    icon: Workflow,
    path: "/workflows",
    color: "text-indigo-500",
    bgColor: "bg-indigo-50",
  },
];

export function AssetTypeConfigNavigation({
  assetTypeId,
  assetTypeName,
  stats
}: AssetTypeConfigNavigationProps) {

  const getStatValue = (sectionId: string) => {
    if (!stats) return null;
    
    switch (sectionId) {
      case "forms":
        return stats.forms;
      case "fields":
        return stats.customFields;
      case "lifecycle":
        return stats.lifecycleStages;
      case "maintenance":
        return stats.maintenanceSchedules;
      case "depreciation":
        return stats.hasDepreciationSettings ? "Configured" : "Not configured";
      case "workflows":
        return stats.workflows;
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Configuration Options for {assetTypeName}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {CONFIG_SECTIONS.map((section) => {
            const IconComponent = section.icon;
            const statValue = getStatValue(section.id);
            
            return (
              <Link
                key={section.id}
                href={`/admin/asset-types/${assetTypeId}${section.path}`}
                className={`block p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer ${section.bgColor}`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className={`p-2 rounded-lg bg-white`}>
                    <IconComponent className={`h-5 w-5 ${section.color}`} />
                  </div>
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-medium text-sm">{section.title}</h3>
                  <p className="text-xs text-muted-foreground">{section.description}</p>
                  
                  {statValue !== null && (
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {typeof statValue === "number" 
                          ? `${statValue} configured`
                          : statValue
                        }
                      </Badge>
                    </div>
                  )}
                </div>
                
                <div className="mt-3 pt-3 border-t border-white/50">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-between text-xs"
                    asChild
                  >
                    <Link href={`/admin/asset-types/${assetTypeId}${section.path}`}>
                      Configure
                      <ExternalLink className="h-3 w-3" />
                    </Link>
                  </Button>
                </div>
              </Link>
            );
          })}
        </div>
        
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Settings className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Quick Actions</span>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              Export Configuration
            </Button>
            <Button variant="outline" size="sm">
              Import Configuration
            </Button>
            <Button variant="outline" size="sm">
              Clone from Template
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
