"use client"

import * as React from "react"
import { useAppHeaderStore } from "@/store/app-header-store"
import { cn } from "@/lib/utils"

interface TabContentItem {
  id: string;
  content: React.ReactNode;
}

interface HeaderTabContentProps {
  tabContents: TabContentItem[];
  className?: string;
}

/**
 * Component that renders content based on the active header tab
 * Works seamlessly with the header-integrated tabs
 */
export function HeaderTabContent({ tabContents, className }: HeaderTabContentProps) {
  const activeTab = useAppHeaderStore(state => state.activeTab);

  const activeContent = tabContents.find(item => item.id === activeTab);

  if (!activeContent) {
    return null;
  }

  return (
    <div className={cn("w-full", className)}>
      {activeContent.content}
    </div>
  );
}

/**
 * Individual tab content wrapper for better organization
 */
export function TabContent({ 
  children, 
  className 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <div className={cn("space-y-6", className)}>
      {children}
    </div>
  );
}
