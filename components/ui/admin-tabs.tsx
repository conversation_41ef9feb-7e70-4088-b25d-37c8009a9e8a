"use client"

import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useAdminTabsState } from "@/hooks/use-admin-tabs"
import { useAdminTabsStore } from "@/store/admin-tabs-store"
import { LucideIcon } from "lucide-react"

// Enhanced AdminTabs component that uses the store
const AdminTabs = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Root>
>(({ className, ...props }, ref) => {
  const { activeTab, tabsConfig } = useAdminTabsState();
  
  return (
    <TabsPrimitive.Root
      ref={ref}
      value={activeTab}
      orientation={tabsConfig?.orientation || "horizontal"}
      className={cn("w-full", className)}
      {...props}
    />
  );
});
AdminTabs.displayName = "AdminTabs";

// Enhanced AdminTabsList with improved styling
const AdminTabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => {
  const { tabsConfig, isLoading } = useAdminTabsState();
  
  const getVariantStyles = (variant: string = 'default') => {
    switch (variant) {
      case 'pills':
        return "inline-flex h-12 items-center justify-start rounded-lg bg-muted/50 p-1 text-muted-foreground gap-1";
      case 'underline':
        return "inline-flex h-12 items-center justify-start border-b border-border bg-transparent p-0 text-muted-foreground";
      case 'cards':
        return "inline-flex h-14 items-center justify-start bg-background p-1 text-muted-foreground gap-2 border-b";
      default:
        return "inline-flex h-11 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground";
    }
  };

  const getSizeStyles = (size: string = 'md') => {
    switch (size) {
      case 'sm':
        return "h-9";
      case 'lg':
        return "h-14";
      default:
        return "h-11";
    }
  };

  if (isLoading) {
    return (
      <div className={cn(getVariantStyles(tabsConfig?.variant), getSizeStyles(tabsConfig?.size), className)}>
        <Skeleton className="h-8 w-20" />
        <Skeleton className="h-8 w-24" />
        <Skeleton className="h-8 w-18" />
      </div>
    );
  }

  return (
    <TabsPrimitive.List
      ref={ref}
      className={cn(
        getVariantStyles(tabsConfig?.variant),
        getSizeStyles(tabsConfig?.size),
        tabsConfig?.tabsListClassName,
        className
      )}
      {...props}
    />
  );
});
AdminTabsList.displayName = "AdminTabsList";

// Enhanced AdminTabsTrigger with icons, badges, and improved styling
const AdminTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> & {
    icon?: LucideIcon;
    badge?: string | number;
  }
>(({ className, children, icon: Icon, badge, ...props }, ref) => {
  const { tabsConfig } = useAdminTabsState();
  
  const getVariantStyles = (variant: string = 'default') => {
    switch (variant) {
      case 'pills':
        return "inline-flex items-center justify-center whitespace-nowrap rounded-md px-4 py-2 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm hover:bg-background/50";
      case 'underline':
        return "inline-flex items-center justify-center whitespace-nowrap px-4 py-3 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:text-foreground hover:text-foreground/80";
      case 'cards':
        return "inline-flex items-center justify-center whitespace-nowrap rounded-lg px-6 py-3 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-card data-[state=active]:text-card-foreground data-[state=active]:shadow-md data-[state=active]:border border-border hover:bg-muted/50";
      default:
        return "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm hover:bg-background/50";
    }
  };

  return (
    <TabsPrimitive.Trigger
      ref={ref}
      className={cn(
        getVariantStyles(tabsConfig?.variant),
        tabsConfig?.tabsTriggerClassName,
        className
      )}
      {...props}
    >
      <div className="flex items-center gap-2">
        {Icon && <Icon className="h-4 w-4" />}
        <span>{children}</span>
        {badge && (
          <Badge 
            variant="secondary" 
            className="ml-1 h-5 min-w-[20px] px-1.5 text-xs"
          >
            {badge}
          </Badge>
        )}
      </div>
    </TabsPrimitive.Trigger>
  );
});
AdminTabsTrigger.displayName = "AdminTabsTrigger";

// Enhanced AdminTabsContent
const AdminTabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => {
  const { tabsConfig } = useAdminTabsState();
  
  return (
    <TabsPrimitive.Content
      ref={ref}
      className={cn(
        "mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        tabsConfig?.tabsContentClassName,
        className
      )}
      {...props}
    />
  );
});
AdminTabsContent.displayName = "AdminTabsContent";

// Comprehensive AdminTabsRenderer that automatically renders tabs from store
const AdminTabsRenderer: React.FC<{
  className?: string;
  onTabChange?: (tabId: string) => void;
}> = ({ className, onTabChange }) => {
  const { activeTab, tabsConfig, isLoading } = useAdminTabsState();

  if (!tabsConfig || tabsConfig.tabs.length === 0) {
    return null;
  }

  const handleTabChange = (value: string) => {
    const { setActiveTab } = useAdminTabsStore.getState();
    setActiveTab(value);
    onTabChange?.(value);
  };

  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="flex space-x-1">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-18" />
        </div>
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  return (
    <AdminTabs
      value={activeTab}
      onValueChange={handleTabChange}
      className={cn("space-y-4", className)}
    >
      <AdminTabsList>
        {tabsConfig.tabs.map((tab) => (
          <AdminTabsTrigger
            key={tab.id}
            value={tab.id}
            disabled={tab.disabled}
            icon={tab.icon}
            badge={tab.badge}
          >
            {tab.label}
          </AdminTabsTrigger>
        ))}
      </AdminTabsList>

      {tabsConfig.tabs.map((tab) => (
        <AdminTabsContent key={tab.id} value={tab.id}>
          {tab.content}
        </AdminTabsContent>
      ))}
    </AdminTabs>
  );
};

export {
  AdminTabs,
  AdminTabsList,
  AdminTabsTrigger,
  AdminTabsContent,
  AdminTabsRenderer
};
