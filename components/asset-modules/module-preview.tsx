"use client";

import React, { useState, use<PERSON>emo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Eye, 
  Play, 
  RotateCcw, 
  Download, 
  Code, 
  Layout,
  Table,
  Grid,
  List,
  AlertCircle,
  CheckCircle,
  Info,
  Smartphone,
  Tablet,
  Monitor
} from "lucide-react";
import { AssetModule, ModuleField } from "@/lib/types/asset-modules";

interface ModulePreviewProps {
  module: AssetModule;
}

export function ModulePreview({ module }: ModulePreviewProps) {
  const [previewMode, setPreviewMode] = useState<'form' | 'display' | 'mobile'>('form');
  const [testData, setTestData] = useState<Record<string, any>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [deviceView, setDeviceView] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  // Generate test data for preview
  const generateTestData = () => {
    const newTestData: Record<string, any> = {};
    
    module.fields.forEach(field => {
      switch (field.type) {
        case 'text':
          newTestData[field.name] = `Sample ${field.label}`;
          break;
        case 'number':
          newTestData[field.name] = Math.floor(Math.random() * 100);
          break;
        case 'email':
          newTestData[field.name] = '<EMAIL>';
          break;
        case 'url':
          newTestData[field.name] = 'https://example.com';
          break;
        case 'phone':
          newTestData[field.name] = '+****************';
          break;
        case 'date':
          newTestData[field.name] = new Date().toISOString().split('T')[0];
          break;
        case 'boolean':
          newTestData[field.name] = Math.random() > 0.5;
          break;
        case 'select':
          newTestData[field.name] = 'Option 1';
          break;
        case 'currency':
          newTestData[field.name] = '$1,234.56';
          break;
        case 'percentage':
          newTestData[field.name] = '75%';
          break;
        case 'rating':
          newTestData[field.name] = 4;
          break;
        default:
          newTestData[field.name] = `Sample ${field.type}`;
      }
    });
    
    setTestData(newTestData);
  };

  // Validate field data
  const validateField = (field: ModuleField, value: any): string | null => {
    if (field.isRequired && (!value || value === '')) {
      return `${field.label} is required`;
    }

    if (field.validation.minLength && value && value.length < field.validation.minLength) {
      return `${field.label} must be at least ${field.validation.minLength} characters`;
    }

    if (field.validation.maxLength && value && value.length > field.validation.maxLength) {
      return `${field.label} must be no more than ${field.validation.maxLength} characters`;
    }

    if (field.validation.min && value && parseFloat(value) < field.validation.min) {
      return `${field.label} must be at least ${field.validation.min}`;
    }

    if (field.validation.max && value && parseFloat(value) > field.validation.max) {
      return `${field.label} must be no more than ${field.validation.max}`;
    }

    if (field.validation.pattern && value) {
      const regex = new RegExp(field.validation.pattern);
      if (!regex.test(value)) {
        return `${field.label} format is invalid`;
      }
    }

    return null;
  };

  // Run validation on all fields
  const runValidation = () => {
    const errors: Record<string, string> = {};
    
    module.fields.forEach(field => {
      const error = validateField(field, testData[field.name]);
      if (error) {
        errors[field.name] = error;
      }
    });
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle field value change
  const handleFieldChange = (fieldName: string, value: any) => {
    setTestData(prev => ({ ...prev, [fieldName]: value }));
    
    // Clear validation error for this field
    if (validationErrors[fieldName]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  // Render field based on type
  const renderField = (field: ModuleField) => {
    const value = testData[field.name] || field.defaultValue || '';
    const error = validationErrors[field.name];
    const widthClass = {
      full: 'col-span-full',
      half: 'col-span-6',
      third: 'col-span-4',
      quarter: 'col-span-3',
    }[field.uiConfig.width];

    const fieldElement = (() => {
      switch (field.type) {
        case 'text':
        case 'email':
        case 'url':
        case 'phone':
          return (
            <Input
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              type={field.type === 'email' ? 'email' : field.type === 'url' ? 'url' : 'text'}
            />
          );
        
        case 'number':
        case 'currency':
        case 'percentage':
          return (
            <Input
              type="number"
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              min={field.validation.min}
              max={field.validation.max}
            />
          );
        
        case 'textarea':
          return (
            <Textarea
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              rows={3}
            />
          );
        
        case 'date':
          return (
            <Input
              type="date"
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
            />
          );
        
        case 'datetime':
          return (
            <Input
              type="datetime-local"
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
            />
          );
        
        case 'boolean':
          return (
            <Switch
              checked={value}
              onCheckedChange={(checked) => handleFieldChange(field.name, checked)}
            />
          );
        
        case 'select':
          return (
            <Select value={value} onValueChange={(val) => handleFieldChange(field.name, val)}>
              <SelectTrigger>
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Option 1">Option 1</SelectItem>
                <SelectItem value="Option 2">Option 2</SelectItem>
                <SelectItem value="Option 3">Option 3</SelectItem>
              </SelectContent>
            </Select>
          );
        
        case 'rating':
          return (
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  onClick={() => handleFieldChange(field.name, star)}
                  className={`text-2xl ${star <= value ? 'text-yellow-400' : 'text-gray-300'}`}
                >
                  ★
                </button>
              ))}
            </div>
          );
        
        default:
          return (
            <Input
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
            />
          );
      }
    })();

    return (
      <div key={field.id} className={widthClass}>
        <div className="space-y-2">
          <Label htmlFor={field.name} className="flex items-center gap-2">
            {field.label}
            {field.isRequired && <span className="text-red-500">*</span>}
          </Label>
          {fieldElement}
          {field.helpText && (
            <p className="text-xs text-muted-foreground">{field.helpText}</p>
          )}
          {error && (
            <p className="text-xs text-red-500 flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              {error}
            </p>
          )}
        </div>
      </div>
    );
  };

  // Group fields by group
  const groupedFields = useMemo(() => {
    const groups: Record<string, ModuleField[]> = {};
    
    module.fields.forEach(field => {
      const groupName = field.group || 'default';
      if (!groups[groupName]) {
        groups[groupName] = [];
      }
      groups[groupName].push(field);
    });
    
    return groups;
  }, [module.fields]);

  const getDeviceClass = () => {
    switch (deviceView) {
      case 'mobile':
        return 'max-w-sm mx-auto';
      case 'tablet':
        return 'max-w-2xl mx-auto';
      default:
        return 'max-w-4xl mx-auto';
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Preview Controls */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h3 className="font-medium">Module Preview</h3>
            <Badge variant="outline">{module.fields.length} fields</Badge>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Device View Toggle */}
            <div className="flex items-center border rounded-lg p-1">
              <Button
                variant={deviceView === 'desktop' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setDeviceView('desktop')}
              >
                <Monitor className="h-4 w-4" />
              </Button>
              <Button
                variant={deviceView === 'tablet' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setDeviceView('tablet')}
              >
                <Tablet className="h-4 w-4" />
              </Button>
              <Button
                variant={deviceView === 'mobile' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setDeviceView('mobile')}
              >
                <Smartphone className="h-4 w-4" />
              </Button>
            </div>
            
            <Button variant="outline" size="sm" onClick={generateTestData}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Generate Data
            </Button>
            <Button variant="outline" size="sm" onClick={runValidation}>
              <Play className="h-4 w-4 mr-2" />
              Test Validation
            </Button>
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-auto">
        <Tabs value={previewMode} onValueChange={(value: any) => setPreviewMode(value)}>
          <div className="border-b px-4">
            <TabsList>
              <TabsTrigger value="form" className="flex items-center gap-2">
                <Layout className="h-4 w-4" />
                Form View
              </TabsTrigger>
              <TabsTrigger value="display" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Display View
              </TabsTrigger>
              <TabsTrigger value="mobile" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                JSON Data
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="p-6">
            <div className={getDeviceClass()}>
              <TabsContent value="form" className="mt-0">
                {module.fields.length === 0 ? (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Layout className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="font-medium mb-2">No Fields to Preview</h3>
                      <p className="text-sm text-muted-foreground">
                        Add fields to see the form preview.
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardHeader>
                      <CardTitle>{module.name}</CardTitle>
                      {module.description && (
                        <p className="text-sm text-muted-foreground">{module.description}</p>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        {Object.entries(groupedFields).map(([groupName, fields]) => (
                          <div key={groupName}>
                            {groupName !== 'default' && (
                              <h4 className="font-medium mb-4 pb-2 border-b">{groupName}</h4>
                            )}
                            <div className="grid grid-cols-12 gap-4">
                              {fields
                                .sort((a, b) => a.order - b.order)
                                .map(renderField)}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="display" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Display View</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      How the module data would appear in asset details
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {module.fields.map(field => (
                        <div key={field.id} className="flex justify-between py-2 border-b">
                          <span className="font-medium">{field.label}:</span>
                          <span className="text-muted-foreground">
                            {testData[field.name] || field.defaultValue || '—'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="mobile" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>JSON Data Structure</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Raw data structure for API integration
                    </p>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto">
                      {JSON.stringify({ 
                        module: {
                          id: module.id,
                          name: module.name,
                          version: module.version,
                        },
                        data: testData,
                        validation: validationErrors
                      }, null, 2)}
                    </pre>
                  </CardContent>
                </Card>
              </TabsContent>
            </div>
          </div>
        </Tabs>
      </div>

      {/* Validation Summary */}
      {Object.keys(validationErrors).length > 0 && (
        <div className="border-t p-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {Object.keys(validationErrors).length} validation error{Object.keys(validationErrors).length !== 1 ? 's' : ''} found.
              Please fix the errors above.
            </AlertDescription>
          </Alert>
        </div>
      )}
    </div>
  );
}
