"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Shield, 
  Plus, 
  Trash2, 
  AlertCircle, 
  CheckCircle, 
  Code,
  Link,
  Play,
  Settings
} from "lucide-react";
import { AssetModule, ModuleValidationRule, CrossFieldValidation } from "@/lib/types/asset-modules";

interface ModuleValidationEditorProps {
  module: AssetModule;
  onUpdate: (updates: Partial<AssetModule>) => void;
  readOnly?: boolean;
}

export function ModuleValidationEditor({
  module,
  onUpdate,
  readOnly = false,
}: ModuleValidationEditorProps) {
  const [selectedRule, setSelectedRule] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<any>(null);

  const handleValidationUpdate = (updates: any) => {
    onUpdate({
      validation: {
        ...module.validation,
        ...updates,
      },
    });
  };

  const handleAddRule = () => {
    const newRule: ModuleValidationRule = {
      id: `rule-${Date.now()}`,
      field: module.fields[0]?.name || '',
      type: 'required',
      config: {},
      errorMessage: 'This field is required',
    };

    handleValidationUpdate({
      rules: [...module.validation.rules, newRule],
    });
  };

  const handleUpdateRule = (ruleId: string, updates: Partial<ModuleValidationRule>) => {
    const updatedRules = module.validation.rules.map(rule =>
      rule.id === ruleId ? { ...rule, ...updates } : rule
    );
    handleValidationUpdate({ rules: updatedRules });
  };

  const handleDeleteRule = (ruleId: string) => {
    const updatedRules = module.validation.rules.filter(rule => rule.id !== ruleId);
    handleValidationUpdate({ rules: updatedRules });
    if (selectedRule === ruleId) {
      setSelectedRule(null);
    }
  };

  const handleAddCrossFieldValidation = () => {
    const newValidation: CrossFieldValidation = {
      id: `cross-${Date.now()}`,
      name: 'Cross Field Validation',
      fields: [],
      condition: '',
      errorMessage: 'Fields do not meet the required condition',
    };

    handleValidationUpdate({
      crossFieldValidation: [...module.validation.crossFieldValidation, newValidation],
    });
  };

  const handleUpdateCrossFieldValidation = (validationId: string, updates: Partial<CrossFieldValidation>) => {
    const updatedValidations = module.validation.crossFieldValidation.map(validation =>
      validation.id === validationId ? { ...validation, ...updates } : validation
    );
    handleValidationUpdate({ crossFieldValidation: updatedValidations });
  };

  const handleDeleteCrossFieldValidation = (validationId: string) => {
    const updatedValidations = module.validation.crossFieldValidation.filter(
      validation => validation.id !== validationId
    );
    handleValidationUpdate({ crossFieldValidation: updatedValidations });
  };

  const handleTestValidation = () => {
    // Simulate validation testing
    const results = {
      fieldValidations: module.validation.rules.map(rule => ({
        ruleId: rule.id,
        field: rule.field,
        status: Math.random() > 0.3 ? 'passed' : 'failed',
        message: Math.random() > 0.3 ? 'Validation passed' : rule.errorMessage,
      })),
      crossFieldValidations: module.validation.crossFieldValidation.map(validation => ({
        validationId: validation.id,
        name: validation.name,
        status: Math.random() > 0.5 ? 'passed' : 'failed',
        message: Math.random() > 0.5 ? 'Cross-field validation passed' : validation.errorMessage,
      })),
    };
    setTestResults(results);
  };

  const validationTypes = [
    { value: 'required', label: 'Required', description: 'Field must have a value' },
    { value: 'format', label: 'Format', description: 'Field must match a specific format' },
    { value: 'range', label: 'Range', description: 'Field value must be within a range' },
    { value: 'custom', label: 'Custom', description: 'Custom validation logic' },
  ];

  const availableFields = module.fields.map(field => ({
    value: field.name,
    label: field.label,
  }));

  return (
    <div className="h-full flex">
      {/* Validation Rules List */}
      <div className="w-1/3 border-r bg-muted/30">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Validation Rules</h3>
            {!readOnly && (
              <Button size="sm" onClick={handleAddRule}>
                <Plus className="h-4 w-4 mr-2" />
                Add Rule
              </Button>
            )}
          </div>
        </div>

        <ScrollArea className="h-[calc(50%-80px)]">
          <div className="p-4 space-y-2">
            {module.validation.rules.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Shield className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No validation rules</p>
              </div>
            ) : (
              module.validation.rules.map((rule) => (
                <Card
                  key={rule.id}
                  className={`cursor-pointer transition-all ${
                    selectedRule === rule.id
                      ? "ring-2 ring-primary border-primary"
                      : "hover:border-primary/50"
                  }`}
                  onClick={() => setSelectedRule(rule.id)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium text-sm">{rule.field}</p>
                        <p className="text-xs text-muted-foreground">{rule.type}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">{rule.type}</Badge>
                        {!readOnly && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteRule(rule.id);
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </ScrollArea>

        {/* Cross-Field Validations */}
        <div className="border-t">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Cross-Field Rules</h3>
              {!readOnly && (
                <Button size="sm" onClick={handleAddCrossFieldValidation}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add
                </Button>
              )}
            </div>
          </div>

          <ScrollArea className="h-[calc(50%-80px)]">
            <div className="p-4 space-y-2">
              {module.validation.crossFieldValidation.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Link className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No cross-field rules</p>
                </div>
              ) : (
                module.validation.crossFieldValidation.map((validation) => (
                  <Card key={validation.id} className="cursor-pointer hover:border-primary/50">
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="font-medium text-sm">{validation.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {validation.fields.length} field{validation.fields.length !== 1 ? 's' : ''}
                          </p>
                        </div>
                        {!readOnly && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteCrossFieldValidation(validation.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* Validation Configuration */}
      <div className="flex-1">
        {selectedRule ? (
          <ValidationRuleEditor
            rule={module.validation.rules.find(r => r.id === selectedRule)!}
            availableFields={availableFields}
            onUpdate={(updates) => handleUpdateRule(selectedRule, updates)}
            readOnly={readOnly}
          />
        ) : (
          <div className="h-full flex flex-col">
            {/* Validation Overview */}
            <div className="p-6 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center justify-between">
                    Validation Overview
                    <Button variant="outline" size="sm" onClick={handleTestValidation}>
                      <Play className="h-4 w-4 mr-2" />
                      Test All
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                      <div className="text-2xl font-bold">{module.validation.rules.length}</div>
                      <div className="text-sm text-muted-foreground">Field Rules</div>
                    </div>
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                      <div className="text-2xl font-bold">{module.validation.crossFieldValidation.length}</div>
                      <div className="text-sm text-muted-foreground">Cross-Field Rules</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Test Results */}
              {testResults && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Test Results</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Field Validations</h4>
                      <div className="space-y-2">
                        {testResults.fieldValidations.map((result: any) => (
                          <div key={result.ruleId} className="flex items-center justify-between p-2 border rounded">
                            <span className="text-sm">{result.field}</span>
                            <div className="flex items-center gap-2">
                              {result.status === 'passed' ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                              <span className="text-xs text-muted-foreground">{result.message}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {testResults.crossFieldValidations.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Cross-Field Validations</h4>
                        <div className="space-y-2">
                          {testResults.crossFieldValidations.map((result: any) => (
                            <div key={result.validationId} className="flex items-center justify-between p-2 border rounded">
                              <span className="text-sm">{result.name}</span>
                              <div className="flex items-center gap-2">
                                {result.status === 'passed' ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                  <AlertCircle className="h-4 w-4 text-red-500" />
                                )}
                                <span className="text-xs text-muted-foreground">{result.message}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Getting Started */}
              {module.validation.rules.length === 0 && module.validation.crossFieldValidation.length === 0 && (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Shield className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="font-medium mb-2">No Validation Rules</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Add validation rules to ensure data quality and consistency.
                    </p>
                    {!readOnly && (
                      <div className="flex gap-2 justify-center">
                        <Button onClick={handleAddRule}>
                          <Plus className="h-4 w-4 mr-2" />
                          Add Field Rule
                        </Button>
                        <Button variant="outline" onClick={handleAddCrossFieldValidation}>
                          <Plus className="h-4 w-4 mr-2" />
                          Add Cross-Field Rule
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

interface ValidationRuleEditorProps {
  rule: ModuleValidationRule;
  availableFields: { value: string; label: string }[];
  onUpdate: (updates: Partial<ModuleValidationRule>) => void;
  readOnly?: boolean;
}

function ValidationRuleEditor({
  rule,
  availableFields,
  onUpdate,
  readOnly = false,
}: ValidationRuleEditorProps) {
  const validationTypes = [
    { value: 'required', label: 'Required', description: 'Field must have a value' },
    { value: 'format', label: 'Format', description: 'Field must match a specific format' },
    { value: 'range', label: 'Range', description: 'Field value must be within a range' },
    { value: 'custom', label: 'Custom', description: 'Custom validation logic' },
  ];

  return (
    <ScrollArea className="h-full">
      <div className="p-6 space-y-6">
        <div className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          <h3 className="font-medium">Validation Rule Configuration</h3>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Basic Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Field</Label>
              <Select
                value={rule.field}
                onValueChange={(value) => onUpdate({ field: value })}
                disabled={readOnly}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {availableFields.map((field) => (
                    <SelectItem key={field.value} value={field.value}>
                      {field.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Validation Type</Label>
              <Select
                value={rule.type}
                onValueChange={(value: any) => onUpdate({ type: value })}
                disabled={readOnly}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {validationTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div>
                        <div className="font-medium">{type.label}</div>
                        <div className="text-xs text-muted-foreground">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Error Message</Label>
              <Input
                value={rule.errorMessage}
                onChange={(e) => onUpdate({ errorMessage: e.target.value })}
                placeholder="Error message to display when validation fails"
                disabled={readOnly}
              />
            </div>
          </CardContent>
        </Card>

        {/* Type-specific configuration */}
        {rule.type === 'format' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Format Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Pattern (Regex)</Label>
                <Input
                  value={rule.config.pattern || ''}
                  onChange={(e) => onUpdate({ 
                    config: { ...rule.config, pattern: e.target.value }
                  })}
                  placeholder="Regular expression pattern"
                  disabled={readOnly}
                />
              </div>
            </CardContent>
          </Card>
        )}

        {rule.type === 'range' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Range Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Minimum Value</Label>
                  <Input
                    type="number"
                    value={rule.config.min || ''}
                    onChange={(e) => onUpdate({ 
                      config: { ...rule.config, min: parseFloat(e.target.value) }
                    })}
                    disabled={readOnly}
                  />
                </div>
                <div>
                  <Label>Maximum Value</Label>
                  <Input
                    type="number"
                    value={rule.config.max || ''}
                    onChange={(e) => onUpdate({ 
                      config: { ...rule.config, max: parseFloat(e.target.value) }
                    })}
                    disabled={readOnly}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {rule.type === 'custom' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Custom Validation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Validation Expression</Label>
                <Textarea
                  value={rule.config.expression || ''}
                  onChange={(e) => onUpdate({ 
                    config: { ...rule.config, expression: e.target.value }
                  })}
                  placeholder="JavaScript expression that returns true/false"
                  rows={4}
                  disabled={readOnly}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Use 'value' to reference the field value. Example: value.length >= 5
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </ScrollArea>
  );
}
