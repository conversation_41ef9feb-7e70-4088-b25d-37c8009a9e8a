"use client";

import React from "react";
import { Scroll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  X, 
  Trash2, 
  Terminal,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle
} from "lucide-react";
import { ConsoleMessage } from "./use-module-editor-state";

interface ModuleConsolePanelProps {
  output: ConsoleMessage[];
  onClear: () => void;
  onClose: () => void;
}

export function ModuleConsolePanel({
  output,
  onClear,
  onClose,
}: ModuleConsolePanelProps) {
  const getMessageIcon = (type: ConsoleMessage['type']) => {
    switch (type) {
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  const getMessageColor = (type: ConsoleMessage['type']) => {
    switch (type) {
      case 'error':
        return 'text-red-600';
      case 'warning':
        return 'text-yellow-600';
      case 'success':
        return 'text-green-600';
      default:
        return 'text-blue-600';
    }
  };

  return (
    <div className="h-full border-t bg-background flex flex-col">
      {/* Console Header */}
      <div className="p-2 border-b bg-muted/30 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Terminal className="h-4 w-4" />
          <h3 className="text-sm font-medium">Console</h3>
          <Badge variant="outline" className="text-xs">
            {output.length} message{output.length !== 1 ? 's' : ''}
          </Badge>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={onClear}
            disabled={output.length === 0}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Console Content */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {output.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Terminal className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No console output</p>
            </div>
          ) : (
            <div className="space-y-1">
              {output.map((message, index) => (
                <div
                  key={index}
                  className="flex items-start gap-2 p-2 rounded text-sm hover:bg-muted/30"
                >
                  <div className="flex-shrink-0 mt-0.5">
                    {getMessageIcon(message.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className={`font-medium ${getMessageColor(message.type)}`}>
                      {message.message}
                    </div>
                    {message.details && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {message.details}
                      </div>
                    )}
                    <div className="text-xs text-muted-foreground mt-1">
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
