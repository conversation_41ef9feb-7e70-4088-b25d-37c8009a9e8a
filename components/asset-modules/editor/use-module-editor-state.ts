import { useState, useCallback } from "react";

export interface EditorTab {
  id: string;
  title: string;
  type: 'fields' | 'logic' | 'rendering' | 'validation' | 'preview' | 'field' | 'node' | 'rule';
  hasUnsavedChanges?: boolean;
  data?: any;
}

export interface SelectedItem {
  type: 'field' | 'node' | 'rule' | 'section' | 'view' | null;
  id: string | null;
  data?: any;
}

export interface ConsoleMessage {
  type: 'info' | 'success' | 'warning' | 'error';
  message: string;
  timestamp: Date;
  details?: string;
}

export type SaveStatus = 'idle' | 'saving' | 'saved' | 'error';

export function useModuleEditorState() {
  const [selectedItem, setSelectedItem] = useState<SelectedItem>({ type: null, id: null });
  const [activeTab, setActiveTab] = useState<string>('fields');
  const [openTabs, setOpenTabs] = useState<EditorTab[]>([
    { id: 'fields', title: 'Fields', type: 'fields' },
    { id: 'logic', title: 'Logic', type: 'logic' },
    { id: 'rendering', title: 'Rendering', type: 'rendering' },
    { id: 'validation', title: 'Validation', type: 'validation' },
    { id: 'preview', title: 'Preview', type: 'preview' },
  ]);
  
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<SaveStatus>('idle');
  const [showConsole, setShowConsole] = useState(false);
  const [consoleOutput, setConsoleOutput] = useState<ConsoleMessage[]>([]);

  const addTab = useCallback((tab: EditorTab) => {
    setOpenTabs(prev => {
      const existing = prev.find(t => t.id === tab.id);
      if (existing) {
        return prev;
      }
      return [...prev, tab];
    });
    setActiveTab(tab.id);
  }, []);

  const removeTab = useCallback((tabId: string) => {
    setOpenTabs(prev => {
      const newTabs = prev.filter(tab => tab.id !== tabId);
      
      // If we're closing the active tab, switch to another tab
      if (activeTab === tabId && newTabs.length > 0) {
        const currentIndex = prev.findIndex(tab => tab.id === tabId);
        const nextTab = newTabs[Math.min(currentIndex, newTabs.length - 1)];
        setActiveTab(nextTab.id);
      }
      
      return newTabs;
    });
  }, [activeTab]);

  const updateTab = useCallback((tabId: string, updates: Partial<EditorTab>) => {
    setOpenTabs(prev => prev.map(tab => 
      tab.id === tabId ? { ...tab, ...updates } : tab
    ));
  }, []);

  const addConsoleMessage = useCallback((message: ConsoleMessage) => {
    setConsoleOutput(prev => [...prev, message]);
  }, []);

  const clearConsole = useCallback(() => {
    setConsoleOutput([]);
  }, []);

  const selectItem = useCallback((item: SelectedItem) => {
    setSelectedItem(item);
    
    // Auto-open relevant tab if item is selected
    if (item.type === 'field' && !openTabs.find(t => t.id === 'fields')) {
      addTab({ id: 'fields', title: 'Fields', type: 'fields' });
    } else if (item.type === 'node' && !openTabs.find(t => t.id === 'logic')) {
      addTab({ id: 'logic', title: 'Logic', type: 'logic' });
    } else if (item.type === 'rule' && !openTabs.find(t => t.id === 'validation')) {
      addTab({ id: 'validation', title: 'Validation', type: 'validation' });
    }
  }, [openTabs, addTab]);

  return {
    // Selection state
    selectedItem,
    setSelectedItem: selectItem,
    
    // Tab state
    activeTab,
    setActiveTab,
    openTabs,
    setOpenTabs,
    addTab,
    removeTab,
    updateTab,
    
    // Save state
    isSaving,
    setIsSaving,
    saveStatus,
    setSaveStatus,
    
    // Console state
    showConsole,
    setShowConsole,
    consoleOutput,
    setConsoleOutput,
    addConsoleMessage,
    clearConsole,
  };
}
