"use client";

import React from "react";
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AssetModule, ModuleField } from "@/lib/types/asset-modules";
import { SelectedItem } from "./use-module-editor-state";

interface ModulePropertiesPanelProps {
  module: AssetModule;
  selectedItem: SelectedItem;
  onUpdate: (updates: Partial<AssetModule>) => void;
}

export function ModulePropertiesPanel({
  module,
  selectedItem,
  onUpdate,
}: ModulePropertiesPanelProps) {
  const renderFieldProperties = (fieldId: string) => {
    const field = module.fields.find(f => f.id === fieldId);
    if (!field) return null;

    const updateField = (updates: Partial<ModuleField>) => {
      const updatedFields = module.fields.map(f =>
        f.id === fieldId ? { ...f, ...updates } : f
      );
      onUpdate({ fields: updatedFields });
    };

    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Field Properties</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-xs">Name</Label>
              <Input
                value={field.name}
                onChange={(e) => updateField({ name: e.target.value })}
                className="h-8 text-sm"
              />
            </div>
            <div>
              <Label className="text-xs">Label</Label>
              <Input
                value={field.label}
                onChange={(e) => updateField({ label: e.target.value })}
                className="h-8 text-sm"
              />
            </div>
            <div>
              <Label className="text-xs">Type</Label>
              <Select
                value={field.type}
                onValueChange={(value: any) => updateField({ type: value })}
              >
                <SelectTrigger className="h-8 text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">Text</SelectItem>
                  <SelectItem value="number">Number</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="boolean">Boolean</SelectItem>
                  <SelectItem value="select">Select</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Required</Label>
              <Switch
                checked={field.isRequired}
                onCheckedChange={(checked) => updateField({ isRequired: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Unique</Label>
              <Switch
                checked={field.isUnique}
                onCheckedChange={(checked) => updateField({ isUnique: checked })}
              />
            </div>
            <div>
              <Label className="text-xs">Description</Label>
              <Textarea
                value={field.description || ''}
                onChange={(e) => updateField({ description: e.target.value })}
                className="text-sm"
                rows={2}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderModuleProperties = () => {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Module Properties</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-xs">Name</Label>
              <Input
                value={module.name}
                onChange={(e) => onUpdate({ name: e.target.value })}
                className="h-8 text-sm"
              />
            </div>
            <div>
              <Label className="text-xs">Version</Label>
              <Input
                value={module.version}
                onChange={(e) => onUpdate({ version: e.target.value })}
                className="h-8 text-sm"
              />
            </div>
            <div>
              <Label className="text-xs">Category</Label>
              <Select
                value={module.category}
                onValueChange={(value: any) => onUpdate({ category: value })}
              >
                <SelectTrigger className="h-8 text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="location">Location</SelectItem>
                  <SelectItem value="software">Software</SelectItem>
                  <SelectItem value="hardware">Hardware</SelectItem>
                  <SelectItem value="financial">Financial</SelectItem>
                  <SelectItem value="compliance">Compliance</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Description</Label>
              <Textarea
                value={module.description}
                onChange={(e) => onUpdate({ description: e.target.value })}
                className="text-sm"
                rows={3}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label className="text-xs">Active</Label>
              <Switch
                checked={module.isActive}
                onCheckedChange={(checked) => onUpdate({ isActive: checked })}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="text-center p-2 bg-muted/30 rounded">
                <div className="font-medium">{module.fields.length}</div>
                <div className="text-muted-foreground">Fields</div>
              </div>
              <div className="text-center p-2 bg-muted/30 rounded">
                <div className="font-medium">{module.logic.nodes.length}</div>
                <div className="text-muted-foreground">Nodes</div>
              </div>
              <div className="text-center p-2 bg-muted/30 rounded">
                <div className="font-medium">{module.validation.rules.length}</div>
                <div className="text-muted-foreground">Rules</div>
              </div>
              <div className="text-center p-2 bg-muted/30 rounded">
                <div className="font-medium">{module.usageCount}</div>
                <div className="text-muted-foreground">Uses</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <ScrollArea className="h-full">
      <div className="p-3">
        {selectedItem.type === 'field' && selectedItem.id ? (
          renderFieldProperties(selectedItem.id)
        ) : selectedItem.type === 'node' && selectedItem.id ? (
          <div className="space-y-4">
            <Card>
              <CardContent className="p-4">
                <p className="text-sm text-muted-foreground">
                  Logic node properties will be implemented here.
                </p>
              </CardContent>
            </Card>
          </div>
        ) : selectedItem.type === 'rule' && selectedItem.id ? (
          <div className="space-y-4">
            <Card>
              <CardContent className="p-4">
                <p className="text-sm text-muted-foreground">
                  Validation rule properties will be implemented here.
                </p>
              </CardContent>
            </Card>
          </div>
        ) : (
          renderModuleProperties()
        )}
      </div>
    </ScrollArea>
  );
}
