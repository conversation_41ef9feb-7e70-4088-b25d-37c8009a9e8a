"use client";

import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import { 
  ChevronRight, 
  ChevronDown, 
  Type, 
  Settings, 
  AlertTriangle,
  Layers,
  Workflow,
  Palette,
  Shield,
  Eye
} from "lucide-react";
import { AssetModule } from "@/lib/types/asset-modules";
import { SelectedItem, EditorTab } from "./use-module-editor-state";

interface ModuleStructureTreeProps {
  module: AssetModule;
  selectedItem: SelectedItem;
  onSelectItem: (item: SelectedItem) => void;
  onTabAdd: (tab: EditorTab) => void;
}

export function ModuleStructureTree({
  module,
  selectedItem,
  onSelectItem,
  onTabAdd,
}: ModuleStructureTreeProps) {
  const [expandedSections, setExpandedSections] = React.useState<Set<string>>(
    new Set(['fields', 'logic', 'validation'])
  );

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const handleItemClick = (item: SelectedItem) => {
    onSelectItem(item);
  };

  const sections = [
    {
      id: 'fields',
      title: 'Fields',
      icon: Layers,
      items: module.fields.map(field => ({
        id: field.id,
        name: field.name,
        label: field.label,
        type: 'field' as const,
        icon: Type,
      })),
    },
    {
      id: 'logic',
      title: 'Logic Nodes',
      icon: Workflow,
      items: module.logic.nodes.map(node => ({
        id: node.id,
        name: node.data.label,
        label: node.data.label,
        type: 'node' as const,
        icon: Settings,
      })),
    },
    {
      id: 'validation',
      title: 'Validation Rules',
      icon: Shield,
      items: module.validation.rules.map(rule => ({
        id: rule.id,
        name: rule.field,
        label: `${rule.field} (${rule.type})`,
        type: 'rule' as const,
        icon: AlertTriangle,
      })),
    },
  ];

  return (
    <ScrollArea className="h-full">
      <div className="p-2">
        {sections.map((section) => {
          const isExpanded = expandedSections.has(section.id);
          const SectionIcon = section.icon;
          
          return (
            <div key={section.id} className="mb-2">
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start p-1 h-auto"
                onClick={() => toggleSection(section.id)}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 mr-1" />
                ) : (
                  <ChevronRight className="h-4 w-4 mr-1" />
                )}
                <SectionIcon className="h-4 w-4 mr-2" />
                <span className="text-sm">{section.title}</span>
                <span className="ml-auto text-xs text-muted-foreground">
                  {section.items.length}
                </span>
              </Button>
              
              {isExpanded && (
                <div className="ml-4 mt-1 space-y-1">
                  {section.items.length === 0 ? (
                    <div className="text-xs text-muted-foreground p-2">
                      No {section.title.toLowerCase()}
                    </div>
                  ) : (
                    section.items.map((item) => {
                      const ItemIcon = item.icon;
                      const isSelected = selectedItem.type === item.type && selectedItem.id === item.id;
                      
                      return (
                        <Button
                          key={item.id}
                          variant={isSelected ? "secondary" : "ghost"}
                          size="sm"
                          className="w-full justify-start p-1 h-auto text-xs"
                          onClick={() => handleItemClick({ type: item.type, id: item.id })}
                        >
                          <ItemIcon className="h-3 w-3 mr-2" />
                          <span className="truncate">{item.label}</span>
                        </Button>
                      );
                    })
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </ScrollArea>
  );
}
