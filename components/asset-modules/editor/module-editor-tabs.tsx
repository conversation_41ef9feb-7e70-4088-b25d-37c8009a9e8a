"use client";

import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  X, 
  Plus, 
  ChevronDown,
  Layers,
  Workflow,
  Palette,
  Shield,
  Eye,
  Type,
  Settings,
  AlertTriangle
} from "lucide-react";
import { AssetModule } from "@/lib/types/asset-modules";
import { EditorTab, SelectedItem } from "./use-module-editor-state";
import { ModuleFieldDesigner } from "../module-field-designer";
import { ModuleLogicEditor } from "../module-logic-editor";
import { ModuleRenderingEditor } from "../module-rendering-editor";
import { ModuleValidationEditor } from "../module-validation-editor";
import { ModulePreview } from "../module-preview";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ModuleEditorTabsProps {
  module: AssetModule;
  onUpdate: (updates: Partial<AssetModule>) => void;
  activeTab: string;
  openTabs: EditorTab[];
  onTabChange: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onTabAdd: (tab: EditorTab) => void;
  selectedItem: SelectedItem;
}

export function ModuleEditorTabs({
  module,
  onUpdate,
  activeTab,
  openTabs,
  onTabChange,
  onTabClose,
  onTabAdd,
  selectedItem,
}: ModuleEditorTabsProps) {
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number; tabId: string } | null>(null);
  const tabsRef = useRef<HTMLDivElement>(null);

  const getTabIcon = (type: string) => {
    const icons = {
      fields: Layers,
      logic: Workflow,
      rendering: Palette,
      validation: Shield,
      preview: Eye,
      field: Type,
      node: Settings,
      rule: AlertTriangle,
    };
    return icons[type as keyof typeof icons] || Settings;
  };

  const getTabColor = (type: string) => {
    const colors = {
      fields: "text-blue-500",
      logic: "text-purple-500",
      rendering: "text-green-500",
      validation: "text-red-500",
      preview: "text-orange-500",
      field: "text-blue-400",
      node: "text-purple-400",
      rule: "text-red-400",
    };
    return colors[type as keyof typeof colors] || "text-gray-500";
  };

  const handleTabRightClick = (e: React.MouseEvent, tabId: string) => {
    e.preventDefault();
    setContextMenu({ x: e.clientX, y: e.clientY, tabId });
  };

  const handleCloseOthers = (keepTabId: string) => {
    openTabs.forEach(tab => {
      if (tab.id !== keepTabId) {
        onTabClose(tab.id);
      }
    });
    setContextMenu(null);
  };

  const handleCloseToRight = (fromTabId: string) => {
    const fromIndex = openTabs.findIndex(tab => tab.id === fromTabId);
    openTabs.slice(fromIndex + 1).forEach(tab => {
      onTabClose(tab.id);
    });
    setContextMenu(null);
  };

  const handleCloseAll = () => {
    openTabs.forEach(tab => {
      onTabClose(tab.id);
    });
    setContextMenu(null);
  };

  // Close context menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => setContextMenu(null);
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // Keyboard shortcuts for tab navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === 'Tab') {
        e.preventDefault();
        const currentIndex = openTabs.findIndex(tab => tab.id === activeTab);
        const nextIndex = e.shiftKey 
          ? (currentIndex - 1 + openTabs.length) % openTabs.length
          : (currentIndex + 1) % openTabs.length;
        onTabChange(openTabs[nextIndex].id);
      }
      
      if (e.ctrlKey && e.key === 'w') {
        e.preventDefault();
        if (openTabs.length > 1) {
          onTabClose(activeTab);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [activeTab, openTabs, onTabChange, onTabClose]);

  const renderTabContent = () => {
    const activeTabData = openTabs.find(tab => tab.id === activeTab);
    if (!activeTabData) return null;

    switch (activeTabData.type) {
      case 'fields':
        return (
          <ModuleFieldDesigner
            module={module}
            onUpdate={onUpdate}
            selectedFieldId={selectedItem.type === 'field' ? selectedItem.id : null}
          />
        );
      case 'logic':
        return (
          <ModuleLogicEditor
            module={module}
            onUpdate={onUpdate}
          />
        );
      case 'rendering':
        return (
          <ModuleRenderingEditor
            module={module}
            onUpdate={onUpdate}
          />
        );
      case 'validation':
        return (
          <ModuleValidationEditor
            module={module}
            onUpdate={onUpdate}
          />
        );
      case 'preview':
        return (
          <ModulePreview
            module={module}
          />
        );
      default:
        return (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Tab content not implemented</p>
            </div>
          </div>
        );
    }
  };

  const availableTabsToAdd = [
    { id: 'fields', title: 'Fields', type: 'fields' },
    { id: 'logic', title: 'Logic', type: 'logic' },
    { id: 'rendering', title: 'Rendering', type: 'rendering' },
    { id: 'validation', title: 'Validation', type: 'validation' },
    { id: 'preview', title: 'Preview', type: 'preview' },
  ].filter(tab => !openTabs.find(openTab => openTab.id === tab.id));

  return (
    <div className="h-full flex flex-col">
      {/* Tab Bar */}
      <div className="border-b bg-muted/30">
        <div className="flex items-center" ref={tabsRef}>
          {/* Tabs */}
          <div className="flex-1 flex overflow-x-auto scrollbar-hide">
            {openTabs.map((tab) => {
              const IconComponent = getTabIcon(tab.type);
              const isActive = activeTab === tab.id;
              
              return (
                <div
                  key={tab.id}
                  className={`
                    flex items-center gap-2 px-3 py-2 border-r cursor-pointer min-w-0 group
                    ${isActive 
                      ? 'bg-background border-b-2 border-b-primary' 
                      : 'hover:bg-background/50'
                    }
                  `}
                  onClick={() => onTabChange(tab.id)}
                  onContextMenu={(e) => handleTabRightClick(e, tab.id)}
                >
                  <IconComponent className={`h-4 w-4 flex-shrink-0 ${getTabColor(tab.type)}`} />
                  <span className="text-sm truncate min-w-0">
                    {tab.title}
                    {tab.hasUnsavedChanges && <span className="text-orange-500 ml-1">•</span>}
                  </span>
                  {openTabs.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 hover:bg-destructive hover:text-destructive-foreground"
                      onClick={(e) => {
                        e.stopPropagation();
                        onTabClose(tab.id);
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              );
            })}
          </div>

          {/* Add Tab Button */}
          {availableTabsToAdd.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="px-2">
                  <Plus className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {availableTabsToAdd.map((tab) => {
                  const IconComponent = getTabIcon(tab.type);
                  return (
                    <DropdownMenuItem
                      key={tab.id}
                      onClick={() => onTabAdd(tab as EditorTab)}
                    >
                      <IconComponent className={`h-4 w-4 mr-2 ${getTabColor(tab.type)}`} />
                      {tab.title}
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {renderTabContent()}
      </div>

      {/* Context Menu */}
      {contextMenu && (
        <div
          className="fixed z-50 bg-background border rounded-md shadow-lg py-1 min-w-[150px]"
          style={{ left: contextMenu.x, top: contextMenu.y }}
        >
          <button
            className="w-full px-3 py-1 text-left text-sm hover:bg-muted"
            onClick={() => {
              onTabClose(contextMenu.tabId);
              setContextMenu(null);
            }}
          >
            Close
          </button>
          <button
            className="w-full px-3 py-1 text-left text-sm hover:bg-muted"
            onClick={() => handleCloseOthers(contextMenu.tabId)}
          >
            Close Others
          </button>
          <button
            className="w-full px-3 py-1 text-left text-sm hover:bg-muted"
            onClick={() => handleCloseToRight(contextMenu.tabId)}
          >
            Close to the Right
          </button>
          <div className="border-t my-1" />
          <button
            className="w-full px-3 py-1 text-left text-sm hover:bg-muted"
            onClick={handleCloseAll}
          >
            Close All
          </button>
        </div>
      )}
    </div>
  );
}
