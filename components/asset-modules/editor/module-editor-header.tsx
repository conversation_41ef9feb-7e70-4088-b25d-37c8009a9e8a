"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Save, 
  X, 
  Download, 
  Play, 
  Terminal,
  Maximize2,
  Minimize2,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  Loader2
} from "lucide-react";
import { AssetModule } from "@/lib/types/asset-modules";
import { SaveStatus } from "./use-module-editor-state";

interface ModuleEditorHeaderProps {
  module: AssetModule;
  hasUnsavedChanges: boolean;
  saveStatus: SaveStatus;
  isSaving: boolean;
  isNewModule: boolean;
  onSave: () => void;
  onClose: () => void;
  onExport: () => void;
  onTest: () => void;
  onToggleConsole: () => void;
  onToggleFullscreen: () => void;
  isFullscreen: boolean;
}

export function ModuleEditorHeader({
  module,
  hasUnsavedChanges,
  saveStatus,
  isSaving,
  isNewModule,
  onSave,
  onClose,
  onExport,
  onTest,
  onToggleConsole,
  onToggleFullscreen,
  isFullscreen,
}: ModuleEditorHeaderProps) {
  const getSaveStatusIcon = () => {
    switch (saveStatus) {
      case 'saving':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'saved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Save className="h-4 w-4" />;
    }
  };

  const getSaveStatusText = () => {
    switch (saveStatus) {
      case 'saving':
        return 'Saving...';
      case 'saved':
        return 'Saved';
      case 'error':
        return 'Save Failed';
      default:
        return hasUnsavedChanges ? 'Save' : 'Saved';
    }
  };

  return (
    <div className="h-12 border-b bg-background flex items-center px-4">
      {/* Left Section - Module Info */}
      <div className="flex items-center gap-4 flex-1">
        <div className="flex items-center gap-2">
          <h1 className="font-semibold text-lg">
            {module.name}
            {hasUnsavedChanges && <span className="text-orange-500 ml-1">•</span>}
          </h1>
          {isNewModule && (
            <Badge variant="outline" className="text-xs">
              New
            </Badge>
          )}
          <Badge variant="secondary" className="text-xs">
            v{module.version}
          </Badge>
          <Badge 
            variant={module.isActive ? "default" : "outline"} 
            className="text-xs"
          >
            {module.isActive ? "Active" : "Draft"}
          </Badge>
        </div>

        {/* Breadcrumb */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Asset Modules</span>
          <span>/</span>
          <span>{module.category}</span>
          <span>/</span>
          <span className="text-foreground">{module.name}</span>
        </div>
      </div>

      {/* Center Section - Status */}
      <div className="flex items-center gap-4">
        {/* Save Status */}
        <div className="flex items-center gap-2 text-sm">
          {getSaveStatusIcon()}
          <span className={
            saveStatus === 'error' ? 'text-red-500' : 
            saveStatus === 'saved' ? 'text-green-500' : 
            saveStatus === 'saving' ? 'text-blue-500' : 
            hasUnsavedChanges ? 'text-orange-500' : 'text-muted-foreground'
          }>
            {getSaveStatusText()}
          </span>
        </div>

        {/* Module Stats */}
        <div className="flex items-center gap-4 text-xs text-muted-foreground">
          <span>{module.fields.length} fields</span>
          <span>{module.logic.nodes.length} logic nodes</span>
          <span>{module.validation.rules.length} rules</span>
        </div>
      </div>

      {/* Right Section - Actions */}
      <div className="flex items-center gap-2 ml-4">
        {/* Test Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={onTest}
          className="flex items-center gap-2"
        >
          <Play className="h-4 w-4" />
          Test
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Console Toggle */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleConsole}
          title="Toggle Console (Ctrl+`)"
        >
          <Terminal className="h-4 w-4" />
        </Button>

        {/* Fullscreen Toggle */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleFullscreen}
          title="Toggle Fullscreen (F11)"
        >
          {isFullscreen ? (
            <Minimize2 className="h-4 w-4" />
          ) : (
            <Maximize2 className="h-4 w-4" />
          )}
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Export Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={onExport}
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          Export
        </Button>

        {/* Save Button */}
        <Button
          size="sm"
          onClick={onSave}
          disabled={isSaving || (!hasUnsavedChanges && !isNewModule)}
          className="flex items-center gap-2"
        >
          {getSaveStatusIcon()}
          {getSaveStatusText()}
        </Button>

        {/* Close Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="flex items-center gap-2"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
