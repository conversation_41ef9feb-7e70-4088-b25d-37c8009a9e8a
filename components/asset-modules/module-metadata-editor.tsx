"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { 
  Plus, 
  X, 
  Info,
  Tag,
  User,
  Package
} from "lucide-react";
import { AssetModule, ModuleCategory } from "@/lib/types/asset-modules";

interface ModuleMetadataEditorProps {
  module: AssetModule;
  onUpdate: (updates: Partial<AssetModule>) => void;
  readOnly?: boolean;
}

export function ModuleMetadataEditor({
  module,
  onUpdate,
  readOnly = false,
}: ModuleMetadataEditorProps) {
  const [newTag, setNewTag] = React.useState("");

  const categories: { value: ModuleCategory; label: string }[] = [
    { value: "location", label: "Location" },
    { value: "software", label: "Software" },
    { value: "hardware", label: "Hardware" },
    { value: "financial", label: "Financial" },
    { value: "compliance", label: "Compliance" },
    { value: "maintenance", label: "Maintenance" },
    { value: "security", label: "Security" },
    { value: "custom", label: "Custom" },
  ];

  const handleAddTag = () => {
    if (newTag.trim() && !module.tags.includes(newTag.trim())) {
      onUpdate({
        tags: [...module.tags, newTag.trim()],
      });
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    onUpdate({
      tags: module.tags.filter(tag => tag !== tagToRemove),
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Info className="h-4 w-4" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="module-name">Module Name *</Label>
              <Input
                id="module-name"
                value={module.name}
                onChange={(e) => onUpdate({ name: e.target.value })}
                placeholder="Enter module name"
                disabled={readOnly}
              />
            </div>
            <div>
              <Label htmlFor="module-version">Version</Label>
              <Input
                id="module-version"
                value={module.version}
                onChange={(e) => onUpdate({ version: e.target.value })}
                placeholder="1.0.0"
                disabled={readOnly}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="module-description">Description</Label>
            <Textarea
              id="module-description"
              value={module.description}
              onChange={(e) => onUpdate({ description: e.target.value })}
              placeholder="Describe what this module does..."
              rows={3}
              disabled={readOnly}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="module-category">Category</Label>
              <Select
                value={module.category}
                onValueChange={(value: ModuleCategory) => onUpdate({ category: value })}
                disabled={readOnly}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="module-author">Author</Label>
              <Input
                id="module-author"
                value={module.author}
                onChange={(e) => onUpdate({ author: e.target.value })}
                placeholder="Author name"
                disabled={readOnly}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tags */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Tag className="h-4 w-4" />
            Tags
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            {module.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                {tag}
                {!readOnly && (
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
              </Badge>
            ))}
            {module.tags.length === 0 && (
              <p className="text-sm text-muted-foreground">No tags added</p>
            )}
          </div>

          {!readOnly && (
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Add a tag..."
                className="flex-1"
              />
              <Button onClick={handleAddTag} disabled={!newTag.trim()}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status & Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Package className="h-4 w-4" />
            Status & Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Active Module</Label>
              <p className="text-sm text-muted-foreground">
                Active modules can be used in asset types
              </p>
            </div>
            <Switch
              checked={module.isActive}
              onCheckedChange={(checked) => onUpdate({ isActive: checked })}
              disabled={readOnly}
            />
          </div>

          <div className="grid grid-cols-2 gap-4 pt-4 border-t">
            <div className="text-center p-3 bg-muted/30 rounded-lg">
              <div className="text-lg font-semibold">{module.fields.length}</div>
              <div className="text-sm text-muted-foreground">Fields</div>
            </div>
            <div className="text-center p-3 bg-muted/30 rounded-lg">
              <div className="text-lg font-semibold">{module.usageCount}</div>
              <div className="text-sm text-muted-foreground">Usage Count</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Compatibility */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Compatibility</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Compatible Asset Types</Label>
            <p className="text-sm text-muted-foreground mb-2">
              Leave empty to make compatible with all asset types
            </p>
            <div className="flex flex-wrap gap-2">
              {module.compatibleAssetTypes.length === 0 ? (
                <Badge variant="outline">All Asset Types</Badge>
              ) : (
                module.compatibleAssetTypes.map((assetType) => (
                  <Badge key={assetType} variant="secondary">
                    {assetType}
                  </Badge>
                ))
              )}
            </div>
          </div>

          <div>
            <Label>Dependencies</Label>
            <p className="text-sm text-muted-foreground mb-2">
              Other modules required for this module to work
            </p>
            <div className="flex flex-wrap gap-2">
              {module.dependencies.length === 0 ? (
                <p className="text-sm text-muted-foreground">No dependencies</p>
              ) : (
                module.dependencies.map((dependency) => (
                  <Badge key={dependency} variant="outline">
                    {dependency}
                  </Badge>
                ))
              )}
            </div>
          </div>

          <div>
            <Label>Required Permissions</Label>
            <p className="text-sm text-muted-foreground mb-2">
              Permissions needed to use this module
            </p>
            <div className="flex flex-wrap gap-2">
              {module.requiredPermissions.length === 0 ? (
                <p className="text-sm text-muted-foreground">No special permissions required</p>
              ) : (
                module.requiredPermissions.map((permission) => (
                  <Badge key={permission} variant="outline">
                    {permission}
                  </Badge>
                ))
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Metadata</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <Label className="text-xs text-muted-foreground">Created</Label>
              <p>{new Date(module.createdAt).toLocaleString()}</p>
            </div>
            <div>
              <Label className="text-xs text-muted-foreground">Last Updated</Label>
              <p>{new Date(module.updatedAt).toLocaleString()}</p>
            </div>
            <div>
              <Label className="text-xs text-muted-foreground">Module ID</Label>
              <p className="font-mono text-xs">{module.id}</p>
            </div>
            <div>
              <Label className="text-xs text-muted-foreground">Built-in Module</Label>
              <p>{module.isBuiltIn ? "Yes" : "No"}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
