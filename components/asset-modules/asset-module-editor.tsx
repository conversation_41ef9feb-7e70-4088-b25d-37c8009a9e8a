
"use client";

import React, { useState, use<PERSON>allback, useEffect } from "react";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Save,
  X,
  Download,
  Play,
  Settings,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Maximize2,
  Minimize2,
  Terminal,
  FileTree,
  Code,
  Eye,
  AlertCircle,
  CheckCircle,
  Clock,
  Layers,
  Workflow,
  Palette,
  Shield
} from "lucide-react";
import { AssetModule } from "@/lib/types/asset-modules";
// Reuse existing components
import { ModuleFieldDesigner } from "./module-field-designer";
import { ModuleLogicEditor } from "./module-logic-editor";
import { ModuleRenderingEditor } from "./module-rendering-editor";
import { ModuleValidationEditor } from "./module-validation-editor";
import { ModulePreview } from "./module-preview";

// New IDE-specific components
import { ModuleStructureTree } from "./editor/module-structure-tree";
import { ModulePropertiesPanel } from "./editor/module-properties-panel";
import { ModuleConsolePanel } from "./editor/module-console-panel";
import { ModuleEditorHeader } from "./editor/module-editor-header";
import { useModuleEditorState, EditorTab } from "./editor/use-module-editor-state";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface AssetModuleEditorProps {
  module: AssetModule;
  onUpdate: (updates: Partial<AssetModule>) => void;
  onSave: (module: AssetModule) => Promise<AssetModule>;
  onClose: () => void;
  onExport: (module: AssetModule) => void;
  hasUnsavedChanges: boolean;
  isNewModule?: boolean;
}

export function AssetModuleEditor({
  module,
  onUpdate,
  onSave,
  onClose,
  onExport,
  hasUnsavedChanges,
  isNewModule = false,
}: AssetModuleEditorProps) {
  const {
    selectedItem,
    setSelectedItem,
    activeTab,
    setActiveTab,
    openTabs,
    setOpenTabs,
    isSaving,
    setIsSaving,
    saveStatus,
    setSaveStatus,
    showConsole,
    setShowConsole,
    consoleOutput,
    setConsoleOutput,
  } = useModuleEditorState();

  const [leftPanelCollapsed, setLeftPanelCollapsed] = useState(false);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Auto-save functionality
  useEffect(() => {
    if (!hasUnsavedChanges) return;

    const autoSaveTimer = setTimeout(() => {
      handleAutoSave();
    }, 30000); // Auto-save after 30 seconds

    return () => clearTimeout(autoSaveTimer);
  }, [hasUnsavedChanges, module]);

  const handleAutoSave = async () => {
    if (!hasUnsavedChanges) return;

    try {
      setSaveStatus('saving');
      await onSave(module);
      setSaveStatus('saved');
      
      // Reset status after 2 seconds
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      setSaveStatus('error');
      console.error('Auto-save failed:', error);
      // Reset status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setSaveStatus('saving');
      
      await onSave(module);
      setSaveStatus('saved');
      
      setConsoleOutput(prev => [...prev, {
        type: 'success',
        message: 'Module saved successfully',
        timestamp: new Date(),
      }]);

      // Reset status after 2 seconds
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      setSaveStatus('error');
      setConsoleOutput(prev => [...prev, {
        type: 'error',
        message: 'Failed to save module',
        timestamp: new Date(),
      }]);
      console.error('Save failed:', error);
      // Reset status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handleTest = () => {
    setConsoleOutput(prev => [...prev, {
      type: 'info',
      message: 'Running module tests...',
      timestamp: new Date(),
    }]);

    // Simulate testing
    setTimeout(() => {
      setConsoleOutput(prev => [...prev, {
        type: 'success',
        message: 'All tests passed',
        timestamp: new Date(),
      }]);
    }, 1000);

    setShowConsole(true);
  };

  const handleExport = () => {
    onExport(module);
    setConsoleOutput(prev => [...prev, {
      type: 'info',
      message: 'Module exported successfully',
      timestamp: new Date(),
    }]);
  };

  const handleTabClose = (tabId: string) => {
    const newTabs = openTabs.filter(tab => tab.id !== tabId);
    setOpenTabs(newTabs);
    
    if (activeTab === tabId && newTabs.length > 0) {
      setActiveTab(newTabs[0].id);
    }
  };

  const handleTabAdd = (tab: EditorTab) => {
    if (!openTabs.find(t => t.id === tab.id)) {
      setOpenTabs([...openTabs, tab]);
    }
    setActiveTab(tab.id);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+S to save
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        handleSave();
      }
      
      // Ctrl+Shift+P for command palette (future implementation)
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        // TODO: Open command palette
      }
      
      // Ctrl+` to toggle console
      if (e.ctrlKey && e.key === '`') {
        e.preventDefault();
        setShowConsole(!showConsole);
      }
      
      // F11 for fullscreen
      if (e.key === 'F11') {
        e.preventDefault();
        toggleFullscreen();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showConsole, handleSave]);

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Editor Header */}
      <ModuleEditorHeader
        module={module}
        hasUnsavedChanges={hasUnsavedChanges}
        saveStatus={saveStatus}
        isSaving={isSaving}
        isNewModule={isNewModule}
        onSave={handleSave}
        onClose={onClose}
        onExport={handleExport}
        onTest={handleTest}
        onToggleConsole={() => setShowConsole(!showConsole)}
        onToggleFullscreen={toggleFullscreen}
        isFullscreen={isFullscreen}
      />

      {/* Main Editor Area */}
      <div className="flex-1 overflow-hidden">
        <PanelGroup direction="horizontal">
          {/* Left Panel - Structure Tree */}
          {!leftPanelCollapsed && (
            <>
              <Panel defaultSize={20} minSize={15} maxSize={35}>
                <div className="h-full border-r bg-muted/30">
                  <div className="p-2 border-b bg-background">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium flex items-center gap-2">
                        <FileTree className="h-4 w-4" />
                        Structure
                      </h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setLeftPanelCollapsed(true)}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <ModuleStructureTree
                    module={module}
                    selectedItem={selectedItem}
                    onSelectItem={setSelectedItem}
                    onTabAdd={handleTabAdd}
                  />
                </div>
              </Panel>
              <PanelResizeHandle className="w-1 bg-border hover:bg-primary/20 transition-colors" />
            </>
          )}

          {/* Center Panel - Main Editor */}
          <Panel defaultSize={leftPanelCollapsed ? 70 : 55} minSize={30}>
            <PanelGroup direction="vertical">
              {/* Main Editing Area */}
              <Panel defaultSize={showConsole ? 70 : 100} minSize={40}>
                <div className="h-full flex flex-col">
                  {/* Collapsed Left Panel Toggle */}
                  {leftPanelCollapsed && (
                    <div className="absolute top-2 left-2 z-10">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setLeftPanelCollapsed(false)}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  )}

                  <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
                    <div className="border-b bg-muted/30 px-4">
                      <TabsList className="h-12">
                        <TabsTrigger value="fields" className="flex items-center gap-2">
                          <Layers className="h-4 w-4" />
                          Fields
                        </TabsTrigger>
                        <TabsTrigger value="logic" className="flex items-center gap-2">
                          <Workflow className="h-4 w-4" />
                          Logic
                        </TabsTrigger>
                        <TabsTrigger value="rendering" className="flex items-center gap-2">
                          <Palette className="h-4 w-4" />
                          Rendering
                        </TabsTrigger>
                        <TabsTrigger value="validation" className="flex items-center gap-2">
                          <Shield className="h-4 w-4" />
                          Validation
                        </TabsTrigger>
                        <TabsTrigger value="preview" className="flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          Preview
                        </TabsTrigger>
                      </TabsList>
                    </div>

                    <div className="flex-1 overflow-auto">
                      <TabsContent value="fields" className="h-full m-0">
                        <ModuleFieldDesigner
                          module={module}
                          onUpdate={onUpdate}
                        />
                      </TabsContent>

                      <TabsContent value="logic" className="h-full m-0">
                        <ModuleLogicEditor
                          module={module}
                          onUpdate={onUpdate}
                        />
                      </TabsContent>

                      <TabsContent value="rendering" className="h-full m-0">
                        <ModuleRenderingEditor
                          module={module}
                          onUpdate={onUpdate}
                        />
                      </TabsContent>

                      <TabsContent value="validation" className="h-full m-0">
                        <ModuleValidationEditor
                          module={module}
                          onUpdate={onUpdate}
                        />
                      </TabsContent>

                      <TabsContent value="preview" className="h-full m-0">
                        <ModulePreview
                          module={module}
                        />
                      </TabsContent>
                    </div>
                  </Tabs>
                </div>
              </Panel>

              {/* Console Panel */}
              {showConsole && (
                <>
                  <PanelResizeHandle className="h-1 bg-border hover:bg-primary/20 transition-colors" />
                  <Panel defaultSize={30} minSize={20} maxSize={50}>
                    <ModuleConsolePanel
                      output={consoleOutput}
                      onClear={() => setConsoleOutput([])}
                      onClose={() => setShowConsole(false)}
                    />
                  </Panel>
                </>
              )}
            </PanelGroup>
          </Panel>

          {/* Right Panel - Properties */}
          {!rightPanelCollapsed && (
            <>
              <PanelResizeHandle className="w-1 bg-border hover:bg-primary/20 transition-colors" />
              <Panel defaultSize={25} minSize={20} maxSize={40}>
                <div className="h-full border-l bg-muted/30">
                  <div className="p-2 border-b bg-background">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Properties
                      </h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setRightPanelCollapsed(true)}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <ModulePropertiesPanel
                    module={module}
                    selectedItem={selectedItem}
                    onUpdate={onUpdate}
                  />
                </div>
              </Panel>
            </>
          )}

          {/* Collapsed Right Panel Toggle */}
          {rightPanelCollapsed && (
            <div className="absolute top-2 right-2 z-10">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setRightPanelCollapsed(false)}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </div>
          )}
        </PanelGroup>
      </div>
    </div>
  );
}
