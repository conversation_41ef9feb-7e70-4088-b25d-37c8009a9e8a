"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Download, 
  Upload, 
  Eye, 
  Settings,
  Search,
  Filter,
  Grid,
  List,
  Star,
  Users,
  Calendar,
  Package,
  Code,
  Layers,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react";
import { AssetModule, ModuleCategory, ModuleTemplate } from "@/lib/types/asset-modules";
import { AssetModulePlayground } from "./asset-module-playground";

interface ModuleManagementInterfaceProps {
  onCreateModule?: (template?: ModuleTemplate) => void;
  onEditModule?: (moduleId: string) => void;
}

export function ModuleManagementInterface({
  onCreateModule,
  onEditModule,
}: ModuleManagementInterfaceProps) {
  const [modules, setModules] = useState<AssetModule[]>([]);
  const [templates, setTemplates] = useState<ModuleTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<ModuleCategory | "all">("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedModule, setSelectedModule] = useState<AssetModule | null>(null);
  const [isPlaygroundOpen, setIsPlaygroundOpen] = useState(false);
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);

  useEffect(() => {
    loadModules();
    loadTemplates();
  }, []);

  const loadModules = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/asset-modules");
      if (response.ok) {
        const data = await response.json();
        setModules(data);
      }
    } catch (error) {
      console.error("Error loading modules:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      const response = await fetch("/api/asset-module-templates");
      if (response.ok) {
        const data = await response.json();
        setTemplates(data);
      }
    } catch (error) {
      console.error("Error loading templates:", error);
    }
  };

  const handleCreateModule = (template?: ModuleTemplate) => {
    if (onCreateModule) {
      onCreateModule(template);
    } else {
      setIsPlaygroundOpen(true);
      setSelectedModule(null);
    }
    setIsTemplateDialogOpen(false);
  };

  const handleEditModule = (module: AssetModule) => {
    if (onEditModule) {
      onEditModule(module.id);
    } else {
      setSelectedModule(module);
      setIsPlaygroundOpen(true);
    }
  };

  const handleDuplicateModule = async (module: AssetModule) => {
    const duplicatedModule = {
      ...module,
      id: `${module.id}-copy-${Date.now()}`,
      name: `${module.name} (Copy)`,
      isActive: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    try {
      const response = await fetch("/api/asset-modules", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(duplicatedModule),
      });

      if (response.ok) {
        loadModules();
      }
    } catch (error) {
      console.error("Error duplicating module:", error);
    }
  };

  const handleDeleteModule = async (moduleId: string) => {
    if (!confirm("Are you sure you want to delete this module?")) return;

    try {
      const response = await fetch(`/api/asset-modules/${moduleId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        loadModules();
      }
    } catch (error) {
      console.error("Error deleting module:", error);
    }
  };

  const handleExportModule = (module: AssetModule) => {
    const dataStr = JSON.stringify(module, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${module.name.replace(/\s+/g, '_')}_module.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const filteredModules = modules.filter(module => {
    const matchesSearch = module.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         module.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         module.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || module.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const categories: { value: ModuleCategory | "all"; label: string }[] = [
    { value: "all", label: "All Categories" },
    { value: "location", label: "Location" },
    { value: "software", label: "Software" },
    { value: "hardware", label: "Hardware" },
    { value: "financial", label: "Financial" },
    { value: "compliance", label: "Compliance" },
    { value: "maintenance", label: "Maintenance" },
    { value: "security", label: "Security" },
    { value: "custom", label: "Custom" },
  ];

  const getCategoryIcon = (category: ModuleCategory) => {
    const icons: Record<ModuleCategory, React.ElementType> = {
      location: Package,
      software: Code,
      hardware: Settings,
      financial: Calendar,
      compliance: CheckCircle,
      maintenance: Settings,
      security: AlertCircle,
      custom: Layers,
    };
    return icons[category] || Package;
  };

  const getCategoryColor = (category: ModuleCategory) => {
    const colors: Record<ModuleCategory, string> = {
      location: "#3b82f6",
      software: "#10b981",
      hardware: "#f59e0b",
      financial: "#ef4444",
      compliance: "#8b5cf6",
      maintenance: "#06b6d4",
      security: "#f97316",
      custom: "#6b7280",
    };
    return colors[category] || "#6b7280";
  };

  if (isPlaygroundOpen) {
    return (
      <AssetModulePlayground
        moduleId={selectedModule?.id}
        onSave={(module) => {
          loadModules();
          setIsPlaygroundOpen(false);
          setSelectedModule(null);
        }}
        onClose={() => {
          setIsPlaygroundOpen(false);
          setSelectedModule(null);
        }}
      />
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold">Asset Modules</h1>
            <p className="text-muted-foreground">
              Create and manage reusable field collections for your asset types
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => setIsTemplateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Module
            </Button>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="border-b p-4">
        <div className="flex items-center gap-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search modules..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={selectedCategory} onValueChange={(value: any) => setSelectedCategory(value)}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex items-center border rounded-lg p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-6">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Loading modules...</p>
            </div>
          </div>
        ) : filteredModules.length === 0 ? (
          <div className="text-center py-12">
            <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No modules found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery || selectedCategory !== "all" 
                ? "Try adjusting your search or filters"
                : "Create your first asset module to get started"
              }
            </p>
            <Button onClick={() => setIsTemplateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Module
            </Button>
          </div>
        ) : (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {filteredModules.map((module) => (
              <ModuleCard
                key={module.id}
                module={module}
                viewMode={viewMode}
                onEdit={() => handleEditModule(module)}
                onDuplicate={() => handleDuplicateModule(module)}
                onDelete={() => handleDeleteModule(module.id)}
                onExport={() => handleExportModule(module)}
                getCategoryIcon={getCategoryIcon}
                getCategoryColor={getCategoryColor}
              />
            ))}
          </div>
        )}
      </div>

      {/* Template Selection Dialog */}
      <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Module</DialogTitle>
          </DialogHeader>
          
          <Tabs defaultValue="templates">
            <TabsList>
              <TabsTrigger value="templates">From Template</TabsTrigger>
              <TabsTrigger value="scratch">From Scratch</TabsTrigger>
            </TabsList>
            
            <TabsContent value="templates" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templates.map((template) => (
                  <Card key={template.id} className="cursor-pointer hover:border-primary/50">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div
                          className="p-2 rounded-lg"
                          style={{ 
                            backgroundColor: `${getCategoryColor(template.category)}20`, 
                            color: getCategoryColor(template.category) 
                          }}
                        >
                          {React.createElement(getCategoryIcon(template.category), { className: "h-5 w-5" })}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium">{template.name}</h4>
                          <p className="text-sm text-muted-foreground">{template.description}</p>
                          <Badge variant="outline" className="mt-2">{template.category}</Badge>
                        </div>
                      </div>
                      <Button 
                        className="w-full mt-4" 
                        onClick={() => handleCreateModule(template)}
                      >
                        Use Template
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="scratch">
              <Card>
                <CardContent className="p-6 text-center">
                  <Code className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="font-medium mb-2">Start from Scratch</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Create a completely custom module with your own fields and logic.
                  </p>
                  <Button onClick={() => handleCreateModule()}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Blank Module
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface ModuleCardProps {
  module: AssetModule;
  viewMode: "grid" | "list";
  onEdit: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
  onExport: () => void;
  getCategoryIcon: (category: ModuleCategory) => React.ElementType;
  getCategoryColor: (category: ModuleCategory) => string;
}

function ModuleCard({
  module,
  viewMode,
  onEdit,
  onDuplicate,
  onDelete,
  onExport,
  getCategoryIcon,
  getCategoryColor,
}: ModuleCardProps) {
  const IconComponent = getCategoryIcon(module.category);
  const categoryColor = getCategoryColor(module.category);

  if (viewMode === 'list') {
    return (
      <Card className="hover:border-primary/50 transition-colors">
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div
              className="p-2 rounded-lg"
              style={{ 
                backgroundColor: `${categoryColor}20`, 
                color: categoryColor 
              }}
            >
              <IconComponent className="h-5 w-5" />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="font-medium">{module.name}</h3>
                <Badge variant={module.isActive ? "default" : "secondary"}>
                  {module.isActive ? "Active" : "Draft"}
                </Badge>
                <Badge variant="outline">{module.category}</Badge>
              </div>
              <p className="text-sm text-muted-foreground">{module.description}</p>
              <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                <span>{module.fields.length} fields</span>
                <span>v{module.version}</span>
                <span>{module.usageCount} uses</span>
              </div>
            </div>

            <div className="flex items-center gap-1">
              <Button variant="ghost" size="sm" onClick={onEdit}>
                <Edit className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={onDuplicate}>
                <Copy className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={onExport}>
                <Download className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={onDelete}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:border-primary/50 transition-colors">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div
              className="p-2 rounded-lg"
              style={{ 
                backgroundColor: `${categoryColor}20`, 
                color: categoryColor 
              }}
            >
              <IconComponent className="h-5 w-5" />
            </div>
            <div>
              <h3 className="font-medium">{module.name}</h3>
              <Badge variant="outline" className="mt-1">{module.category}</Badge>
            </div>
          </div>
          <Badge variant={module.isActive ? "default" : "secondary"}>
            {module.isActive ? "Active" : "Draft"}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground mb-4">{module.description}</p>
        
        <div className="grid grid-cols-3 gap-2 text-center text-xs mb-4">
          <div className="p-2 bg-muted/30 rounded">
            <div className="font-medium">{module.fields.length}</div>
            <div className="text-muted-foreground">Fields</div>
          </div>
          <div className="p-2 bg-muted/30 rounded">
            <div className="font-medium">v{module.version}</div>
            <div className="text-muted-foreground">Version</div>
          </div>
          <div className="p-2 bg-muted/30 rounded">
            <div className="font-medium">{module.usageCount}</div>
            <div className="text-muted-foreground">Uses</div>
          </div>
        </div>

        <div className="flex gap-1">
          <Button variant="outline" size="sm" onClick={onEdit} className="flex-1">
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
          <Button variant="outline" size="sm" onClick={onDuplicate}>
            <Copy className="h-3 w-3" />
          </Button>
          <Button variant="outline" size="sm" onClick={onExport}>
            <Download className="h-3 w-3" />
          </Button>
          <Button variant="outline" size="sm" onClick={onDelete}>
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
