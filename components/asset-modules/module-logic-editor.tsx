"use client";

import React, { useState, use<PERSON><PERSON>back, use<PERSON>emo } from "react";
import <PERSON><PERSON><PERSON><PERSON>, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  Connection,
  ConnectionMode,
  Panel,
  MiniMap,
} from "reactflow";
import "reactflow/dist/style.css";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Plus, 
  Play, 
  Save, 
  Trash2,
  Settings,
  Calculator,
  CheckCircle,
  GitBranch,
  Database,
  Code,
  Variable,
  Function,
  Workflow,
  Zap,
  Filter,
  ArrowRight
} from "lucide-react";

import { AssetModule, LogicNode, LogicEdge, LogicNodeType } from "@/lib/types/asset-modules";
// We'll create these components inline for now
// import { LogicNodeComponent } from "./logic-nodes/logic-node-component";
// import { NodePalette } from "./logic-nodes/node-palette";
// import { NodeConfigPanel } from "./logic-nodes/node-config-panel";

// Logic Node Component (inline for now)
const LogicNodeComponent = ({ data }: { data: any }) => {
  const getNodeIcon = (nodeType: LogicNodeType) => {
    const icons: Record<LogicNodeType, React.ElementType> = {
      field_input: ArrowRight,
      field_output: ArrowRight,
      computation: Calculator,
      validation: CheckCircle,
      condition: GitBranch,
      transform: Filter,
      api_call: Zap,
      database_query: Database,
      constant: Settings,
      variable: Variable,
      function: Function,
    };
    return icons[nodeType] || Settings;
  };

  const getNodeColor = (nodeType: LogicNodeType) => {
    const colors: Record<LogicNodeType, string> = {
      field_input: '#3b82f6',
      field_output: '#10b981',
      computation: '#f59e0b',
      validation: '#ef4444',
      condition: '#8b5cf6',
      transform: '#06b6d4',
      api_call: '#f97316',
      database_query: '#6366f1',
      constant: '#6b7280',
      variable: '#84cc16',
      function: '#ec4899',
    };
    return colors[nodeType] || '#6b7280';
  };

  const IconComponent = getNodeIcon(data.nodeType);
  const nodeColor = getNodeColor(data.nodeType);

  return (
    <div className="bg-background border-2 border-border rounded-lg shadow-lg min-w-[150px]">
      {/* Node Header */}
      <div
        className="px-3 py-2 rounded-t-lg text-white text-sm font-medium flex items-center gap-2"
        style={{ backgroundColor: nodeColor }}
      >
        <IconComponent className="h-4 w-4" />
        {data.label}
      </div>

      {/* Node Content */}
      <div className="p-3">
        <p className="text-xs text-muted-foreground">{data.description}</p>

        {/* Input Handles */}
        {data.inputs?.map((input: any, index: number) => (
          <div key={input.id} className="flex items-center gap-2 mt-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full" />
            <span className="text-xs">{input.name}</span>
          </div>
        ))}

        {/* Output Handles */}
        {data.outputs?.map((output: any, index: number) => (
          <div key={output.id} className="flex items-center justify-end gap-2 mt-2">
            <span className="text-xs">{output.name}</span>
            <div className="w-2 h-2 bg-green-500 rounded-full" />
          </div>
        ))}
      </div>
    </div>
  );
};

// Node Palette Component (inline for now)
const NodePalette = ({ isOpen, onClose, onAddNode, moduleFields }: any) => {
  if (!isOpen) return null;

  const nodeCategories = [
    {
      name: 'Field Operations',
      nodes: [
        { type: 'field_input' as LogicNodeType, name: 'Field Input', icon: ArrowRight },
        { type: 'field_output' as LogicNodeType, name: 'Field Output', icon: ArrowRight },
      ],
    },
    {
      name: 'Logic & Math',
      nodes: [
        { type: 'computation' as LogicNodeType, name: 'Computation', icon: Calculator },
        { type: 'condition' as LogicNodeType, name: 'Condition', icon: GitBranch },
        { type: 'validation' as LogicNodeType, name: 'Validation', icon: CheckCircle },
      ],
    },
    {
      name: 'Data',
      nodes: [
        { type: 'constant' as LogicNodeType, name: 'Constant', icon: Settings },
        { type: 'variable' as LogicNodeType, name: 'Variable', icon: Variable },
        { type: 'transform' as LogicNodeType, name: 'Transform', icon: Filter },
      ],
    },
  ];

  return (
    <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-96 max-h-[80vh] overflow-hidden">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Add Logic Node
            <Button variant="ghost" size="sm" onClick={onClose}>×</Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            <div className="space-y-4">
              {nodeCategories.map((category) => (
                <div key={category.name}>
                  <h4 className="font-medium text-sm mb-2">{category.name}</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {category.nodes.map((node) => (
                      <Button
                        key={node.type}
                        variant="outline"
                        className="h-auto p-3 flex flex-col items-center gap-2"
                        onClick={() => onAddNode(node.type, { x: 100, y: 100 })}
                      >
                        <node.icon className="h-5 w-5" />
                        <span className="text-xs">{node.name}</span>
                      </Button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};

// Node Config Panel Component (inline for now)
const NodeConfigPanel = ({ node, moduleFields, onUpdate, onDelete, readOnly }: any) => {
  return (
    <div className="h-full">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">{node.data.label}</h3>
          {!readOnly && (
            <Button variant="ghost" size="sm" onClick={onDelete}>
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
        <p className="text-sm text-muted-foreground">{node.data.description}</p>
      </div>

      <ScrollArea className="h-[calc(100%-80px)]">
        <div className="p-4 space-y-4">
          <div>
            <h4 className="font-medium text-sm mb-2">Configuration</h4>
            <p className="text-xs text-muted-foreground">
              Node configuration will be implemented based on node type.
            </p>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
};

// Custom node types
const nodeTypes = {
  logicNode: LogicNodeComponent,
};

interface ModuleLogicEditorProps {
  module: AssetModule;
  onUpdate: (updates: Partial<AssetModule>) => void;
  readOnly?: boolean;
}

export function ModuleLogicEditor({
  module,
  onUpdate,
  readOnly = false,
}: ModuleLogicEditorProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState(
    module.logic.nodes.map(node => ({
      id: node.id,
      type: 'logicNode',
      position: node.position,
      data: {
        ...node.data,
        nodeType: node.type,
        onUpdate: (updates: any) => handleNodeUpdate(node.id, updates),
        onDelete: () => handleDeleteNode(node.id),
        readOnly,
      },
    }))
  );

  const [edges, setEdges, onEdgesChange] = useEdgesState(
    module.logic.edges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      sourceHandle: edge.sourceHandle,
      targetHandle: edge.targetHandle,
      type: 'smoothstep',
      animated: true,
    }))
  );

  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isPaletteOpen, setIsPaletteOpen] = useState(false);

  // Update module when nodes or edges change
  const updateModuleLogic = useCallback((newNodes: Node[], newEdges: Edge[]) => {
    const logicNodes: LogicNode[] = newNodes.map(node => ({
      id: node.id,
      type: node.data.nodeType,
      position: node.position,
      data: {
        label: node.data.label,
        description: node.data.description,
        config: node.data.config || {},
        inputs: node.data.inputs || [],
        outputs: node.data.outputs || [],
      },
    }));

    const logicEdges: LogicEdge[] = newEdges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      sourceHandle: edge.sourceHandle || '',
      targetHandle: edge.targetHandle || '',
      type: edge.type,
      data: edge.data || {},
    }));

    onUpdate({
      logic: {
        ...module.logic,
        nodes: logicNodes,
        edges: logicEdges,
      },
    });
  }, [module.logic, onUpdate]);

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdges = addEdge(
        {
          ...params,
          type: 'smoothstep',
          animated: true,
        },
        edges
      );
      setEdges(newEdges);
      updateModuleLogic(nodes, newEdges);
    },
    [edges, nodes, setEdges, updateModuleLogic]
  );

  const handleAddNode = (nodeType: LogicNodeType, position: { x: number; y: number }) => {
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type: 'logicNode',
      position,
      data: {
        nodeType,
        label: getNodeTypeLabel(nodeType),
        description: getNodeTypeDescription(nodeType),
        config: getDefaultNodeConfig(nodeType),
        inputs: getNodeTypeInputs(nodeType),
        outputs: getNodeTypeOutputs(nodeType),
        onUpdate: (updates: any) => handleNodeUpdate(`node-${Date.now()}`, updates),
        onDelete: () => handleDeleteNode(`node-${Date.now()}`),
        readOnly,
      },
    };

    const newNodes = [...nodes, newNode];
    setNodes(newNodes);
    updateModuleLogic(newNodes, edges);
    setIsPaletteOpen(false);
  };

  const handleNodeUpdate = (nodeId: string, updates: any) => {
    const newNodes = nodes.map(node =>
      node.id === nodeId
        ? {
            ...node,
            data: { ...node.data, ...updates },
          }
        : node
    );
    setNodes(newNodes);
    updateModuleLogic(newNodes, edges);
  };

  const handleDeleteNode = (nodeId: string) => {
    const newNodes = nodes.filter(node => node.id !== nodeId);
    const newEdges = edges.filter(edge => edge.source !== nodeId && edge.target !== nodeId);
    setNodes(newNodes);
    setEdges(newEdges);
    updateModuleLogic(newNodes, newEdges);
    if (selectedNode === nodeId) {
      setSelectedNode(null);
    }
  };

  const handleNodeClick = (event: React.MouseEvent, node: Node) => {
    setSelectedNode(node.id);
  };

  const handlePaneClick = () => {
    setSelectedNode(null);
  };

  const handleTestLogic = () => {
    // Implement logic testing
    console.log("Testing module logic...");
  };

  const selectedNodeData = selectedNode ? nodes.find(n => n.id === selectedNode) : null;

  return (
    <div className="h-full flex">
      {/* Logic Flow Canvas */}
      <div className="flex-1 relative">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={handleNodeClick}
          onPaneClick={handlePaneClick}
          nodeTypes={nodeTypes}
          connectionMode={ConnectionMode.Loose}
          fitView
          className="bg-background"
        >
          <Background />
          <Controls />
          <MiniMap />
          
          {/* Toolbar */}
          <Panel position="top-left">
            <Card className="shadow-lg">
              <CardContent className="p-3">
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    onClick={() => setIsPaletteOpen(true)}
                    disabled={readOnly}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Node
                  </Button>
                  <Separator orientation="vertical" className="h-6" />
                  <Button variant="outline" size="sm" onClick={handleTestLogic}>
                    <Play className="h-4 w-4 mr-2" />
                    Test
                  </Button>
                  <Badge variant="outline">
                    {nodes.length} node{nodes.length !== 1 ? 's' : ''}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </Panel>

          {/* Empty State */}
          {nodes.length === 0 && (
            <Panel position="center">
              <Card className="w-80">
                <CardContent className="p-6 text-center">
                  <Workflow className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="font-medium mb-2">No Logic Defined</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Add nodes to define field computations, validations, and interactions.
                  </p>
                  {!readOnly && (
                    <Button onClick={() => setIsPaletteOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add First Node
                    </Button>
                  )}
                </CardContent>
              </Card>
            </Panel>
          )}
        </ReactFlow>

        {/* Node Palette */}
        <NodePalette
          isOpen={isPaletteOpen}
          onClose={() => setIsPaletteOpen(false)}
          onAddNode={handleAddNode}
          moduleFields={module.fields}
        />
      </div>

      {/* Node Configuration Panel */}
      {selectedNodeData && (
        <div className="w-80 border-l bg-muted/30">
          <NodeConfigPanel
            node={selectedNodeData}
            moduleFields={module.fields}
            onUpdate={(updates) => handleNodeUpdate(selectedNodeData.id, updates)}
            onDelete={() => handleDeleteNode(selectedNodeData.id)}
            readOnly={readOnly}
          />
        </div>
      )}
    </div>
  );
}

// Helper functions
function getNodeTypeLabel(type: LogicNodeType): string {
  const labels: Record<LogicNodeType, string> = {
    field_input: 'Field Input',
    field_output: 'Field Output',
    computation: 'Computation',
    validation: 'Validation',
    condition: 'Condition',
    transform: 'Transform',
    api_call: 'API Call',
    database_query: 'Database Query',
    constant: 'Constant',
    variable: 'Variable',
    function: 'Function',
  };
  return labels[type] || type;
}

function getNodeTypeDescription(type: LogicNodeType): string {
  const descriptions: Record<LogicNodeType, string> = {
    field_input: 'Read value from a field',
    field_output: 'Write value to a field',
    computation: 'Perform mathematical operations',
    validation: 'Validate field values',
    condition: 'Conditional logic branching',
    transform: 'Transform data format',
    api_call: 'Call external API',
    database_query: 'Query database',
    constant: 'Static value',
    variable: 'Store temporary value',
    function: 'Custom function',
  };
  return descriptions[type] || '';
}

function getDefaultNodeConfig(type: LogicNodeType): Record<string, any> {
  const configs: Record<LogicNodeType, Record<string, any>> = {
    field_input: { fieldId: '' },
    field_output: { fieldId: '' },
    computation: { operation: 'add', expression: '' },
    validation: { validationType: 'required', errorMessage: '' },
    condition: { operator: 'equals', value: '' },
    transform: { transformType: 'format', template: '' },
    api_call: { url: '', method: 'GET' },
    database_query: { query: '' },
    constant: { value: '', type: 'string' },
    variable: { name: '', type: 'string' },
    function: { name: '', parameters: [] },
  };
  return configs[type] || {};
}

function getNodeTypeInputs(type: LogicNodeType): any[] {
  const inputs: Record<LogicNodeType, any[]> = {
    field_input: [],
    field_output: [{ id: 'value', name: 'Value', type: 'any', required: true }],
    computation: [
      { id: 'a', name: 'A', type: 'number', required: true },
      { id: 'b', name: 'B', type: 'number', required: true },
    ],
    validation: [{ id: 'value', name: 'Value', type: 'any', required: true }],
    condition: [{ id: 'value', name: 'Value', type: 'any', required: true }],
    transform: [{ id: 'input', name: 'Input', type: 'any', required: true }],
    api_call: [{ id: 'data', name: 'Data', type: 'object', required: false }],
    database_query: [{ id: 'params', name: 'Parameters', type: 'object', required: false }],
    constant: [],
    variable: [{ id: 'value', name: 'Value', type: 'any', required: false }],
    function: [{ id: 'args', name: 'Arguments', type: 'array', required: false }],
  };
  return inputs[type] || [];
}

function getNodeTypeOutputs(type: LogicNodeType): any[] {
  const outputs: Record<LogicNodeType, any[]> = {
    field_input: [{ id: 'value', name: 'Value', type: 'any', required: false }],
    field_output: [],
    computation: [{ id: 'result', name: 'Result', type: 'number', required: false }],
    validation: [{ id: 'valid', name: 'Valid', type: 'boolean', required: false }],
    condition: [
      { id: 'true', name: 'True', type: 'any', required: false },
      { id: 'false', name: 'False', type: 'any', required: false },
    ],
    transform: [{ id: 'output', name: 'Output', type: 'any', required: false }],
    api_call: [{ id: 'response', name: 'Response', type: 'object', required: false }],
    database_query: [{ id: 'results', name: 'Results', type: 'array', required: false }],
    constant: [{ id: 'value', name: 'Value', type: 'any', required: false }],
    variable: [{ id: 'value', name: 'Value', type: 'any', required: false }],
    function: [{ id: 'result', name: 'Result', type: 'any', required: false }],
  };
  return outputs[type] || [];
}
