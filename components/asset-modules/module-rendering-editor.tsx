"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Layout, 
  Grid, 
  List, 
  Table, 
  Eye, 
  Settings,
  Plus,
  Trash2,
  GripVertical
} from "lucide-react";
import { AssetModule } from "@/lib/types/asset-modules";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";

interface ModuleRenderingEditorProps {
  module: AssetModule;
  onUpdate: (updates: Partial<AssetModule>) => void;
  readOnly?: boolean;
}

export function ModuleRenderingEditor({
  module,
  onUpdate,
  readOnly = false,
}: ModuleRenderingEditorProps) {
  const [activeTab, setActiveTab] = useState<'form' | 'display'>('form');

  const handleFormLayoutUpdate = (updates: any) => {
    onUpdate({
      rendering: {
        ...module.rendering,
        formLayout: {
          ...module.rendering.formLayout,
          ...updates,
        },
      },
    });
  };

  const handleDisplayLayoutUpdate = (updates: any) => {
    onUpdate({
      rendering: {
        ...module.rendering,
        displayLayout: {
          ...module.rendering.displayLayout,
          ...updates,
        },
      },
    });
  };

  const handleAddSection = () => {
    const newSection = {
      id: `section-${Date.now()}`,
      title: `Section ${module.rendering.formLayout.sections.length + 1}`,
      description: '',
      fields: [],
      collapsible: false,
      defaultExpanded: true,
    };

    handleFormLayoutUpdate({
      sections: [...module.rendering.formLayout.sections, newSection],
    });
  };

  const handleUpdateSection = (sectionId: string, updates: any) => {
    const updatedSections = module.rendering.formLayout.sections.map(section =>
      section.id === sectionId ? { ...section, ...updates } : section
    );
    handleFormLayoutUpdate({ sections: updatedSections });
  };

  const handleDeleteSection = (sectionId: string) => {
    const updatedSections = module.rendering.formLayout.sections.filter(
      section => section.id !== sectionId
    );
    handleFormLayoutUpdate({ sections: updatedSections });
  };

  const handleSectionDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(module.rendering.formLayout.sections);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    handleFormLayoutUpdate({ sections: items });
  };

  const handleAddDisplayView = () => {
    const newView = {
      id: `view-${Date.now()}`,
      name: `View ${module.rendering.displayLayout.views.length + 1}`,
      type: 'card' as const,
      fields: [],
      template: '',
    };

    handleDisplayLayoutUpdate({
      views: [...module.rendering.displayLayout.views, newView],
    });
  };

  const handleUpdateDisplayView = (viewId: string, updates: any) => {
    const updatedViews = module.rendering.displayLayout.views.map(view =>
      view.id === viewId ? { ...view, ...updates } : view
    );
    handleDisplayLayoutUpdate({ views: updatedViews });
  };

  const handleDeleteDisplayView = (viewId: string) => {
    const updatedViews = module.rendering.displayLayout.views.filter(
      view => view.id !== viewId
    );
    handleDisplayLayoutUpdate({ views: updatedViews });
  };

  const availableFields = module.fields.map(field => ({
    id: field.id,
    name: field.name,
    label: field.label,
  }));

  return (
    <div className="h-full">
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="h-full flex flex-col">
        <div className="border-b px-4">
          <TabsList>
            <TabsTrigger value="form" className="flex items-center gap-2">
              <Layout className="h-4 w-4" />
              Form Layout
            </TabsTrigger>
            <TabsTrigger value="display" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Display Views
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="flex-1 overflow-auto">
          <TabsContent value="form" className="h-full m-0">
            <FormLayoutEditor
              formLayout={module.rendering.formLayout}
              availableFields={availableFields}
              onUpdate={handleFormLayoutUpdate}
              onAddSection={handleAddSection}
              onUpdateSection={handleUpdateSection}
              onDeleteSection={handleDeleteSection}
              onSectionDragEnd={handleSectionDragEnd}
              readOnly={readOnly}
            />
          </TabsContent>

          <TabsContent value="display" className="h-full m-0">
            <DisplayLayoutEditor
              displayLayout={module.rendering.displayLayout}
              availableFields={availableFields}
              onUpdate={handleDisplayLayoutUpdate}
              onAddView={handleAddDisplayView}
              onUpdateView={handleUpdateDisplayView}
              onDeleteView={handleDeleteDisplayView}
              readOnly={readOnly}
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}

interface FormLayoutEditorProps {
  formLayout: any;
  availableFields: any[];
  onUpdate: (updates: any) => void;
  onAddSection: () => void;
  onUpdateSection: (sectionId: string, updates: any) => void;
  onDeleteSection: (sectionId: string) => void;
  onSectionDragEnd: (result: any) => void;
  readOnly?: boolean;
}

function FormLayoutEditor({
  formLayout,
  availableFields,
  onUpdate,
  onAddSection,
  onUpdateSection,
  onDeleteSection,
  onSectionDragEnd,
  readOnly = false,
}: FormLayoutEditorProps) {
  return (
    <div className="p-6 space-y-6">
      {/* Form Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Form Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Columns</Label>
              <Select
                value={formLayout.columns.toString()}
                onValueChange={(value) => onUpdate({ columns: parseInt(value) })}
                disabled={readOnly}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 Column</SelectItem>
                  <SelectItem value="2">2 Columns</SelectItem>
                  <SelectItem value="3">3 Columns</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Spacing</Label>
              <Select
                value={formLayout.spacing}
                onValueChange={(value) => onUpdate({ spacing: value })}
                disabled={readOnly}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="compact">Compact</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="spacious">Spacious</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sections */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center justify-between">
            Form Sections
            {!readOnly && (
              <Button size="sm" onClick={onAddSection}>
                <Plus className="h-4 w-4 mr-2" />
                Add Section
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {formLayout.sections.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Layout className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No sections defined</p>
              <p className="text-xs">Add sections to organize your form fields</p>
            </div>
          ) : (
            <DragDropContext onDragEnd={onSectionDragEnd}>
              <Droppable droppableId="sections">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                    {formLayout.sections.map((section: any, index: number) => (
                      <Draggable key={section.id} draggableId={section.id} index={index}>
                        {(provided) => (
                          <Card
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className="border-l-4 border-l-primary"
                          >
                            <CardHeader className="pb-3">
                              <div className="flex items-center gap-3">
                                <div
                                  {...provided.dragHandleProps}
                                  className="text-muted-foreground hover:text-foreground"
                                >
                                  <GripVertical className="h-4 w-4" />
                                </div>
                                <div className="flex-1">
                                  <Input
                                    value={section.title}
                                    onChange={(e) => onUpdateSection(section.id, { title: e.target.value })}
                                    className="font-medium"
                                    disabled={readOnly}
                                  />
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline">
                                    {section.fields?.length || 0} fields
                                  </Badge>
                                  {!readOnly && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => onDeleteSection(section.id)}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                              <div className="space-y-3">
                                <Input
                                  value={section.description || ''}
                                  onChange={(e) => onUpdateSection(section.id, { description: e.target.value })}
                                  placeholder="Section description (optional)"
                                  disabled={readOnly}
                                />
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-4">
                                    <div className="flex items-center gap-2">
                                      <Switch
                                        checked={section.collapsible}
                                        onCheckedChange={(checked) => onUpdateSection(section.id, { collapsible: checked })}
                                        disabled={readOnly}
                                      />
                                      <Label className="text-sm">Collapsible</Label>
                                    </div>
                                    {section.collapsible && (
                                      <div className="flex items-center gap-2">
                                        <Switch
                                          checked={section.defaultExpanded}
                                          onCheckedChange={(checked) => onUpdateSection(section.id, { defaultExpanded: checked })}
                                          disabled={readOnly}
                                        />
                                        <Label className="text-sm">Default Expanded</Label>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

interface DisplayLayoutEditorProps {
  displayLayout: any;
  availableFields: any[];
  onUpdate: (updates: any) => void;
  onAddView: () => void;
  onUpdateView: (viewId: string, updates: any) => void;
  onDeleteView: (viewId: string) => void;
  readOnly?: boolean;
}

function DisplayLayoutEditor({
  displayLayout,
  availableFields,
  onUpdate,
  onAddView,
  onUpdateView,
  onDeleteView,
  readOnly = false,
}: DisplayLayoutEditorProps) {
  const viewTypes = [
    { value: 'card', label: 'Card View', icon: Layout },
    { value: 'table', label: 'Table View', icon: Table },
    { value: 'list', label: 'List View', icon: List },
    { value: 'grid', label: 'Grid View', icon: Grid },
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Display Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Display Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label>Default View</Label>
            <Select
              value={displayLayout.defaultView}
              onValueChange={(value) => onUpdate({ defaultView: value })}
              disabled={readOnly}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {displayLayout.views.map((view: any) => (
                  <SelectItem key={view.id} value={view.id}>
                    {view.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Display Views */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center justify-between">
            Display Views
            {!readOnly && (
              <Button size="sm" onClick={onAddView}>
                <Plus className="h-4 w-4 mr-2" />
                Add View
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {displayLayout.views.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No display views defined</p>
              <p className="text-xs">Add views to customize how data is displayed</p>
            </div>
          ) : (
            <div className="space-y-4">
              {displayLayout.views.map((view: any) => (
                <Card key={view.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Input
                          value={view.name}
                          onChange={(e) => onUpdateView(view.id, { name: e.target.value })}
                          className="font-medium max-w-xs"
                          disabled={readOnly}
                        />
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{view.type}</Badge>
                          {!readOnly && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onDeleteView(view.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>

                      <div>
                        <Label>View Type</Label>
                        <Select
                          value={view.type}
                          onValueChange={(value) => onUpdateView(view.id, { type: value })}
                          disabled={readOnly}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {viewTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                <div className="flex items-center gap-2">
                                  <type.icon className="h-4 w-4" />
                                  {type.label}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>Fields to Display</Label>
                        <div className="mt-2 grid grid-cols-2 gap-2">
                          {availableFields.map((field) => (
                            <div key={field.id} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id={`${view.id}-${field.id}`}
                                checked={view.fields.includes(field.name)}
                                onChange={(e) => {
                                  const updatedFields = e.target.checked
                                    ? [...view.fields, field.name]
                                    : view.fields.filter((f: string) => f !== field.name);
                                  onUpdateView(view.id, { fields: updatedFields });
                                }}
                                disabled={readOnly}
                              />
                              <label
                                htmlFor={`${view.id}-${field.id}`}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                              >
                                {field.label}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
