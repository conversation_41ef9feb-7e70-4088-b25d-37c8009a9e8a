'use client'

import React, { useCallback } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { ThemeToggle } from "@/components/theme-toggle";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useAppHeaderStore, type HeaderVariant } from "@/store/app-header-store";
import { ArrowLeft, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

// Helper component for rendering breadcrumbs
const HeaderBreadcrumbs: React.FC<{ breadcrumbs: { label: string; url: string }[] }> = ({ breadcrumbs }) => (
  <Breadcrumb>
    <BreadcrumbList>
      {breadcrumbs.map((item, index) => (
        <React.Fragment key={item.url}>
          <BreadcrumbItem>
            {index === breadcrumbs.length - 1 ? (
              <BreadcrumbPage>{item.label}</BreadcrumbPage>
            ) : (
              <BreadcrumbLink asChild>
                <Link href={item.url}>{item.label}</Link>
              </BreadcrumbLink>
            )}
          </BreadcrumbItem>
          {index < breadcrumbs.length - 1 && (
            <BreadcrumbSeparator />
          )}
        </React.Fragment>
      ))}
    </BreadcrumbList>
  </Breadcrumb>
);

// Variant-specific styling
const getVariantStyles = (variant: HeaderVariant) => {
  switch (variant) {
    case 'dashboard':
      return {
        header: "bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-b border-blue-200/50 dark:border-blue-800/50",
        title: "text-blue-900 dark:text-blue-100",
        description: "text-blue-700 dark:text-blue-300",
      };
    case 'management':
      return {
        header: "bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-b border-green-200/50 dark:border-green-800/50",
        title: "text-green-900 dark:text-green-100",
        description: "text-green-700 dark:text-green-300",
      };
    case 'analytics':
      return {
        header: "bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 border-b border-purple-200/50 dark:border-purple-800/50",
        title: "text-purple-900 dark:text-purple-100",
        description: "text-purple-700 dark:text-purple-300",
      };
    case 'settings':
      return {
        header: "bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-950/20 dark:to-slate-950/20 border-b border-gray-200/50 dark:border-gray-800/50",
        title: "text-gray-900 dark:text-gray-100",
        description: "text-gray-700 dark:text-gray-300",
      };
    default:
      return {
        header: "border-b",
        title: "text-foreground",
        description: "text-muted-foreground",
      };
  }
};

// Variant badge component
const VariantBadge: React.FC<{ variant: HeaderVariant }> = ({ variant }) => {
  if (variant === 'default') return null;
  
  const badgeConfig = {
    dashboard: { label: "Dashboard", className: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" },
    management: { label: "Management", className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" },
    analytics: { label: "Analytics", className: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200" },
    settings: { label: "Settings", className: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200" },
  };

  const config = badgeConfig[variant];
  if (!config) return null;

  return (
    <Badge variant="secondary" className={cn("text-xs", config.className)}>
      {config.label}
    </Badge>
  );
};

export function AppHeader() {
  const router = useRouter();
  const [isRouterReady, setIsRouterReady] = useState(false);
  
  const {
    title,
    description,
    breadcrumbs,
    actions,
    variant = 'default',
    isLoading,
    showBackButton,
    backUrl,
    tabs,
    activeTab,
    showTabs
  } = useAppHeaderStore();

  useEffect(() => {
    // Check if router is available and ready
    if (router && typeof router.push === 'function' && typeof router.back === 'function') {
      setIsRouterReady(true);
    }
  }, [router]);

  const handleTabClick = useCallback((tabId: string) => {
    const { setActiveTab } = useAppHeaderStore.getState();
    setActiveTab(tabId);
  }, []);

  const styles = getVariantStyles(variant);

  const handleBackClick = () => {
    if (!router || !isRouterReady) {
      console.error('Router is not available or not ready');
      return;
    }
    
    try {
      if (backUrl) {
        router.push(backUrl);
      } else {
        router.back();
      }
    } catch (error) {
      console.error('Error navigating:', error);
      // Fallback to window.history if router fails
      if (typeof window !== 'undefined') {
        if (backUrl) {
          window.location.href = backUrl;
        } else {
          window.history.back();
        }
      }
    }
  };

  return (
    <header className={cn(
      "flex flex-col shrink-0 transition-all duration-200 ease-in-out",
      styles.header
    )}>
      {/* Main header row */}
      <div className="flex h-16 items-center gap-2 group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          
          {/* Back button */}
          {showBackButton && isRouterReady && (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackClick}
                className="mr-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <Separator orientation="vertical" className="mr-2 h-4" />
            </>
          )}
          
          {/* Loading state */}
          {isLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <Skeleton className="h-6 w-32" />
            </div>
          ) : (
            /* Conditionally render title or breadcrumbs */
            breadcrumbs ? (
              <HeaderBreadcrumbs breadcrumbs={breadcrumbs} />
            ) : title ? (
              <div className="flex items-center gap-2">
                <h1 className={cn("text-lg font-semibold", styles.title)}>{title}</h1>
                <VariantBadge variant={variant} />
              </div>
            ) : null
          )}
        </div>
        
        <div className="ml-auto px-4 flex items-center gap-2">
          {/* Render action buttons */}
          {!isLoading && actions && actions.map((action, index) => (
            <React.Fragment key={index}>{action}</React.Fragment>
          ))}
          <ThemeToggle />
        </div>
      </div>
      
      {/* Description row (if present) */}
      {description && !isLoading && (
        <div className="px-4 pb-3">
          <p className={cn("text-sm", styles.description)}>{description}</p>
        </div>
      )}



      {/* Integrated Tabs Section */}
      {showTabs && tabs && tabs.length > 0 && !isLoading && (
        <div className="border-t border-border/50">
          <div className="px-4">
            <div className="flex space-x-0 overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => !tab.disabled && handleTabClick(tab.id)}
                  disabled={tab.disabled}
                  className={cn(
                    "flex items-center gap-2 px-6 py-4 text-sm font-medium transition-all duration-200 border-b-2 whitespace-nowrap",
                    "hover:text-foreground focus:outline-none focus:text-foreground",
                    activeTab === tab.id
                      ? "border-primary text-foreground bg-background/50"
                      : "border-transparent text-muted-foreground hover:border-border",
                    tab.disabled && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {tab.icon && <tab.icon className="h-4 w-4" />}
                  <span>{tab.label}</span>
                  {tab.badge && (
                    <Badge
                      variant="secondary"
                      className="ml-1 h-5 min-w-[20px] px-1.5 text-xs"
                    >
                      {tab.badge}
                    </Badge>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </header>
  );
}