"use client";

import React, { useState, useEffect } from "react";
import { FormRenderer } from "./form-renderer";
import { FormDefinition } from "./form-builder";
import { CustomField } from "@/lib/modules/asset-types/types";
import { AssetOperationType, FormContext, FormSubmissionData } from "@/lib/types/asset-type-forms";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Loader2, AlertCircle, CheckCircle } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface AssetOperationFormRuntimeProps {
  assetTypeId: string;
  operationType: AssetOperationType;
  context: FormContext;
  onSubmit: (data: FormSubmissionData) => Promise<void>;
  onCancel?: () => void;
  initialValues?: Record<string, any>;
  readOnly?: boolean;
}

export function AssetOperationFormRuntime({
  assetTypeId,
  operationType,
  context,
  onSubmit,
  onCancel,
  initialValues = {},
  readOnly = false,
}: AssetOperationFormRuntimeProps) {
  const [form, setForm] = useState<FormDefinition | null>(null);
  const [fields, setFields] = useState<CustomField[]>([]);
  const [values, setValues] = useState<Record<string, any>>(initialValues);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Load form and fields
  useEffect(() => {
    loadFormData();
  }, [assetTypeId, operationType]);

  const loadFormData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load form definition for this asset type and operation
      const formResponse = await fetch(
        `/api/asset-type-forms?assetTypeId=${assetTypeId}&operationType=${operationType}`
      );

      if (!formResponse.ok) {
        throw new Error("Failed to load form configuration");
      }

      const formData = await formResponse.json();
      
      if (!formData.form) {
        throw new Error("No form configured for this operation");
      }

      setForm(formData.form);

      // Load available fields for this asset type
      const fieldsResponse = await fetch(`/api/asset-types/${assetTypeId}/fields`);
      
      if (fieldsResponse.ok) {
        const fieldsData = await fieldsResponse.json();
        setFields(fieldsData);
      }

    } catch (error) {
      console.error("Error loading form data:", error);
      setError(error instanceof Error ? error.message : "Failed to load form");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (formValues: Record<string, any>) => {
    if (!form) return;

    try {
      setIsSubmitting(true);
      setValidationErrors({});

      // Validate required fields
      const errors: Record<string, string> = {};
      
      form.sections.forEach(section => {
        section.fields.forEach(fieldId => {
          const field = fields.find(f => f.id === fieldId);
          if (field?.isRequired && (!formValues[fieldId] || formValues[fieldId] === "")) {
            errors[fieldId] = `${field.label} is required`;
          }
        });
      });

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields",
          variant: "destructive",
        });
        return;
      }

      // Prepare submission data
      const submissionData: FormSubmissionData = {
        formId: form.id,
        assetTypeId,
        operationType,
        data: formValues,
        context,
      };

      await onSubmit(submissionData);

      toast({
        title: "Success",
        description: "Form submitted successfully",
      });

    } catch (error) {
      console.error("Error submitting form:", error);
      toast({
        title: "Error",
        description: "Failed to submit form",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSaveDraft = async (formValues: Record<string, any>) => {
    // TODO: Implement draft saving
    console.log("Save draft:", formValues);
    toast({
      title: "Draft Saved",
      description: "Your progress has been saved",
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-muted-foreground">Loading form...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!form) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              No form is configured for this operation. Please contact your administrator.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Form Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {form.name}
                {readOnly && <Badge variant="outline">Read Only</Badge>}
              </CardTitle>
              {form.description && (
                <p className="text-sm text-muted-foreground mt-1">
                  {form.description}
                </p>
              )}
            </div>
            <Badge variant="secondary">{operationType}</Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Form Content */}
      <FormRenderer
        form={form}
        fields={fields}
        values={values}
        onChange={setValues}
        onSubmit={handleSubmit}
        onCancel={onCancel}
        onSaveDraft={form.settings.allowSaveAsDraft ? handleSaveDraft : undefined}
        readOnly={readOnly || isSubmitting}
        errors={validationErrors}
      />
    </div>
  );
}