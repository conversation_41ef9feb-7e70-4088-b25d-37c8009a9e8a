"use client";

import React, { useState, useEffect } from "react";
import { Drag<PERSON>rop<PERSON>ontext, Droppable, Draggable } from "@hello-pangea/dnd";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CustomField } from "@/lib/modules/asset-types/types";
import { FIELD_TYPES } from "@/lib/modules/custom-fields/constants";
import { CustomFieldEditor } from "@/components/custom-fields/custom-field-editor";
import { FormRenderer } from "./form-renderer";
import { FormSettings } from "./form-settings";
import { 
  Plus, 
  Trash2, 
  Copy, 
  Settings, 
  MoveUp, 
  MoveDown, 
  Eye, 
  Code, 
  Save, 
  Download, 
  Upload, 
  Layers, 
  Layout, 
  Columns, 
  Rows
} from "lucide-react";

export interface FormSection {
  id: string;
  title: string;
  description?: string;
  columns: number;
  fields: string[]; // Array of field IDs
}

export interface FormDefinition {
  id: string;
  name: string;
  description?: string;
  sections: FormSection[];
  settings: {
    submitButtonText: string;
    cancelButtonText: string;
    showProgressBar: boolean;
    allowSaveAsDraft: boolean;
    confirmOnCancel: boolean;
    layout: "standard" | "compact" | "spacious";
    labelPosition: "top" | "left" | "floating";
  };
  version?: number;
}

interface FormBuilderProps {
  initialForm?: FormDefinition;
  availableFields: CustomField[];
  onSave: (form: FormDefinition) => void;
  onPreview: (form: FormDefinition) => void;
  onExport: (form: FormDefinition) => void;
  onAddField: () => void;
}

export function FormBuilder({
  initialForm,
  availableFields,
  onSave,
  onPreview,
  onExport,
  onAddField,
}: FormBuilderProps) {
  const defaultForm: FormDefinition = {
    id: `form-${Date.now()}`,
    name: "New Form",
    description: "Form description",
    sections: [
      {
        id: `section-${Date.now()}`,
        title: "Section 1",
        description: "First section of the form",
        columns: 1,
        fields: [],
      },
    ],
    settings: {
      submitButtonText: "Submit",
      cancelButtonText: "Cancel",
      showProgressBar: true,
      allowSaveAsDraft: true,
      confirmOnCancel: true,
      layout: "standard",
      labelPosition: "top",
    },
  };

  const [form, setForm] = useState<FormDefinition>(initialForm || defaultForm);
  const [activeTab, setActiveTab] = useState("builder");
  const [activeSection, setActiveSection] = useState<string | null>(
    form.sections.length > 0 ? form.sections[0].id : null
  );
  const [previewData, setPreviewData] = useState<Record<string, any>>({});
  const [showFieldSelector, setShowFieldSelector] = useState(false);
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null);
  const [sectionTitle, setSectionTitle] = useState("");
  const [sectionDescription, setSectionDescription] = useState("");
  const [sectionColumns, setSectionColumns] = useState(1);

  // Get fields for the active section
  const getActiveSectionFields = (): CustomField[] => {
    if (!activeSection) return [];
    const section = form.sections.find((s) => s.id === activeSection);
    if (!section) return [];
    
    return section.fields
      .map((fieldId) => availableFields.find((f) => f.id === fieldId))
      .filter((f): f is CustomField => f !== undefined);
  };

  // Handle form name and description changes
  const handleFormMetadataChange = (key: keyof FormDefinition, value: string) => {
    setForm((prev) => ({ ...prev, [key]: value }));
  };

  // Handle section changes
  const handleSectionChange = (sectionId: string, key: keyof FormSection, value: any) => {
    setForm((prev) => ({
      ...prev,
      sections: prev.sections.map((section) =>
        section.id === sectionId ? { ...section, [key]: value } : section
      ),
    }));
  };

  // Add a new section
  const handleAddSection = () => {
    const newSection: FormSection = {
      id: `section-${Date.now()}`,
      title: `Section ${form.sections.length + 1}`,
      description: "",
      columns: 1,
      fields: [],
    };
    
    setForm((prev) => ({
      ...prev,
      sections: [...prev.sections, newSection],
    }));
    
    setActiveSection(newSection.id);
  };

  // Delete a section
  const handleDeleteSection = (sectionId: string) => {
    setForm((prev) => ({
      ...prev,
      sections: prev.sections.filter((section) => section.id !== sectionId),
    }));
    
    if (activeSection === sectionId) {
      setActiveSection(form.sections[0]?.id || null);
    }
  };

  // Start editing a section
  const handleEditSection = (sectionId: string) => {
    const section = form.sections.find((s) => s.id === sectionId);
    if (section) {
      setEditingSectionId(sectionId);
      setSectionTitle(section.title);
      setSectionDescription(section.description || "");
      setSectionColumns(section.columns);
    }
  };

  // Save section edits
  const handleSaveSectionEdit = () => {
    if (editingSectionId) {
      setForm((prev) => ({
        ...prev,
        sections: prev.sections.map((section) =>
          section.id === editingSectionId
            ? {
                ...section,
                title: sectionTitle,
                description: sectionDescription,
                columns: sectionColumns,
              }
            : section
        ),
      }));
      setEditingSectionId(null);
    }
  };

  // Cancel section edit
  const handleCancelSectionEdit = () => {
    setEditingSectionId(null);
  };

  // Add a field to a section
  const handleAddFieldToSection = (sectionId: string, fieldId: string) => {
    setForm((prev) => ({
      ...prev,
      sections: prev.sections.map((section) =>
        section.id === sectionId
          ? { ...section, fields: [...section.fields, fieldId] }
          : section
      ),
    }));
    setShowFieldSelector(false);
  };

  // Remove a field from a section
  const handleRemoveFieldFromSection = (sectionId: string, fieldId: string) => {
    setForm((prev) => ({
      ...prev,
      sections: prev.sections.map((section) =>
        section.id === sectionId
          ? { ...section, fields: section.fields.filter((id) => id !== fieldId) }
          : section
      ),
    }));
  };

  // Handle drag and drop reordering
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const { source, destination, type } = result;

    // Reordering sections
    if (type === "section") {
      const reorderedSections = Array.from(form.sections);
      const [removed] = reorderedSections.splice(source.index, 1);
      reorderedSections.splice(destination.index, 0, removed);

      setForm((prev) => ({
        ...prev,
        sections: reorderedSections,
      }));
      return;
    }

    // Reordering fields within a section
    if (type === "field") {
      const sectionId = source.droppableId;
      const section = form.sections.find((s) => s.id === sectionId);
      
      if (!section) return;
      
      const reorderedFields = Array.from(section.fields);
      const [removed] = reorderedFields.splice(source.index, 1);
      reorderedFields.splice(destination.index, 0, removed);

      setForm((prev) => ({
        ...prev,
        sections: prev.sections.map((s) =>
          s.id === sectionId ? { ...s, fields: reorderedFields } : s
        ),
      }));
    }
  };

  // Handle form settings changes
  const handleSettingsChange = (settings: FormDefinition["settings"]) => {
    setForm((prev) => ({
      ...prev,
      settings,
    }));
  };

  // Handle preview data changes
  const handlePreviewDataChange = (data: Record<string, any>) => {
    setPreviewData(data);
  };

  // Save the form
  const handleSaveForm = () => {
    onSave(form);
  };

  // Preview the form
  const handlePreviewForm = () => {
    onPreview(form);
  };

  // Export the form
  const handleExportForm = () => {
    onExport(form);
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center p-4 border-b bg-background sticky top-0 z-10">
        <div className="flex items-center space-x-4">
          <div className="flex space-x-2">
            <Input
              value={form.name}
              onChange={(e) => handleFormMetadataChange("name", e.target.value)}
              placeholder="Form name"
              className="w-64 h-9"
            />
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-9">
              <TabsList className="h-full">
                <TabsTrigger value="builder" className="h-full px-3">
                  <Layout className="mr-2 h-4 w-4" />
                  Builder
                </TabsTrigger>
                <TabsTrigger value="preview" className="h-full px-3">
                  <Eye className="mr-2 h-4 w-4" />
                  Preview
                </TabsTrigger>
                <TabsTrigger value="settings" className="h-full px-3">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={handlePreviewForm}>
            <Eye className="mr-1 h-4 w-4" />
            Preview
          </Button>
          <Button variant="outline" size="sm" onClick={handleExportForm}>
            <Download className="mr-1 h-4 w-4" />
            Export
          </Button>
          <Button size="sm" onClick={handleSaveForm}>
            <Save className="mr-1 h-4 w-4" />
            Save
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsContent value="builder" className="p-4 flex-1 overflow-auto h-full m-0">
            <div className="flex flex-col h-full space-y-4">
              <div className="flex justify-between items-center">
                <div className="space-y-2 w-full">
                  <Label htmlFor="form-description" className="text-sm">Description</Label>
                  <Textarea
                    id="form-description"
                    value={form.description || ""}
                    onChange={(e) => handleFormMetadataChange("description", e.target.value)}
                    placeholder="Enter form description"
                    rows={1}
                    className="resize-none"
                  />
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <h2 className="text-base font-medium">Form Sections</h2>
                <Button onClick={handleAddSection} size="sm" className="h-8">
                  <Plus className="mr-1 h-3 w-3" />
                  Add Section
                </Button>
              </div>

              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="sections" type="section">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-3 flex-1">
                      {form.sections.map((section, index) => (
                        <Draggable key={section.id} draggableId={section.id} index={index}>
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={`border rounded-lg ${
                                activeSection === section.id ? "border-primary" : "border-border"
                              }`}
                            >
                              <div
                                className="p-3 flex justify-between items-center bg-muted rounded-t-lg"
                                {...provided.dragHandleProps}
                              >
                                <div>
                                  <h3 className="font-medium text-sm">{section.title}</h3>
                                  {section.description && (
                                    <p className="text-xs text-muted-foreground">{section.description}</p>
                                  )}
                                </div>
                                <div className="flex space-x-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setActiveSection(section.id)}
                                    className="h-7 px-2"
                                  >
                                    {activeSection === section.id ? "Active" : "Edit"}
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEditSection(section.id)}
                                    className="h-7 w-7 p-0"
                                  >
                                    <Settings className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteSection(section.id)}
                                    className="h-7 w-7 p-0"
                                  >
                                    <Trash2 className="h-3 w-3 text-destructive" />
                                  </Button>
                                </div>
                              </div>

                          {activeSection === section.id && (
                            <div className="p-3">
                              <div className="flex justify-between items-center mb-3">
                                <h4 className="font-medium text-sm">Fields</h4>
                                <Dialog open={showFieldSelector} onOpenChange={setShowFieldSelector}>
                                  <DialogTrigger asChild>
                                    <Button size="sm" className="h-8">
                                      <Plus className="mr-1 h-3 w-3" />
                                      Add Field
                                    </Button>
                                  </DialogTrigger>
                                  <DialogContent className="sm:max-w-[700px]">
                                    <DialogHeader>
                                      <DialogTitle>Select Field to Add</DialogTitle>
                                    </DialogHeader>
                                    <div className="py-2">
                                      <div className="flex justify-between items-center mb-3">
                                        <h3 className="font-medium text-sm">Available Fields</h3>
                                        <Button size="sm" onClick={onAddField} className="h-8">
                                          <Plus className="mr-1 h-3 w-3" />
                                          Create New Field
                                        </Button>
                                      </div>
                                      <ScrollArea className="h-[400px]">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                          {availableFields.map((field) => (
                                            <div
                                              key={field.id}
                                              className="flex justify-between items-center p-2 border rounded-md hover:bg-muted"
                                            >
                                              <div>
                                                <div className="font-medium text-sm">{field.label}</div>
                                                <div className="text-xs text-muted-foreground flex items-center">
                                                  <Badge variant="outline" className="mr-1 text-xs px-1 h-5">
                                                    {field.type}
                                                  </Badge>
                                                  <span className="truncate max-w-[200px]">{field.description}</span>
                                                </div>
                                              </div>
                                              <Button
                                                size="sm"
                                                onClick={() =>
                                                  handleAddFieldToSection(section.id, field.id)
                                                }
                                                className="h-7 px-2"
                                              >
                                                Add
                                              </Button>
                                            </div>
                                          ))}
                                        </div>
                                      </ScrollArea>
                                    </div>
                                  </DialogContent>
                                </Dialog>
                              </div>

                              <Droppable droppableId={section.id} type="field">
                                {(provided) => (
                                  <div
                                    {...provided.droppableProps}
                                    ref={provided.innerRef}
                                    className={`grid grid-cols-${section.columns} gap-2 min-h-[80px]`}
                                  >
                                    {section.fields.length === 0 ? (
                                      <div className="col-span-full flex items-center justify-center h-20 border border-dashed rounded-md">
                                        <p className="text-xs text-muted-foreground">
                                          Drag fields here or click "Add Field"
                                        </p>
                                      </div>
                                    ) : (
                                      section.fields.map((fieldId, index) => {
                                        const field = availableFields.find((f) => f.id === fieldId);
                                        if (!field) return null;

                                        return (
                                          <Draggable
                                            key={fieldId}
                                            draggableId={fieldId}
                                            index={index}
                                          >
                                            {(provided) => (
                                              <div
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                                {...provided.dragHandleProps}
                                                className="border rounded-md p-2 bg-card"
                                              >
                                                <div className="flex justify-between items-start">
                                                  <div className="flex-1 pr-2">
                                                    <div className="font-medium text-sm">{field.label}</div>
                                                    <div className="text-xs text-muted-foreground flex items-center mt-1 flex-wrap gap-1">
                                                      <Badge variant="outline" className="px-1 h-5 text-xs">
                                                        {field.type}
                                                      </Badge>
                                                      {field.isRequired && (
                                                        <Badge className="bg-red-100 text-red-800 px-1 h-5 text-xs">
                                                          Required
                                                        </Badge>
                                                      )}
                                                    </div>
                                                    {field.description && (
                                                      <p className="text-xs text-muted-foreground mt-1 truncate">
                                                        {field.description}
                                                      </p>
                                                    )}
                                                  </div>
                                                  <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() =>
                                                      handleRemoveFieldFromSection(
                                                        section.id,
                                                        fieldId
                                                      )
                                                    }
                                                    className="h-6 w-6 p-0"
                                                  >
                                                    <Trash2 className="h-3 w-3 text-destructive" />
                                                  </Button>
                                                </div>
                                              </div>
                                            )}
                                          </Draggable>
                                        );
                                      })
                                    )}
                                    {provided.placeholder}
                                  </div>
                                )}
                              </Droppable>
                            </div>
                          )}
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>

            </div>
          </TabsContent>

          <TabsContent value="preview" className="p-4 flex-1 overflow-auto h-full m-0">
            <FormRenderer
              form={form}
              fields={availableFields}
              values={previewData}
              onChange={handlePreviewDataChange}
            />
          </TabsContent>

          <TabsContent value="settings" className="p-4 flex-1 overflow-auto h-full m-0">
            <FormSettings settings={form.settings} onChange={handleSettingsChange} />
          </TabsContent>
        </Tabs>
      </div>

      {/* Section Edit Dialog */}
      <Dialog open={editingSectionId !== null} onOpenChange={(open) => !open && handleCancelSectionEdit()}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Section</DialogTitle>
          </DialogHeader>
          <div className="space-y-3 py-2">
            <div className="space-y-2">
              <Label htmlFor="section-title" className="text-sm">Section Title</Label>
              <Input
                id="section-title"
                value={sectionTitle}
                onChange={(e) => setSectionTitle(e.target.value)}
                className="h-9"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="section-description" className="text-sm">Description</Label>
              <Textarea
                id="section-description"
                value={sectionDescription}
                onChange={(e) => setSectionDescription(e.target.value)}
                rows={2}
                className="resize-none"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="section-columns" className="text-sm">Columns</Label>
              <Select
                value={sectionColumns.toString()}
                onValueChange={(value) => setSectionColumns(parseInt(value))}
              >
                <SelectTrigger id="section-columns" className="h-9">
                  <SelectValue placeholder="Select columns" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 Column</SelectItem>
                  <SelectItem value="2">2 Columns</SelectItem>
                  <SelectItem value="3">3 Columns</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end space-x-2 pt-2">
              <Button variant="outline" size="sm" onClick={handleCancelSectionEdit}>
                Cancel
              </Button>
              <Button size="sm" onClick={handleSaveSectionEdit}>
                Save Changes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}