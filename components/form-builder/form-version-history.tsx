"use client";

import React, { useState, useEffect } from "react";
import { FormDefinition } from "./form-builder";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  History, 
  Clock, 
  User, 
  Eye, 
  RotateCcw,
  GitBranch,
  FileText,
  Calendar
} from "lucide-react";

interface FormVersion {
  id: string;
  version: number;
  name: string;
  description?: string;
  sections: any[];
  settings: any;
  createdAt: string;
  updatedAt: string;
  updatedBy?: string;
  changesSummary?: string;
}

interface FormVersionHistoryProps {
  formId: string;
  currentVersion: number;
  onRestoreVersion?: (version: FormVersion) => void;
  onPreviewVersion?: (version: FormVersion) => void;
}

export function FormVersionHistory({
  formId,
  currentVersion,
  onRestoreVersion,
  onPreviewVersion,
}: FormVersionHistoryProps) {
  const [versions, setVersions] = useState<FormVersion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedVersion, setSelectedVersion] = useState<FormVersion | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  useEffect(() => {
    loadVersionHistory();
  }, [formId]);

  const loadVersionHistory = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // For now, we'll simulate version history since we don't have a versions table
      // In a real implementation, you'd fetch from /api/form-definitions/{id}/versions
      const response = await fetch(`/api/form-definitions/${formId}`);
      if (!response.ok) {
        throw new Error("Failed to load form");
      }

      const form = await response.json();
      
      // Simulate version history - in reality this would come from a versions table
      const simulatedVersions: FormVersion[] = [
        {
          id: form.id,
          version: form.version || 1,
          name: form.name,
          description: form.description,
          sections: form.sections,
          settings: form.settings,
          createdAt: form.updatedAt || form.createdAt,
          updatedAt: form.updatedAt || form.createdAt,
          updatedBy: form.updatedBy || form.createdBy,
          changesSummary: "Current version",
        },
      ];

      setVersions(simulatedVersions);
    } catch (error) {
      console.error("Error loading version history:", error);
      setError("Failed to load version history");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestoreVersion = (version: FormVersion) => {
    if (onRestoreVersion) {
      onRestoreVersion(version);
    }
  };

  const handlePreviewVersion = (version: FormVersion) => {
    setSelectedVersion(version);
    setIsPreviewOpen(true);
    if (onPreviewVersion) {
      onPreviewVersion(version);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getChangesSummary = (version: FormVersion) => {
    if (version.changesSummary) {
      return version.changesSummary;
    }
    
    // Generate a basic summary based on sections count
    const sectionCount = version.sections?.length || 0;
    return `${sectionCount} section${sectionCount !== 1 ? 's' : ''}`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading version history...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center text-destructive">
        <p>{error}</p>
        <Button variant="outline" size="sm" onClick={loadVersionHistory} className="mt-2">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <History className="h-4 w-4" />
        <h3 className="font-medium">Version History</h3>
        <Badge variant="outline">{versions.length} version{versions.length !== 1 ? 's' : ''}</Badge>
      </div>

      <ScrollArea className="h-[400px]">
        <div className="space-y-3">
          {versions.map((version, index) => (
            <Card key={version.id} className={version.version === currentVersion ? "border-primary" : ""}>
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant={version.version === currentVersion ? "default" : "secondary"}>
                        v{version.version}
                      </Badge>
                      {version.version === currentVersion && (
                        <Badge variant="outline">Current</Badge>
                      )}
                    </div>
                    
                    <div className="space-y-1">
                      <p className="font-medium text-sm">{version.name}</p>
                      {version.description && (
                        <p className="text-xs text-muted-foreground">{version.description}</p>
                      )}
                      <p className="text-xs text-muted-foreground">
                        {getChangesSummary(version)}
                      </p>
                    </div>

                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(version.updatedAt)}</span>
                      </div>
                      {version.updatedBy && (
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <span>{version.updatedBy}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreviewVersion(version)}
                    >
                      <Eye className="h-3 w-3" />
                    </Button>
                    {version.version !== currentVersion && onRestoreVersion && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRestoreVersion(version)}
                      >
                        <RotateCcw className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </ScrollArea>

      {/* Version Preview Dialog */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>
              Version {selectedVersion?.version} Preview - {selectedVersion?.name}
            </DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-[60vh]">
            {selectedVersion && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Version:</span> {selectedVersion.version}
                  </div>
                  <div>
                    <span className="font-medium">Updated:</span> {formatDate(selectedVersion.updatedAt)}
                  </div>
                  <div>
                    <span className="font-medium">Updated By:</span> {selectedVersion.updatedBy || 'Unknown'}
                  </div>
                  <div>
                    <span className="font-medium">Sections:</span> {selectedVersion.sections?.length || 0}
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="font-medium mb-2">Form Structure</h4>
                  <div className="space-y-2">
                    {selectedVersion.sections?.map((section: any, index: number) => (
                      <div key={section.id || index} className="p-3 border rounded">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span className="font-medium">{section.title}</span>
                          <Badge variant="outline">{section.fields?.length || 0} fields</Badge>
                        </div>
                        {section.description && (
                          <p className="text-sm text-muted-foreground mt-1">{section.description}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </div>
  );
}
