"use client";

import React, { useState, useEffect, useMemo } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  Save, 
  Play, 
  Eye, 
  Settings, 
  Layers, 
  Code, 
  TestTube,
  AlertCircle,
  CheckCircle,
  Clock,
  Workflow,
  FileText,
  Palette,
  Shield
} from "lucide-react";
import { AssetModule, ModuleBuilderState, ModuleError, ModuleWarning } from "@/lib/types/asset-modules";
import { ModuleFieldDesigner } from "./module-field-designer";
import { ModuleLogicEditor } from "./module-logic-editor";
import { ModuleRenderingEditor } from "./module-rendering-editor";
import { ModuleValidationEditor } from "./module-validation-editor";
import { ModulePreview } from "./module-preview";
import { ModuleMetadataEditor } from "./module-metadata-editor";
import { toast } from "@/components/ui/use-toast";

interface AssetModulePlaygroundProps {
  moduleId?: string;
  onSave?: (module: AssetModule) => void;
  onClose?: () => void;
  readOnly?: boolean;
}

export function AssetModulePlayground({
  moduleId,
  onSave,
  onClose,
  readOnly = false,
}: AssetModulePlaygroundProps) {
  const [builderState, setBuilderState] = useState<ModuleBuilderState>({
    module: createDefaultModule(),
    activeTab: 'fields',
    isDirty: false,
    errors: [],
    warnings: [],
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Load module if editing existing
  useEffect(() => {
    if (moduleId) {
      loadModule(moduleId);
    }
  }, [moduleId]);

  const loadModule = async (id: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/asset-modules/${id}`);
      if (!response.ok) {
        throw new Error("Failed to load module");
      }
      const module = await response.json();
      setBuilderState(prev => ({
        ...prev,
        module,
        isDirty: false,
      }));
    } catch (error) {
      console.error("Error loading module:", error);
      toast({
        title: "Error",
        description: "Failed to load module.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleModuleUpdate = (updates: Partial<AssetModule>) => {
    setBuilderState(prev => ({
      ...prev,
      module: { ...prev.module, ...updates },
      isDirty: true,
    }));
  };

  const handleTabChange = (tab: string) => {
    setBuilderState(prev => ({
      ...prev,
      activeTab: tab as any,
    }));
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      
      // Validate module before saving
      const validationResult = validateModule(builderState.module);
      if (validationResult.errors.length > 0) {
        setBuilderState(prev => ({
          ...prev,
          errors: validationResult.errors,
          warnings: validationResult.warnings,
        }));
        toast({
          title: "Validation Failed",
          description: "Please fix the errors before saving.",
          variant: "destructive",
        });
        return;
      }

      // Save module
      const method = moduleId ? "PUT" : "POST";
      const url = moduleId ? `/api/asset-modules/${moduleId}` : "/api/asset-modules";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(builderState.module),
      });

      if (!response.ok) {
        throw new Error("Failed to save module");
      }

      const savedModule = await response.json();
      
      setBuilderState(prev => ({
        ...prev,
        module: savedModule,
        isDirty: false,
        errors: [],
        warnings: validationResult.warnings,
      }));

      toast({
        title: "Success",
        description: "Module saved successfully.",
      });

      if (onSave) {
        onSave(savedModule);
      }

    } catch (error) {
      console.error("Error saving module:", error);
      toast({
        title: "Error",
        description: "Failed to save module.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleTest = () => {
    // Test module functionality
    const testResults = testModule(builderState.module);
    console.log("Test results:", testResults);
    toast({
      title: "Module Test",
      description: "Module testing completed. Check console for details.",
    });
  };

  const handlePreview = () => {
    setBuilderState(prev => ({
      ...prev,
      activeTab: 'preview',
    }));
  };

  const getTabIcon = (tab: string) => {
    const icons = {
      fields: Layers,
      logic: Workflow,
      rendering: Palette,
      validation: Shield,
      preview: Eye,
    };
    return icons[tab as keyof typeof icons] || FileText;
  };

  const getStatusColor = () => {
    if (builderState.errors.length > 0) return "text-red-600";
    if (builderState.warnings.length > 0) return "text-yellow-600";
    return "text-green-600";
  };

  const getStatusIcon = () => {
    if (builderState.errors.length > 0) return AlertCircle;
    if (builderState.warnings.length > 0) return AlertCircle;
    return CheckCircle;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading module...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b bg-background p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div>
              <h1 className="text-xl font-semibold">{builderState.module.name}</h1>
              <p className="text-sm text-muted-foreground">
                {builderState.module.description || "Asset Module Development"}
              </p>
            </div>
            <Badge variant={builderState.module.isActive ? "default" : "secondary"}>
              {builderState.module.isActive ? "Active" : "Draft"}
            </Badge>
            {builderState.isDirty && (
              <Badge variant="outline" className="text-orange-600">
                Unsaved Changes
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            {/* Status Indicator */}
            <div className={`flex items-center gap-1 ${getStatusColor()}`}>
              {React.createElement(getStatusIcon(), { className: "h-4 w-4" })}
              <span className="text-sm">
                {builderState.errors.length > 0 
                  ? `${builderState.errors.length} error${builderState.errors.length !== 1 ? 's' : ''}`
                  : builderState.warnings.length > 0
                  ? `${builderState.warnings.length} warning${builderState.warnings.length !== 1 ? 's' : ''}`
                  : "Ready"
                }
              </span>
            </div>

            <Separator orientation="vertical" className="h-6" />

            {/* Actions */}
            <Button variant="outline" size="sm" onClick={handleTest}>
              <TestTube className="h-4 w-4 mr-2" />
              Test
            </Button>
            <Button variant="outline" size="sm" onClick={handlePreview}>
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
            {!readOnly && (
              <Button 
                size="sm" 
                onClick={handleSave}
                disabled={isSaving || builderState.errors.length > 0}
              >
                {isSaving ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </>
                )}
              </Button>
            )}
            {onClose && (
              <Button variant="outline" size="sm" onClick={onClose}>
                Close
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Error/Warning Display */}
      {(builderState.errors.length > 0 || builderState.warnings.length > 0) && (
        <div className="p-4 space-y-2">
          {builderState.errors.map((error) => (
            <Alert key={error.id} variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error.message}</AlertDescription>
            </Alert>
          ))}
          {builderState.warnings.map((warning) => (
            <Alert key={warning.id}>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{warning.message}</AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={builderState.activeTab} onValueChange={handleTabChange} className="h-full flex flex-col">
          <div className="border-b px-4">
            <TabsList className="h-12">
              <TabsTrigger value="fields" className="flex items-center gap-2">
                <Layers className="h-4 w-4" />
                Fields
              </TabsTrigger>
              <TabsTrigger value="logic" className="flex items-center gap-2">
                <Workflow className="h-4 w-4" />
                Logic
              </TabsTrigger>
              <TabsTrigger value="rendering" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Rendering
              </TabsTrigger>
              <TabsTrigger value="validation" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Validation
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Preview
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-auto">
            <TabsContent value="fields" className="h-full m-0">
              <ModuleFieldDesigner
                module={builderState.module}
                onUpdate={handleModuleUpdate}
                readOnly={readOnly}
              />
            </TabsContent>

            <TabsContent value="logic" className="h-full m-0">
              <ModuleLogicEditor
                module={builderState.module}
                onUpdate={handleModuleUpdate}
                readOnly={readOnly}
              />
            </TabsContent>

            <TabsContent value="rendering" className="h-full m-0">
              <ModuleRenderingEditor
                module={builderState.module}
                onUpdate={handleModuleUpdate}
                readOnly={readOnly}
              />
            </TabsContent>

            <TabsContent value="validation" className="h-full m-0">
              <ModuleValidationEditor
                module={builderState.module}
                onUpdate={handleModuleUpdate}
                readOnly={readOnly}
              />
            </TabsContent>

            <TabsContent value="preview" className="h-full m-0">
              <ModulePreview
                module={builderState.module}
              />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}

// Helper Functions
function createDefaultModule(): AssetModule {
  return {
    id: `module-${Date.now()}`,
    name: "New Asset Module",
    version: "1.0.0",
    description: "",
    category: "custom",
    author: "current-user", // TODO: Get from auth context
    tags: [],
    fields: [],
    logic: {
      nodes: [],
      edges: [],
      variables: [],
      functions: [],
    },
    rendering: {
      formLayout: {
        sections: [],
        columns: 1,
        spacing: "normal",
        grouping: [],
      },
      displayLayout: {
        views: [],
        defaultView: "card",
      },
      components: [],
    },
    validation: {
      rules: [],
      crossFieldValidation: [],
    },
    isActive: false,
    isBuiltIn: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    usageCount: 0,
    dependencies: [],
    compatibleAssetTypes: [],
    requiredPermissions: [],
  };
}

function validateModule(module: AssetModule): { errors: ModuleError[]; warnings: ModuleWarning[] } {
  const errors: ModuleError[] = [];
  const warnings: ModuleWarning[] = [];

  // Basic validation
  if (!module.name.trim()) {
    errors.push({
      id: "name-required",
      type: "field",
      message: "Module name is required",
    });
  }

  if (module.fields.length === 0) {
    warnings.push({
      id: "no-fields",
      type: "usability",
      message: "Module has no fields defined",
      suggestion: "Add at least one field to make the module useful",
    });
  }

  // Field validation
  module.fields.forEach((field, index) => {
    if (!field.name.trim()) {
      errors.push({
        id: `field-${index}-name`,
        type: "field",
        message: `Field ${index + 1} name is required`,
        field: field.id,
      });
    }
  });

  return { errors, warnings };
}

function testModule(module: AssetModule): any {
  // Implement module testing logic
  return {
    fieldsTest: "passed",
    logicTest: "passed",
    renderingTest: "passed",
    validationTest: "passed",
  };
}
