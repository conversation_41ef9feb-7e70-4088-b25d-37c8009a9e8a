"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Plus, 
  Trash2, 
  Edit, 
  GripVertical, 
  Settings, 
  Eye,
  Type,
  Hash,
  Calendar,
  ToggleLeft,
  List,
  FileText,
  Image,
  MapPin,
  DollarSign,
  Star,
  Code,
  Lock,
  Mail,
  Phone,
  Link
} from "lucide-react";
import { AssetModule, ModuleField, ModuleFieldType } from "@/lib/types/asset-modules";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";

interface ModuleFieldDesignerProps {
  module: AssetModule;
  onUpdate: (updates: Partial<AssetModule>) => void;
  readOnly?: boolean;
}

export function ModuleFieldDesigner({
  module,
  onUpdate,
  readOnly = false,
}: ModuleFieldDesignerProps) {
  const [selectedField, setSelectedField] = useState<string | null>(null);
  const [isAddingField, setIsAddingField] = useState(false);

  const handleAddField = () => {
    const newField: ModuleField = {
      id: `field-${Date.now()}`,
      name: `field_${module.fields.length + 1}`,
      label: `Field ${module.fields.length + 1}`,
      type: 'text',
      isRequired: false,
      isUnique: false,
      validation: {},
      uiConfig: { width: 'full' },
      dependsOn: [],
      order: module.fields.length,
    };

    onUpdate({
      fields: [...module.fields, newField],
    });

    setSelectedField(newField.id);
    setIsAddingField(false);
  };

  const handleUpdateField = (fieldId: string, updates: Partial<ModuleField>) => {
    const updatedFields = module.fields.map(field =>
      field.id === fieldId ? { ...field, ...updates } : field
    );
    onUpdate({ fields: updatedFields });
  };

  const handleDeleteField = (fieldId: string) => {
    const updatedFields = module.fields.filter(field => field.id !== fieldId);
    onUpdate({ fields: updatedFields });
    if (selectedField === fieldId) {
      setSelectedField(null);
    }
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(module.fields);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order property
    const updatedFields = items.map((field, index) => ({
      ...field,
      order: index,
    }));

    onUpdate({ fields: updatedFields });
  };

  const getFieldTypeIcon = (type: ModuleFieldType) => {
    const iconMap: Record<ModuleFieldType, React.ElementType> = {
      text: Type,
      number: Hash,
      email: Mail,
      url: Link,
      phone: Phone,
      date: Calendar,
      datetime: Calendar,
      boolean: ToggleLeft,
      select: List,
      multiselect: List,
      textarea: FileText,
      file: FileText,
      image: Image,
      json: Code,
      address: MapPin,
      coordinates: MapPin,
      currency: DollarSign,
      percentage: Hash,
      rating: Star,
      color: Settings,
      code: Code,
      markdown: FileText,
      encrypted: Lock,
    };
    
    const IconComponent = iconMap[type] || Type;
    return <IconComponent className="h-4 w-4" />;
  };

  const selectedFieldData = selectedField ? module.fields.find(f => f.id === selectedField) : null;

  return (
    <div className="h-full flex">
      {/* Fields List */}
      <div className="w-1/3 border-r bg-muted/30">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Fields</h3>
            {!readOnly && (
              <Button size="sm" onClick={handleAddField}>
                <Plus className="h-4 w-4 mr-2" />
                Add Field
              </Button>
            )}
          </div>
        </div>

        <ScrollArea className="h-[calc(100%-80px)]">
          <div className="p-4">
            {module.fields.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Type className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No fields defined</p>
                <p className="text-xs">Add fields to build your module</p>
              </div>
            ) : (
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="fields">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-2">
                      {module.fields
                        .sort((a, b) => a.order - b.order)
                        .map((field, index) => (
                          <Draggable key={field.id} draggableId={field.id} index={index}>
                            {(provided, snapshot) => (
                              <Card
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className={`cursor-pointer transition-all ${
                                  selectedField === field.id
                                    ? "ring-2 ring-primary border-primary"
                                    : "hover:border-primary/50"
                                } ${snapshot.isDragging ? "shadow-lg" : ""}`}
                                onClick={() => setSelectedField(field.id)}
                              >
                                <CardContent className="p-3">
                                  <div className="flex items-center gap-3">
                                    <div
                                      {...provided.dragHandleProps}
                                      className="text-muted-foreground hover:text-foreground"
                                    >
                                      <GripVertical className="h-4 w-4" />
                                    </div>
                                    
                                    <div className="flex items-center gap-2 flex-1">
                                      {getFieldTypeIcon(field.type)}
                                      <div className="flex-1">
                                        <p className="font-medium text-sm">{field.label}</p>
                                        <p className="text-xs text-muted-foreground">{field.name}</p>
                                      </div>
                                    </div>

                                    <div className="flex items-center gap-1">
                                      {field.isRequired && (
                                        <Badge variant="secondary" className="text-xs">Required</Badge>
                                      )}
                                      <Badge variant="outline" className="text-xs">{field.type}</Badge>
                                    </div>

                                    {!readOnly && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDeleteField(field.id);
                                        }}
                                      >
                                        <Trash2 className="h-3 w-3" />
                                      </Button>
                                    )}
                                  </div>
                                </CardContent>
                              </Card>
                            )}
                          </Draggable>
                        ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Field Configuration */}
      <div className="flex-1">
        {selectedFieldData ? (
          <FieldConfigurationPanel
            field={selectedFieldData}
            onUpdate={(updates) => handleUpdateField(selectedFieldData.id, updates)}
            readOnly={readOnly}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Select a field to configure</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

interface FieldConfigurationPanelProps {
  field: ModuleField;
  onUpdate: (updates: Partial<ModuleField>) => void;
  readOnly?: boolean;
}

function FieldConfigurationPanel({
  field,
  onUpdate,
  readOnly = false,
}: FieldConfigurationPanelProps) {
  const fieldTypes: { value: ModuleFieldType; label: string; description: string }[] = [
    { value: 'text', label: 'Text', description: 'Single line text input' },
    { value: 'textarea', label: 'Textarea', description: 'Multi-line text input' },
    { value: 'number', label: 'Number', description: 'Numeric input' },
    { value: 'email', label: 'Email', description: 'Email address input' },
    { value: 'url', label: 'URL', description: 'Web address input' },
    { value: 'phone', label: 'Phone', description: 'Phone number input' },
    { value: 'date', label: 'Date', description: 'Date picker' },
    { value: 'datetime', label: 'Date & Time', description: 'Date and time picker' },
    { value: 'boolean', label: 'Boolean', description: 'True/false toggle' },
    { value: 'select', label: 'Select', description: 'Dropdown selection' },
    { value: 'multiselect', label: 'Multi-Select', description: 'Multiple selection' },
    { value: 'file', label: 'File', description: 'File upload' },
    { value: 'image', label: 'Image', description: 'Image upload' },
    { value: 'address', label: 'Address', description: 'Address input with validation' },
    { value: 'coordinates', label: 'Coordinates', description: 'GPS coordinates picker' },
    { value: 'currency', label: 'Currency', description: 'Money amount input' },
    { value: 'percentage', label: 'Percentage', description: 'Percentage input' },
    { value: 'rating', label: 'Rating', description: 'Star rating input' },
    { value: 'color', label: 'Color', description: 'Color picker' },
    { value: 'code', label: 'Code', description: 'Code editor' },
    { value: 'markdown', label: 'Markdown', description: 'Markdown editor' },
    { value: 'json', label: 'JSON', description: 'JSON data input' },
    { value: 'encrypted', label: 'Encrypted', description: 'Encrypted text storage' },
  ];

  const widthOptions = [
    { value: 'full', label: 'Full Width' },
    { value: 'half', label: 'Half Width' },
    { value: 'third', label: 'One Third' },
    { value: 'quarter', label: 'One Quarter' },
  ];

  return (
    <ScrollArea className="h-full">
      <div className="p-6 space-y-6">
        {/* Basic Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Basic Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="field-name">Field Name</Label>
                <Input
                  id="field-name"
                  value={field.name}
                  onChange={(e) => onUpdate({ name: e.target.value })}
                  placeholder="field_name"
                  disabled={readOnly}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Used in database and API (snake_case recommended)
                </p>
              </div>
              <div>
                <Label htmlFor="field-label">Display Label</Label>
                <Input
                  id="field-label"
                  value={field.label}
                  onChange={(e) => onUpdate({ label: e.target.value })}
                  placeholder="Field Label"
                  disabled={readOnly}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="field-description">Description</Label>
              <Textarea
                id="field-description"
                value={field.description || ''}
                onChange={(e) => onUpdate({ description: e.target.value })}
                placeholder="Field description (optional)"
                rows={2}
                disabled={readOnly}
              />
            </div>

            <div>
              <Label htmlFor="field-type">Field Type</Label>
              <Select
                value={field.type}
                onValueChange={(value: ModuleFieldType) => onUpdate({ type: value })}
                disabled={readOnly}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {fieldTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center gap-2">
                        {React.createElement(
                          {
                            text: Type,
                            number: Hash,
                            email: Mail,
                            url: Link,
                            phone: Phone,
                            date: Calendar,
                            datetime: Calendar,
                            boolean: ToggleLeft,
                            select: List,
                            multiselect: List,
                            textarea: FileText,
                            file: FileText,
                            image: Image,
                            json: Code,
                            address: MapPin,
                            coordinates: MapPin,
                            currency: DollarSign,
                            percentage: Hash,
                            rating: Star,
                            color: Settings,
                            code: Code,
                            markdown: FileText,
                            encrypted: Lock,
                          }[type.value] || Type,
                          { className: "h-4 w-4" }
                        )}
                        <div>
                          <div className="font-medium">{type.label}</div>
                          <div className="text-xs text-muted-foreground">{type.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Field Properties */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Field Properties</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label>Required Field</Label>
                <p className="text-xs text-muted-foreground">Field must have a value</p>
              </div>
              <Switch
                checked={field.isRequired}
                onCheckedChange={(checked) => onUpdate({ isRequired: checked })}
                disabled={readOnly}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label>Unique Field</Label>
                <p className="text-xs text-muted-foreground">Value must be unique across assets</p>
              </div>
              <Switch
                checked={field.isUnique}
                onCheckedChange={(checked) => onUpdate({ isUnique: checked })}
                disabled={readOnly}
              />
            </div>

            <div>
              <Label htmlFor="default-value">Default Value</Label>
              <Input
                id="default-value"
                value={field.defaultValue || ''}
                onChange={(e) => onUpdate({ defaultValue: e.target.value })}
                placeholder="Default value (optional)"
                disabled={readOnly}
              />
            </div>

            <div>
              <Label htmlFor="placeholder">Placeholder Text</Label>
              <Input
                id="placeholder"
                value={field.placeholder || ''}
                onChange={(e) => onUpdate({ placeholder: e.target.value })}
                placeholder="Placeholder text (optional)"
                disabled={readOnly}
              />
            </div>

            <div>
              <Label htmlFor="help-text">Help Text</Label>
              <Input
                id="help-text"
                value={field.helpText || ''}
                onChange={(e) => onUpdate({ helpText: e.target.value })}
                placeholder="Help text (optional)"
                disabled={readOnly}
              />
            </div>
          </CardContent>
        </Card>

        {/* UI Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">UI Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Field Width</Label>
              <Select
                value={field.uiConfig.width}
                onValueChange={(value: any) => onUpdate({ 
                  uiConfig: { ...field.uiConfig, width: value }
                })}
                disabled={readOnly}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {widthOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="field-group">Field Group</Label>
              <Input
                id="field-group"
                value={field.group || ''}
                onChange={(e) => onUpdate({ group: e.target.value })}
                placeholder="Group name (optional)"
                disabled={readOnly}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Group related fields together visually
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Validation Rules */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Validation Rules</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {(field.type === 'text' || field.type === 'textarea') && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="min-length">Min Length</Label>
                  <Input
                    id="min-length"
                    type="number"
                    value={field.validation.minLength || ''}
                    onChange={(e) => onUpdate({
                      validation: {
                        ...field.validation,
                        minLength: e.target.value ? parseInt(e.target.value) : undefined
                      }
                    })}
                    disabled={readOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="max-length">Max Length</Label>
                  <Input
                    id="max-length"
                    type="number"
                    value={field.validation.maxLength || ''}
                    onChange={(e) => onUpdate({
                      validation: {
                        ...field.validation,
                        maxLength: e.target.value ? parseInt(e.target.value) : undefined
                      }
                    })}
                    disabled={readOnly}
                  />
                </div>
              </div>
            )}

            {field.type === 'number' && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="min-value">Min Value</Label>
                  <Input
                    id="min-value"
                    type="number"
                    value={field.validation.min || ''}
                    onChange={(e) => onUpdate({
                      validation: {
                        ...field.validation,
                        min: e.target.value ? parseFloat(e.target.value) : undefined
                      }
                    })}
                    disabled={readOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="max-value">Max Value</Label>
                  <Input
                    id="max-value"
                    type="number"
                    value={field.validation.max || ''}
                    onChange={(e) => onUpdate({
                      validation: {
                        ...field.validation,
                        max: e.target.value ? parseFloat(e.target.value) : undefined
                      }
                    })}
                    disabled={readOnly}
                  />
                </div>
              </div>
            )}

            <div>
              <Label htmlFor="pattern">Pattern (Regex)</Label>
              <Input
                id="pattern"
                value={field.validation.pattern || ''}
                onChange={(e) => onUpdate({
                  validation: {
                    ...field.validation,
                    pattern: e.target.value
                  }
                })}
                placeholder="Regular expression pattern"
                disabled={readOnly}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  );
}
