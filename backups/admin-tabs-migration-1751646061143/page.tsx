"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Download, FileText, TrendingDown, DollarSign, Wrench } from "lucide-react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getReportsHeaderConfig } from "@/lib/utils/admin-header-configs"

// Mock report data
const reports = [
  {
    id: "RPT-001",
    name: "Asset Depreciation Report",
    type: "Financial",
    generatedDate: "2024-01-10",
    status: "Ready",
    description: "Monthly depreciation analysis for all assets",
  },
  {
    id: "RPT-002",
    name: "Maintenance Cost Analysis",
    type: "Maintenance",
    generatedDate: "2024-01-08",
    status: "Ready",
    description: "Quarterly maintenance cost breakdown by category",
  },
  {
    id: "RPT-003",
    name: "Asset Utilization Report",
    type: "Operational",
    generatedDate: "2024-01-05",
    status: "Ready",
    description: "Asset usage and efficiency metrics",
  },
  {
    id: "RPT-004",
    name: "Compliance Audit Report",
    type: "Compliance",
    generatedDate: "2024-01-03",
    status: "Processing",
    description: "Annual compliance and regulatory audit",
  },
]

const depreciationData = [
  { category: "IT Equipment", originalValue: 125000, currentValue: 87500, depreciation: 37500, rate: "30%" },
  { category: "Machinery", originalValue: 450000, currentValue: 360000, depreciation: 90000, rate: "20%" },
  { category: "Furniture", originalValue: 75000, currentValue: 67500, depreciation: 7500, rate: "10%" },
  { category: "Vehicles", originalValue: 200000, currentValue: 140000, depreciation: 60000, rate: "30%" },
  { category: "Security", originalValue: 50000, currentValue: 42500, depreciation: 7500, rate: "15%" },
]

const maintenanceCosts = [
  { month: "January", preventive: 5200, repair: 3800, inspection: 1200, total: 10200 },
  { month: "February", preventive: 4800, repair: 4200, inspection: 1000, total: 10000 },
  { month: "March", preventive: 5500, repair: 2900, inspection: 1300, total: 9700 },
  { month: "April", preventive: 4900, repair: 3600, inspection: 1100, total: 9600 },
  { month: "May", preventive: 5300, repair: 4100, inspection: 1400, total: 10800 },
  { month: "June", preventive: 5100, repair: 3200, inspection: 1200, total: 9500 },
]

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("monthly")
  const [selectedCategory, setSelectedCategory] = useState("all")

  // Set up the header for this page
  useAdminHeader(getReportsHeaderConfig)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Ready":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Ready</Badge>
      case "Processing":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Processing</Badge>
      case "Failed":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Failed</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="depreciation">Depreciation</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="generated">Generated Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{reports.length}</div>
                <p className="text-xs text-muted-foreground">Generated this month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Depreciation</CardTitle>
                <TrendingDown className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${depreciationData.reduce((sum, item) => sum + item.depreciation, 0).toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">This year</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Maintenance Costs</CardTitle>
                <Wrench className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${maintenanceCosts.reduce((sum, item) => sum + item.total, 0).toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">Last 6 months</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Asset Value</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${depreciationData.reduce((sum, item) => sum + item.currentValue, 0).toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">Current total value</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Reports */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Reports</CardTitle>
              <CardDescription>Latest generated reports and their status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reports.slice(0, 3).map((report) => (
                  <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <p className="font-medium">{report.name}</p>
                      <p className="text-sm text-muted-foreground">{report.description}</p>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{report.type}</Badge>
                        {getStatusBadge(report.status)}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">{report.generatedDate}</span>
                      <Button size="sm" variant="outline">
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="depreciation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Asset Depreciation Analysis</CardTitle>
              <CardDescription>Breakdown of asset depreciation by category</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Category</TableHead>
                    <TableHead>Original Value</TableHead>
                    <TableHead>Current Value</TableHead>
                    <TableHead>Depreciation</TableHead>
                    <TableHead>Depreciation Rate</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {depreciationData.map((item) => (
                    <TableRow key={item.category}>
                      <TableCell className="font-medium">{item.category}</TableCell>
                      <TableCell>${item.originalValue.toLocaleString()}</TableCell>
                      <TableCell>${item.currentValue.toLocaleString()}</TableCell>
                      <TableCell className="text-red-600">-${item.depreciation.toLocaleString()}</TableCell>
                      <TableCell>{item.rate}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Maintenance Cost Analysis</CardTitle>
              <CardDescription>Monthly breakdown of maintenance costs by type</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Month</TableHead>
                    <TableHead>Preventive</TableHead>
                    <TableHead>Repair</TableHead>
                    <TableHead>Inspection</TableHead>
                    <TableHead>Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {maintenanceCosts.map((item) => (
                    <TableRow key={item.month}>
                      <TableCell className="font-medium">{item.month}</TableCell>
                      <TableCell>${item.preventive.toLocaleString()}</TableCell>
                      <TableCell>${item.repair.toLocaleString()}</TableCell>
                      <TableCell>${item.inspection.toLocaleString()}</TableCell>
                      <TableCell className="font-medium">${item.total.toLocaleString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="generated" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Generated Reports</CardTitle>
              <CardDescription>All reports generated in the system</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Report ID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Generated Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell className="font-medium">{report.id}</TableCell>
                      <TableCell>{report.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{report.type}</Badge>
                      </TableCell>
                      <TableCell>{report.generatedDate}</TableCell>
                      <TableCell>{getStatusBadge(report.status)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button size="sm" variant="outline">
                            View
                          </Button>
                          <Button size="sm" variant="outline">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
